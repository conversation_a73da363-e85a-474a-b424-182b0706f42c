# TVBox Web - 在線影視播放平台

基於 TVBox Android 應用的完整 Web 版本實現，支持多源影視內容播放和直播功能。

## 🚀 功能特性

### 核心功能
- ✅ **完整的 TVBox 功能移植** - 與 Android 版本功能對等
- ✅ **多源影視播放** - 支持多種數據源格式（XML、JSON、Spider）
- ✅ **直播電視功能** - 支持 M3U/M3U8 播放列表
- ✅ **智能搜索** - 跨多個數據源搜索影片
- ✅ **視頻解析** - 支持多種視頻解析器
- ✅ **播放歷史** - 自動記錄播放進度
- ✅ **收藏功能** - 收藏喜愛的影片
- ✅ **響應式設計** - 支持桌面和移動設備

### 直播功能
- ✅ **M3U 播放列表解析** - 完整支持 M3U/M3U8 格式
- ✅ **GitHub URL 自動轉換** - 自動將 GitHub blob URL 轉換為 raw 格式
- ✅ **頻道分組管理** - 按分組組織直播頻道
- ✅ **EPG 支持** - 電子節目指南支持
- ✅ **多源切換** - 支持多個直播源切換
- ✅ **鍵盤快捷鍵** - 方便的頻道切換操作

### 技術特性
- ✅ **現代化技術棧** - Next.js 14 + React + TypeScript
- ✅ **狀態管理** - Zustand 輕量級狀態管理
- ✅ **視頻播放** - Video.js 專業視頻播放器
- ✅ **UI 組件** - Tailwind CSS + Shadcn/ui
- ✅ **數據持久化** - 本地存儲配置和歷史記錄

## 🛠️ 技術棧

- **前端框架**: Next.js 14 (React 18)
- **開發語言**: TypeScript
- **樣式框架**: Tailwind CSS
- **UI 組件**: Shadcn/ui + Radix UI
- **狀態管理**: Zustand
- **視頻播放**: Video.js
- **HTTP 客戶端**: Axios
- **圖標庫**: Lucide React

## 📦 安裝和運行

### 環境要求
- Node.js 18+
- npm 或 yarn

### 安裝步驟

1. **克隆項目**
   ```bash
   git clone <repository-url>
   cd tvbox-web
   ```

2. **安裝依賴**
   ```bash
   npm install
   ```

3. **啟動開發服務器**
   ```bash
   npm run dev
   ```

4. **訪問應用**
   打開瀏覽器訪問 `http://localhost:3000`

### 構建生產版本
```bash
npm run build
npm start
```

## 🎯 使用指南

### 配置 TVBox
1. 在首頁輸入有效的 TVBox 配置文件 URL
2. 支持 JSON 格式的配置文件
3. 支持 GitHub 鏈接（自動轉換為 raw 格式）

### 使用直播功能
1. 點擊導航欄的「直播」按鈕
2. 輸入 M3U 播放列表 URL，例如：
   ```
   https://github.com/YanG-1989/m3u/blob/main/Gather.m3u
   ```
3. 系統會自動轉換 GitHub URL 並解析播放列表（包含 128 個頻道）
4. 選擇頻道開始觀看

### 鍵盤快捷鍵
- `↑/↓` - 切換頻道
- `L` - 顯示/隱藏頻道列表
- `F` - 全屏切換

## 🌟 特色功能

### 1. GitHub URL 智能轉換
自動識別並轉換 GitHub blob URL 為 raw 格式：
```
輸入: https://github.com/YanG-1989/m3u/blob/main/Gather.m3u
轉換: https://raw.githubusercontent.com/YanG-1989/m3u/main/Gather.m3u
```

### 2. 完整的 M3U 支持
- 標準 M3U/M3U8 格式解析
- EPG 電子節目指南支持
- 頻道分組和 Logo 顯示
- Catchup 回看功能支持

### 3. 響應式設計
- 桌面端：完整功能界面
- 移動端：優化的觸控操作
- 自適應佈局和組件

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

## 📄 許可證

MIT License

## 🙏 致謝

- 感謝 TVBox 項目提供的靈感和參考
- 感謝 YanG-1989 提供的 M3U 播放列表示例（128 個頻道）
- 感謝所有開源項目的貢獻者
