'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Star, Calendar, MapPin } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import type { Movie } from '@/types';
import { cn } from '@/lib/utils';

interface MovieCardProps {
  movie: Movie;
  sourceKey: string;
  className?: string;
  showDetails?: boolean;
  onPlay?: (movie: Movie) => void;
}

const MovieCard: React.FC<MovieCardProps> = ({
  movie,
  sourceKey,
  className,
  showDetails = true,
  onPlay,
}) => {
  const handlePlay = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onPlay?.(movie);
  };

  return (
    <Card className={cn("group overflow-hidden transition-all hover:shadow-lg", className)}>
      <Link href={`/detail/${sourceKey}/${movie.vodId}`}>
        <div className="relative aspect-[3/4] overflow-hidden">
          {/* 海報圖片 */}
          <Image
            src={movie.vodPic || '/placeholder-movie.jpg'}
            alt={movie.vodName}
            fill
            className="object-cover transition-transform group-hover:scale-105"
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder-movie.jpg';
            }}
          />
          
          {/* 懸浮播放按鈕 */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors flex items-center justify-center">
            <Button
              size="icon"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handlePlay}
            >
              <Play className="h-5 w-5" />
            </Button>
          </div>

          {/* 評分標籤 */}
          {movie.vodRemarks && (
            <div className="absolute top-2 right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
              {movie.vodRemarks}
            </div>
          )}

          {/* 年份標籤 */}
          {movie.vodYear && (
            <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
              {movie.vodYear}
            </div>
          )}
        </div>

        <CardContent className="p-3">
          {/* 標題 */}
          <h3 className="font-medium text-sm line-clamp-2 mb-2 group-hover:text-primary transition-colors">
            {movie.vodName}
          </h3>

          {showDetails && (
            <div className="space-y-1 text-xs text-muted-foreground">
              {/* 類型 */}
              {movie.typeName && (
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3" />
                  <span className="line-clamp-1">{movie.typeName}</span>
                </div>
              )}

              {/* 地區 */}
              {movie.vodArea && (
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3" />
                  <span className="line-clamp-1">{movie.vodArea}</span>
                </div>
              )}

              {/* 年份 */}
              {movie.vodYear && (
                <div className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>{movie.vodYear}</span>
                </div>
              )}

              {/* 演員 */}
              {movie.vodActor && (
                <div className="line-clamp-1" title={movie.vodActor}>
                  主演: {movie.vodActor}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  );
};

export default MovieCard;
