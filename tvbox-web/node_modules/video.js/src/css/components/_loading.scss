.vjs-loading-spinner {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.85;

  // Need to fix centered page layouts
  text-align: left;

  border: .6em solid rgba($primary-background-color, $primary-background-transparency);
  // border: 6px solid rgba(43, 51, 63, 0.5);

  box-sizing: border-box;
  background-clip: padding-box;
  width: 5em;
  height: 5em;
  border-radius: 50%;
  visibility: hidden;
}

.vjs-seeking .vjs-loading-spinner,
.vjs-waiting .vjs-loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;

  // add a delay before actual show the spinner
  animation: vjs-spinner-show 0s linear 0.3s forwards;
}

// Hide if an error occurs
.vjs-error .vjs-loading-spinner {
  display: none;
}

.vjs-loading-spinner:before,
.vjs-loading-spinner:after {
  content: "";
  position: absolute;
  box-sizing: inherit;
  width: inherit;
  height: inherit;
  border-radius: inherit;
  // Keep 100% opacity so they don't show through each other
  opacity: 1;
  border: inherit;
  border-color: transparent;
  border-top-color: white;
}

// only animate when showing because it can be processor heavy
.vjs-seeking .vjs-loading-spinner:before,
.vjs-seeking .vjs-loading-spinner:after,
.vjs-waiting .vjs-loading-spinner:before,
.vjs-waiting .vjs-loading-spinner:after {
  animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite, vjs-spinner-fade 1.1s linear infinite;
}

.vjs-seeking .vjs-loading-spinner:before,
.vjs-waiting .vjs-loading-spinner:before {
  border-top-color: rgb(255,255,255);
}

.vjs-seeking .vjs-loading-spinner:after,
.vjs-waiting .vjs-loading-spinner:after {
  border-top-color: rgb(255,255,255);
  animation-delay: 0.44s;
}

@keyframes vjs-spinner-show {
  to {
    visibility: visible;
  }
}

@keyframes vjs-spinner-spin {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes vjs-spinner-fade {
  0% {
    border-top-color: $secondary-background-color;
  }
  20% {
    border-top-color: $secondary-background-color;
  }
  35% {
    border-top-color: white;
  }
  60% {
    border-top-color: $secondary-background-color;
  }
  100% {
    border-top-color: $secondary-background-color;
  }
}
