import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { Movie, SearchResult, SourceBean } from '@/types';

interface SearchState {
  // 搜索狀態
  isSearching: boolean;
  searchKeyword: string;
  searchResults: SearchResult[];
  
  // 搜索歷史
  searchHistory: string[];
  
  // 熱門搜索
  hotSearches: string[];
  
  // 搜索設置
  selectedSources: string[]; // 選中的搜索源
  fastSearchMode: boolean;
  
  // 錯誤狀態
  searchErrors: Record<string, string>; // sourceKey -> error message
  
  // Actions
  setSearchKeyword: (keyword: string) => void;
  setSearching: (searching: boolean) => void;
  setSearchResults: (results: SearchResult[]) => void;
  addSearchResult: (result: SearchResult) => void;
  updateSearchResult: (sourceKey: string, result: Partial<SearchResult>) => void;
  addToSearchHistory: (keyword: string) => void;
  clearSearchHistory: () => void;
  removeFromSearchHistory: (keyword: string) => void;
  setHotSearches: (searches: string[]) => void;
  setSelectedSources: (sources: string[]) => void;
  toggleSource: (sourceKey: string) => void;
  setFastSearchMode: (enabled: boolean) => void;
  setSearchError: (sourceKey: string, error: string) => void;
  clearSearchErrors: () => void;
  clearSearch: () => void;
}

export const useSearchStore = create<SearchState>()(
  persist(
    (set, get) => ({
      // 初始狀態
      isSearching: false,
      searchKeyword: '',
      searchResults: [],
      searchHistory: [],
      hotSearches: [],
      selectedSources: [],
      fastSearchMode: false,
      searchErrors: {},

      // Actions
      setSearchKeyword: (keyword: string) => {
        set({ searchKeyword: keyword });
      },

      setSearching: (searching: boolean) => {
        set({ isSearching: searching });
        if (searching) {
          // 開始搜索時清除之前的錯誤
          set({ searchErrors: {} });
        }
      },

      setSearchResults: (results: SearchResult[]) => {
        set({ searchResults: results });
      },

      addSearchResult: (result: SearchResult) => {
        set(state => {
          const existingIndex = state.searchResults.findIndex(
            r => r.sourceKey === result.sourceKey
          );
          
          if (existingIndex >= 0) {
            // 更新現有結果
            const newResults = [...state.searchResults];
            newResults[existingIndex] = result;
            return { searchResults: newResults };
          } else {
            // 添加新結果
            return { 
              searchResults: [...state.searchResults, result]
            };
          }
        });
      },

      updateSearchResult: (sourceKey: string, update: Partial<SearchResult>) => {
        set(state => ({
          searchResults: state.searchResults.map(result =>
            result.sourceKey === sourceKey
              ? { ...result, ...update }
              : result
          )
        }));
      },

      addToSearchHistory: (keyword: string) => {
        if (!keyword.trim()) return;
        
        set(state => {
          const trimmedKeyword = keyword.trim();
          const newHistory = [
            trimmedKeyword,
            ...state.searchHistory.filter(k => k !== trimmedKeyword)
          ].slice(0, 20); // 保留最近20條搜索記錄
          
          return { searchHistory: newHistory };
        });
      },

      clearSearchHistory: () => {
        set({ searchHistory: [] });
      },

      removeFromSearchHistory: (keyword: string) => {
        set(state => ({
          searchHistory: state.searchHistory.filter(k => k !== keyword)
        }));
      },

      setHotSearches: (searches: string[]) => {
        set({ hotSearches: searches });
      },

      setSelectedSources: (sources: string[]) => {
        set({ selectedSources: sources });
      },

      toggleSource: (sourceKey: string) => {
        set(state => {
          const isSelected = state.selectedSources.includes(sourceKey);
          const newSelectedSources = isSelected
            ? state.selectedSources.filter(key => key !== sourceKey)
            : [...state.selectedSources, sourceKey];
          
          return { selectedSources: newSelectedSources };
        });
      },

      setFastSearchMode: (enabled: boolean) => {
        set({ fastSearchMode: enabled });
      },

      setSearchError: (sourceKey: string, error: string) => {
        set(state => ({
          searchErrors: {
            ...state.searchErrors,
            [sourceKey]: error
          }
        }));
      },

      clearSearchErrors: () => {
        set({ searchErrors: {} });
      },

      clearSearch: () => {
        set({
          isSearching: false,
          searchKeyword: '',
          searchResults: [],
          searchErrors: {},
        });
      },
    }),
    {
      name: 'tvbox-search',
      partialize: (state) => ({
        searchHistory: state.searchHistory,
        selectedSources: state.selectedSources,
        fastSearchMode: state.fastSearchMode,
      }),
    }
  )
);
