import type { LiveChannelGroup, LiveChannelItem } from '@/types';

export interface M3UChannel {
  name: string;
  url: string;
  logo?: string;
  group?: string;
  tvgId?: string;
  tvgName?: string;
  catchup?: string;
  catchupSource?: string;
}

export interface M3UPlaylist {
  channels: M3UChannel[];
  groups: string[];
  epgUrl?: string;
  catchup?: string;
  catchupSource?: string;
}

/**
 * 解析 M3U 播放列表
 */
export class M3UParser {
  /**
   * 解析 M3U 文本內容
   */
  static parse(content: string): M3UPlaylist {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const channels: M3UChannel[] = [];
    const groups = new Set<string>();
    let epgUrl: string | undefined;
    let globalCatchup: string | undefined;
    let globalCatchupSource: string | undefined;

    let currentChannel: Partial<M3UChannel> = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 解析 M3U 頭部信息
      if (line.startsWith('#EXTM3U')) {
        const headerInfo = this.parseM3UHeader(line);
        epgUrl = headerInfo.epgUrl;
        globalCatchup = headerInfo.catchup;
        globalCatchupSource = headerInfo.catchupSource;
        continue;
      }

      // 解析頻道信息
      if (line.startsWith('#EXTINF:')) {
        currentChannel = this.parseExtInf(line);
        continue;
      }

      // 解析 URL
      if (line && !line.startsWith('#') && currentChannel.name) {
        const channel: M3UChannel = {
          name: currentChannel.name,
          url: line,
          logo: currentChannel.logo,
          group: currentChannel.group,
          tvgId: currentChannel.tvgId,
          tvgName: currentChannel.tvgName,
          catchup: currentChannel.catchup || globalCatchup,
          catchupSource: currentChannel.catchupSource || globalCatchupSource,
        };

        channels.push(channel);

        if (channel.group) {
          groups.add(channel.group);
        }

        currentChannel = {};
      }
    }

    return {
      channels,
      groups: Array.from(groups).sort(),
      epgUrl,
      catchup: globalCatchup,
      catchupSource: globalCatchupSource,
    };
  }

  /**
   * 解析 M3U 頭部信息
   */
  private static parseM3UHeader(line: string): {
    epgUrl?: string;
    catchup?: string;
    catchupSource?: string;
  } {
    const result: any = {};

    // 解析 x-tvg-url
    const epgMatch = line.match(/x-tvg-url="([^"]+)"/);
    if (epgMatch) {
      result.epgUrl = epgMatch[1];
    }

    // 解析 catchup
    const catchupMatch = line.match(/catchup="([^"]+)"/);
    if (catchupMatch) {
      result.catchup = catchupMatch[1];
    }

    // 解析 catchup-source
    const catchupSourceMatch = line.match(/catchup-source="([^"]+)"/);
    if (catchupSourceMatch) {
      result.catchupSource = catchupSourceMatch[1];
    }

    return result;
  }

  /**
   * 解析 EXTINF 行
   */
  private static parseExtInf(line: string): Partial<M3UChannel> {
    const channel: Partial<M3UChannel> = {};

    // 提取頻道名稱（最後的部分）
    const nameMatch = line.match(/,(.+)$/);
    if (nameMatch) {
      channel.name = nameMatch[1].trim();
    }

    // 解析各種屬性
    const attributes = [
      { key: 'tvgId', pattern: /tvg-id="([^"]+)"/ },
      { key: 'tvgName', pattern: /tvg-name="([^"]+)"/ },
      { key: 'logo', pattern: /tvg-logo="([^"]+)"/ },
      { key: 'group', pattern: /group-title="([^"]+)"/ },
      { key: 'catchup', pattern: /catchup="([^"]+)"/ },
      { key: 'catchupSource', pattern: /catchup-source="([^"]+)"/ },
    ];

    attributes.forEach(({ key, pattern }) => {
      const match = line.match(pattern);
      if (match) {
        (channel as any)[key] = match[1];
      }
    });

    return channel;
  }

  /**
   * 將 M3U 播放列表轉換為 TVBox 格式的直播頻道組
   */
  static toTVBoxFormat(playlist: M3UPlaylist): LiveChannelGroup[] {
    const groupMap = new Map<string, LiveChannelItem[]>();

    // 按組分類頻道
    playlist.channels.forEach((channel, index) => {
      const groupName = channel.group || '未分類';

      if (!groupMap.has(groupName)) {
        groupMap.set(groupName, []);
      }

      const liveChannel: LiveChannelItem = {
        channelName: channel.name,
        channelUrls: [channel.url],
        channelIndex: index,
        channelLogo: channel.logo,
        channelEpgUrl: playlist.epgUrl,
      };

      groupMap.get(groupName)!.push(liveChannel);
    });

    // 轉換為 LiveChannelGroup 數組
    const groups: LiveChannelGroup[] = [];
    let groupIndex = 0;

    groupMap.forEach((channels, groupName) => {
      groups.push({
        groupName,
        groupIndex: groupIndex++,
        liveChannels: channels.map((channel, channelIndex) => ({
          ...channel,
          channelIndex,
          groupIndex: groupIndex - 1,
        })),
      });
    });

    return groups;
  }

  /**
   * 轉換 GitHub URL 為 raw 格式
   */
  static convertGitHubUrl(url: string): string {
    // 如果是 GitHub blob URL，轉換為 raw URL
    if (url.includes('github.com') && url.includes('/blob/')) {
      return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/');
    }
    return url;
  }

  /**
   * 從 URL 加載並解析 M3U 播放列表
   */
  static async loadFromUrl(url: string): Promise<M3UPlaylist> {
    try {
      // 自動轉換 GitHub URL
      const processedUrl = this.convertGitHubUrl(url);

      console.log('正在加載 M3U 播放列表:', processedUrl);

      const response = await fetch(processedUrl, {
        headers: {
          'User-Agent': 'TVBox/1.0.0 (Web)',
          'Accept': 'text/plain, application/x-mpegurl, */*',
        },
        mode: 'cors',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();

      // 驗證內容是否為有效的 M3U 格式
      if (!this.validate(content)) {
        throw new Error('文件格式不正確，請確保是有效的 M3U 播放列表');
      }

      const playlist = this.parse(content);
      console.log('M3U 播放列表解析成功:', {
        頻道數量: playlist.channels.length,
        分組數量: playlist.groups.length,
        EPG地址: playlist.epgUrl
      });

      return playlist;
    } catch (error) {
      console.error('加載 M3U 播放列表失敗:', error);
      throw new Error(`加載 M3U 播放列表失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  }

  /**
   * 驗證 M3U 內容格式
   */
  static validate(content: string): boolean {
    const lines = content.split('\n').map(line => line.trim());

    // 檢查是否以 #EXTM3U 開頭
    if (!lines[0]?.startsWith('#EXTM3U')) {
      return false;
    }

    // 檢查是否包含有效的頻道信息
    let hasValidChannel = false;
    for (let i = 0; i < lines.length - 1; i++) {
      if (lines[i].startsWith('#EXTINF:') && lines[i + 1] && !lines[i + 1].startsWith('#')) {
        hasValidChannel = true;
        break;
      }
    }

    return hasValidChannel;
  }

  /**
   * 過濾頻道
   */
  static filterChannels(
    playlist: M3UPlaylist,
    filters: {
      group?: string;
      keyword?: string;
      excludeGroups?: string[];
    }
  ): M3UPlaylist {
    let filteredChannels = playlist.channels;

    // 按組過濾
    if (filters.group) {
      filteredChannels = filteredChannels.filter(channel => channel.group === filters.group);
    }

    // 排除特定組
    if (filters.excludeGroups && filters.excludeGroups.length > 0) {
      filteredChannels = filteredChannels.filter(
        channel => !filters.excludeGroups!.includes(channel.group || '')
      );
    }

    // 按關鍵詞過濾
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      filteredChannels = filteredChannels.filter(channel =>
        channel.name.toLowerCase().includes(keyword)
      );
    }

    // 重新計算組列表
    const groups = new Set<string>();
    filteredChannels.forEach(channel => {
      if (channel.group) {
        groups.add(channel.group);
      }
    });

    return {
      ...playlist,
      channels: filteredChannels,
      groups: Array.from(groups).sort(),
    };
  }
}
