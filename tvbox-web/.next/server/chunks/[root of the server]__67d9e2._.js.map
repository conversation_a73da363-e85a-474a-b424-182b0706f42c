{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/app/api/proxy/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const url = searchParams.get('url');\n\n  if (!url) {\n    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });\n  }\n\n  try {\n    // 驗證 URL 格式\n    new URL(url);\n\n    const response = await fetch(url, {\n      headers: {\n        'User-Agent': 'TVBox/1.0.0 (Web)',\n        'Accept': 'application/json, text/plain, */*',\n      },\n      // 設置超時\n      signal: AbortSignal.timeout(30000),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(`代理請求失敗 ${response.status}:`, errorText.substring(0, 200));\n\n      return NextResponse.json(\n        {\n          error: `HTTP ${response.status}: ${response.statusText}`,\n          details: errorText.substring(0, 500),\n          url: url\n        },\n        { status: response.status }\n      );\n    }\n\n    const contentType = response.headers.get('content-type') || '';\n    const data = await response.text();\n\n    // 檢查返回的內容類型\n    if (data.trim().startsWith('<!DOCTYPE') || data.trim().startsWith('<html')) {\n      console.warn('代理返回了 HTML 內容:', data.substring(0, 200));\n\n      return NextResponse.json(\n        {\n          error: 'URL 返回了網頁而不是預期的數據格式',\n          contentType: 'text/html',\n          preview: data.substring(0, 200),\n          url: url\n        },\n        { status: 400 }\n      );\n    }\n\n    // 返回響應，設置適當的 CORS 頭\n    return new NextResponse(data, {\n      status: 200,\n      headers: {\n        'Content-Type': contentType,\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n      },\n    });\n  } catch (error) {\n    console.error('Proxy error:', error);\n\n    if (error instanceof Error) {\n      if (error.name === 'AbortError') {\n        return NextResponse.json({ error: '請求超時' }, { status: 408 });\n      }\n      return NextResponse.json({ error: error.message }, { status: 500 });\n    }\n\n    return NextResponse.json({ error: '未知錯誤' }, { status: 500 });\n  }\n}\n\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;IAE7B,IAAI,CAAC,KAAK;QACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA4B,GAAG;YAAE,QAAQ;QAAI;IACjF;IAEA,IAAI;QACF,YAAY;QACZ,IAAI,IAAI;QAER,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,SAAS;gBACP,cAAc;gBACd,UAAU;YACZ;YACA,OAAO;YACP,QAAQ,YAAY,OAAO,CAAC;QAC9B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC,GAAG;YAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBACxD,SAAS,UAAU,SAAS,CAAC,GAAG;gBAChC,KAAK;YACP,GACA;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAC5D,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,YAAY;QACZ,IAAI,KAAK,IAAI,GAAG,UAAU,CAAC,gBAAgB,KAAK,IAAI,GAAG,UAAU,CAAC,UAAU;YAC1E,QAAQ,IAAI,CAAC,kBAAkB,KAAK,SAAS,CAAC,GAAG;YAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,aAAa;gBACb,SAAS,KAAK,SAAS,CAAC,GAAG;gBAC3B,KAAK;YACP,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;YAC5B,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAE9B,IAAI,iBAAiB,OAAO;YAC1B,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAO,GAAG;oBAAE,QAAQ;gBAAI;YAC5D;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAO,GAAG;YAAE,QAAQ;QAAI;IAC5D;AACF;AAEO,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF"}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}