'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { M3UParser } from '@/lib/m3u-parser';

export default function TestPage() {
  const [url, setUrl] = useState('https://github.com/YanG-1989/m3u/blob/main/Gather.m3u');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testM3UParsing = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('開始測試 M3U 解析...');
      const playlist = await M3UParser.loadFromUrl(url);
      setResult(playlist);
      console.log('M3U 解析成功:', playlist);
    } catch (err: any) {
      setError(err.message);
      console.error('M3U 解析失敗:', err);
    } finally {
      setLoading(false);
    }
  };

  const testProxy = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('開始測試代理...');
      const convertedUrl = M3UParser.convertGitHubUrl(url);
      const proxyUrl = `/api/proxy?url=${encodeURIComponent(convertedUrl)}`;
      
      const response = await fetch(proxyUrl);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const text = await response.text();
      setResult({
        url: convertedUrl,
        proxyUrl,
        contentLength: text.length,
        preview: text.substring(0, 500),
        channelCount: (text.match(/#EXTINF:/g) || []).length
      });
      console.log('代理測試成功');
    } catch (err: any) {
      setError(err.message);
      console.error('代理測試失敗:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>TVBox Web 功能測試</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                測試 URL
              </label>
              <Input
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="輸入 M3U URL..."
              />
            </div>

            <div className="flex space-x-2">
              <Button 
                onClick={testProxy}
                disabled={loading}
                variant="outline"
              >
                {loading ? '測試中...' : '測試代理'}
              </Button>
              <Button 
                onClick={testM3UParsing}
                disabled={loading}
              >
                {loading ? '解析中...' : '測試 M3U 解析'}
              </Button>
            </div>

            {error && (
              <div className="p-4 bg-destructive/10 border border-destructive rounded-lg">
                <div className="text-destructive font-medium">錯誤:</div>
                <div className="text-sm text-destructive/80">{error}</div>
              </div>
            )}

            {result && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">測試結果</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>功能狀態</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="font-medium">✅ 已實現功能</div>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• CORS 代理 API</li>
                  <li>• GitHub URL 自動轉換</li>
                  <li>• M3U 播放列表解析</li>
                  <li>• 頻道分組管理</li>
                  <li>• Video.js 播放器</li>
                  <li>• 響應式設計</li>
                </ul>
              </div>
              <div className="space-y-2">
                <div className="font-medium">🎯 測試項目</div>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• 代理服務器功能</li>
                  <li>• M3U 文件下載</li>
                  <li>• 頻道信息解析</li>
                  <li>• EPG 數據提取</li>
                  <li>• 錯誤處理機制</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
