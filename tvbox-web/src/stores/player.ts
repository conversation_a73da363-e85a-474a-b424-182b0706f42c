import { create } from 'zustand';
import type { VodInfo, PlayInfo, PlayerState, ParseBean } from '@/types';

interface PlayerStore {
  // 當前播放的內容
  currentVod: VodInfo | null;
  currentPlayInfo: PlayInfo | null;
  currentEpisodeIndex: number;
  currentSourceIndex: number;
  
  // 播放器狀態
  playerState: PlayerState;
  
  // 解析相關
  currentParse: ParseBean | null;
  parseHistory: string[]; // 解析歷史記錄
  
  // 播放歷史
  playHistory: Array<{
    vodId: string;
    sourceKey: string;
    playFlag: string;
    episodeIndex: number;
    position: number;
    timestamp: number;
  }>;
  
  // Actions
  setCurrentVod: (vod: VodInfo) => void;
  setCurrentPlayInfo: (playInfo: PlayInfo) => void;
  setCurrentEpisode: (index: number) => void;
  setCurrentSource: (index: number) => void;
  updatePlayerState: (state: Partial<PlayerState>) => void;
  setCurrentParse: (parse: ParseBean | null) => void;
  addToParseHistory: (url: string) => void;
  savePlayProgress: (position: number, duration: number) => void;
  getPlayProgress: (vodId: string, sourceKey: string, playFlag: string, episodeIndex: number) => number;
  clearPlayer: () => void;
}

const initialPlayerState: PlayerState = {
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 1,
  playbackRate: 1,
  isFullscreen: false,
  isLoading: false,
  error: undefined,
};

export const usePlayerStore = create<PlayerStore>((set, get) => ({
  // 初始狀態
  currentVod: null,
  currentPlayInfo: null,
  currentEpisodeIndex: 0,
  currentSourceIndex: 0,
  playerState: initialPlayerState,
  currentParse: null,
  parseHistory: [],
  playHistory: [],

  // Actions
  setCurrentVod: (vod: VodInfo) => {
    set({ 
      currentVod: vod,
      currentEpisodeIndex: vod.playIndex || 0,
      currentSourceIndex: 0,
    });
  },

  setCurrentPlayInfo: (playInfo: PlayInfo) => {
    set({ currentPlayInfo: playInfo });
  },

  setCurrentEpisode: (index: number) => {
    set({ currentEpisodeIndex: index });
    
    // 更新 VodInfo 中的播放索引
    const { currentVod } = get();
    if (currentVod) {
      set({
        currentVod: {
          ...currentVod,
          playIndex: index,
        }
      });
    }
  },

  setCurrentSource: (index: number) => {
    set({ currentSourceIndex: index });
  },

  updatePlayerState: (newState: Partial<PlayerState>) => {
    set(state => ({
      playerState: { ...state.playerState, ...newState }
    }));
  },

  setCurrentParse: (parse: ParseBean | null) => {
    set({ currentParse: parse });
  },

  addToParseHistory: (url: string) => {
    set(state => ({
      parseHistory: [url, ...state.parseHistory.filter(u => u !== url)].slice(0, 10)
    }));
  },

  savePlayProgress: (position: number, duration: number) => {
    const { currentVod, currentEpisodeIndex, currentSourceIndex } = get();
    if (!currentVod || !currentVod.vodPlayFrom) return;

    const playFlag = currentVod.vodPlayFrom[currentSourceIndex];
    const historyItem = {
      vodId: currentVod.id,
      sourceKey: currentVod.sourceKey || '',
      playFlag,
      episodeIndex: currentEpisodeIndex,
      position,
      timestamp: Date.now(),
    };

    set(state => {
      const existingIndex = state.playHistory.findIndex(
        item => 
          item.vodId === historyItem.vodId &&
          item.sourceKey === historyItem.sourceKey &&
          item.playFlag === historyItem.playFlag &&
          item.episodeIndex === historyItem.episodeIndex
      );

      let newHistory;
      if (existingIndex >= 0) {
        // 更新現有記錄
        newHistory = [...state.playHistory];
        newHistory[existingIndex] = historyItem;
      } else {
        // 添加新記錄
        newHistory = [historyItem, ...state.playHistory].slice(0, 100); // 保留最近100條
      }

      return { playHistory: newHistory };
    });
  },

  getPlayProgress: (vodId: string, sourceKey: string, playFlag: string, episodeIndex: number) => {
    const { playHistory } = get();
    const historyItem = playHistory.find(
      item =>
        item.vodId === vodId &&
        item.sourceKey === sourceKey &&
        item.playFlag === playFlag &&
        item.episodeIndex === episodeIndex
    );
    return historyItem?.position || 0;
  },

  clearPlayer: () => {
    set({
      currentVod: null,
      currentPlayInfo: null,
      currentEpisodeIndex: 0,
      currentSourceIndex: 0,
      playerState: initialPlayerState,
      currentParse: null,
    });
  },
}));
