'use client';

import React, { useRef, useEffect, useState } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
// 導入 HLS 支持
import '@videojs/http-streaming';
import { Play, Pause, Volume2, VolumeX, Maximize, Minimize, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePlayerStore } from '@/stores/player';
import { useLiveStore } from '@/stores/live';
import type { PlayInfo } from '@/types';
import { cn } from '@/lib/utils';

interface VideoPlayerProps {
  src?: string;
  poster?: string;
  className?: string;
  autoplay?: boolean;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onEnded?: () => void;
  onError?: (error: string) => void;
  // 添加頻道信息用於失效檢測
  channelInfo?: {
    groupIndex: number;
    channelIndex: number;
    channelName: string;
  };
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  className,
  autoplay = false,
  onTimeUpdate,
  onEnded,
  onError,
  channelInfo,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const { playerState, updatePlayerState } = usePlayerStore();
  const { checkAndRemoveInvalidChannel } = useLiveStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [useNativePlayer, setUseNativePlayer] = useState(false);
  const [isAutoSwitching, setIsAutoSwitching] = useState(false);
  const [currentPlayerMethod, setCurrentPlayerMethod] = useState<'videojs' | 'native' | 'iframe'>('videojs');
  const [availableMethods, setAvailableMethods] = useState<string[]>([]);

  // 自動檢測可用的播放方式
  const detectAvailablePlaybackMethods = async (url: string): Promise<string[]> => {
    const methods: string[] = [];

    // 檢測 Video.js 支持
    try {
      const videoElement = document.createElement('video');
      if (videoElement.canPlayType('application/x-mpegURL') || url.includes('.m3u8')) {
        methods.push('videojs-hls');
      }
      if (videoElement.canPlayType('video/mp4') || url.includes('.mp4')) {
        methods.push('videojs-mp4');
      }
      methods.push('videojs-default');
    } catch (e) {
      console.warn('Video.js 檢測失敗:', e);
    }

    // 檢測原生播放器支持
    try {
      const videoElement = document.createElement('video');
      if (videoElement.canPlayType('application/x-mpegURL') ||
          videoElement.canPlayType('video/mp4') ||
          videoElement.canPlayType('video/webm')) {
        methods.push('native');
      }
    } catch (e) {
      console.warn('原生播放器檢測失敗:', e);
    }

    // 檢測 iframe 嵌入支持（對於某些特殊流）
    if (url.includes('youtube.com') || url.includes('youtu.be') ||
        url.includes('twitch.tv') || url.includes('bilibili.com')) {
      methods.push('iframe');
    }

    console.log('檢測到可用播放方式:', methods);
    return methods;
  };

  // 自動切換到下一個可用的播放方式
  const switchToNextPlaybackMethod = async () => {
    if (isAutoSwitching) return;

    setIsAutoSwitching(true);
    updatePlayerState({ error: undefined, isLoading: true });

    console.log('開始自動切換播放方式...');

    const methods = await detectAvailablePlaybackMethods(src || '');
    setAvailableMethods(methods);

    // 嘗試下一個播放方式
    const currentIndex = methods.indexOf(currentPlayerMethod === 'videojs' ? 'videojs-default' : currentPlayerMethod);
    const nextIndex = (currentIndex + 1) % methods.length;
    const nextMethod = methods[nextIndex];

    console.log(`切換播放方式: ${currentPlayerMethod} -> ${nextMethod}`);

    if (nextMethod.startsWith('videojs')) {
      setCurrentPlayerMethod('videojs');
      setUseNativePlayer(false);
      // 重新初始化 Video.js
      setTimeout(() => {
        if (playerRef.current && src) {
          playerRef.current.src({ src, type: getVideoType(src) });
          playerRef.current.load();
        }
        setIsAutoSwitching(false);
      }, 1000);
    } else if (nextMethod === 'native') {
      setCurrentPlayerMethod('native');
      setUseNativePlayer(true);
      setIsAutoSwitching(false);
    } else if (nextMethod === 'iframe') {
      setCurrentPlayerMethod('iframe');
      setUseNativePlayer(false);
      setIsAutoSwitching(false);
    } else {
      // 如果所有方式都嘗試過了，切換到下一個頻道
      console.log('所有播放方式都失敗，切換到下一個頻道');
      setIsAutoSwitching(false);
      onError?.('所有播放方式都無法播放此頻道');
    }
  };

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current || isInitialized) return;

    const player = videojs(videoRef.current, {
      controls: true,
      responsive: true,
      fluid: true,
      playbackRates: [0.5, 1, 1.25, 1.5, 2],
      poster: poster,
      preload: 'auto',
      // 強制使用軟件解碼
      techOrder: ['html5'],
      html5: {
        vhs: {
          overrideNative: true,
          enableLowInitialPlaylist: true,
          smoothQualityChange: true,
          useBandwidthFromLocalStorage: true,
          // 增加重試次數和超時時間
          xhr: {
            timeout: 30000,
          },
          // 強制軟件解碼
          allowSeeksWithinUnsafeLiveWindow: true,
          // 處理視頻編解碼器問題
          experimentalBufferBasedABR: true,
        },
        // 啟用原生軌道以確保視頻正常顯示
        nativeVideoTracks: true,
        nativeAudioTracks: true,
        nativeTextTracks: false,
      },
      // 添加 CORS 支持
      crossOrigin: 'anonymous',
      // 增加錯誤重試
      retryOnError: true,
      // 確保視頻元素屬性
      muted: false,
      autoplay: false,
    });

    playerRef.current = player;
    setIsInitialized(true);

    // 事件監聽
    player.on('loadstart', () => {
      updatePlayerState({ isLoading: true, error: undefined });
    });

    player.on('canplay', () => {
      updatePlayerState({ isLoading: false });

      // 檢查視頻軌道
      const videoTracks = player.videoTracks();
      const audioTracks = player.audioTracks();

      console.log('視頻軌道數量:', videoTracks ? videoTracks.length : 0);
      console.log('音頻軌道數量:', audioTracks ? audioTracks.length : 0);

      if (videoTracks && videoTracks.length === 0) {
        console.warn('警告: 沒有檢測到視頻軌道，可能只有音頻');
      }

      // 確保視頻元素可見
      const videoElement = player.el().querySelector('video');
      if (videoElement) {
        videoElement.style.display = 'block';
        videoElement.style.width = '100%';
        videoElement.style.height = '100%';
        console.log('視頻元素尺寸:', videoElement.videoWidth, 'x', videoElement.videoHeight);
      }
    });

    player.on('loadedmetadata', () => {
      const videoElement = player.el().querySelector('video');
      if (videoElement) {
        console.log('視頻元數據加載完成:');
        console.log('- 視頻尺寸:', videoElement.videoWidth, 'x', videoElement.videoHeight);
        console.log('- 持續時間:', videoElement.duration);
        console.log('- 是否有視頻軌道:', videoElement.videoWidth > 0 && videoElement.videoHeight > 0);

        // 如果沒有視頻尺寸，可能是純音頻流
        if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
          console.warn('檢測到純音頻流或視頻軌道問題');
          updatePlayerState({ error: '此頻道可能是純音頻流或視頻編碼不支持' });
        }
      }
    });

    player.on('play', () => {
      updatePlayerState({ isPlaying: true });
    });

    player.on('pause', () => {
      updatePlayerState({ isPlaying: false });
    });

    player.on('timeupdate', () => {
      const currentTime = player.currentTime();
      const duration = player.duration();
      updatePlayerState({ currentTime, duration });
      onTimeUpdate?.(currentTime, duration);
    });

    player.on('volumechange', () => {
      updatePlayerState({ volume: player.volume() });
    });

    player.on('ratechange', () => {
      updatePlayerState({ playbackRate: player.playbackRate() });
    });

    player.on('fullscreenchange', () => {
      updatePlayerState({ isFullscreen: player.isFullscreen() });
    });

    player.on('ended', () => {
      updatePlayerState({ isPlaying: false });
      onEnded?.();
    });

    player.on('error', (e: any) => {
      const error = player.error();
      let errorMessage = '播放錯誤';
      let shouldRetry = false;

      if (error) {
        switch (error.code) {
          case 1:
            errorMessage = '播放被中止';
            break;
          case 2:
            errorMessage = '網絡錯誤 - 無法加載視頻流';
            shouldRetry = true;
            break;
          case 3:
            errorMessage = '解碼錯誤 - 視頻格式不支持';
            break;
          case 4:
            errorMessage = '視頻源不可用或已過期';
            shouldRetry = true;
            break;
          default:
            errorMessage = `播放錯誤 (代碼: ${error.code})`;
            shouldRetry = true;
        }

        // 檢查是否是 HLS 相關錯誤
        if (error.message && error.message.includes('HLS')) {
          errorMessage = 'HLS 流加載失敗 - 可能是網絡問題或源已過期';
          shouldRetry = true;
        }

        if (error.message) {
          errorMessage += ` - ${error.message}`;
        }
      }

      console.error('Video.js 播放錯誤:', error);

      // 自動切換播放方式而不是顯示錯誤
      if (retryCount < 1) {
        // 先嘗試重試一次
        console.log(`嘗試自動重試播放... (${retryCount + 1}/1)`);
        setRetryCount(prev => prev + 1);
        setTimeout(() => {
          if (playerRef.current && src) {
            playerRef.current.src({ src, type: getVideoType(src) });
            playerRef.current.load();
          }
        }, 2000);
      } else {
        // 重試失敗，自動切換播放方式
        console.log('Video.js 播放失敗，自動切換播放方式...');
        setTimeout(() => {
          switchToNextPlaybackMethod();
        }, 1000);
      }
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
        setIsInitialized(false);
      }
    };
  }, []);

  // 更新播放源
  useEffect(() => {
    if (!playerRef.current || !src) return;

    const player = playerRef.current;

    // 重置所有狀態
    setRetryCount(0);
    setUseNativePlayer(false);
    setIsAutoSwitching(false);
    setCurrentPlayerMethod('videojs');
    setAvailableMethods([]);
    updatePlayerState({
      error: undefined,
      isLoading: true,
      isPlaying: false
    });

    console.log('切換到新頻道:', src);

    // 檢測可用播放方式
    detectAvailablePlaybackMethods(src).then(methods => {
      setAvailableMethods(methods);
      console.log('新頻道可用播放方式:', methods);
    });

    try {
      // 停止當前播放
      player.pause();

      // 設置新的播放源
      player.src({
        src: src,
        type: getVideoType(src),
      });

      // 重新加載
      player.load();

      if (autoplay) {
        player.ready(() => {
          player.play().catch((error: any) => {
            console.warn('自動播放失敗:', error);
          });
        });
      }
    } catch (error) {
      console.error('設置播放源失敗:', error);
      updatePlayerState({ error: '設置播放源失敗', isLoading: false });
      onError?.('設置播放源失敗');
    }
  }, [src, autoplay]);

  // 獲取視頻類型
  const getVideoType = (url: string): string => {
    if (url.includes('.m3u8')) return 'application/x-mpegURL';
    if (url.includes('.mpd')) return 'application/dash+xml';
    if (url.includes('.mp4')) return 'video/mp4';
    if (url.includes('.webm')) return 'video/webm';
    if (url.includes('.ogg')) return 'video/ogg';
    return 'video/mp4'; // 默認
  };

  // 播放/暫停
  const togglePlay = () => {
    if (!playerRef.current) return;

    if (playerState.isPlaying) {
      playerRef.current.pause();
    } else {
      playerRef.current.play();
    }
  };

  // 靜音/取消靜音
  const toggleMute = () => {
    if (!playerRef.current) return;
    playerRef.current.muted(!playerRef.current.muted());
  };

  // 全屏/退出全屏
  const toggleFullscreen = () => {
    if (!playerRef.current) return;

    if (playerState.isFullscreen) {
      playerRef.current.exitFullscreen();
    } else {
      playerRef.current.requestFullscreen();
    }
  };

  // 重新加載
  const reload = () => {
    if (!playerRef.current || !src) return;

    const currentTime = playerRef.current.currentTime();
    playerRef.current.load();
    playerRef.current.ready(() => {
      playerRef.current.currentTime(currentTime);
    });
  };

  return (
    <div
      ref={containerRef}
      className={cn("relative bg-black rounded-lg overflow-hidden", className)}
    >
      {/* Video.js 播放器 */}
      <div data-vjs-player>
        <video
          ref={videoRef}
          className="video-js vjs-default-skin w-full h-full"
          playsInline
          webkit-playsinline="true"
          x5-playsinline="true"
          x5-video-player-type="h5"
          x5-video-player-fullscreen="true"
          data-setup="{}"
          style={{
            width: '100%',
            height: '100%',
            display: 'block',
            backgroundColor: '#000'
          }}
        />
      </div>

      {/* 自動切換狀態提示 */}
      {(isAutoSwitching || (playerState.error && !isAutoSwitching)) && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <div className="text-lg">正在優化播放方式...</div>
            <div className="text-sm text-gray-400 mt-2">
              {currentPlayerMethod === 'videojs' && '嘗試 Video.js 播放器'}
              {currentPlayerMethod === 'native' && '嘗試原生播放器'}
              {currentPlayerMethod === 'iframe' && '嘗試嵌入播放器'}
            </div>
            {availableMethods.length > 0 && (
              <div className="text-xs text-gray-500 mt-2">
                可用方式: {availableMethods.join(', ')}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 原生 HTML5 播放器 */}
      {useNativePlayer && src && (
        <video
          key={src} // 確保每次 src 變化時重新創建元素
          className="absolute inset-0 w-full h-full"
          controls
          autoPlay={autoplay}
          playsInline
          crossOrigin="anonymous"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            backgroundColor: '#000'
          }}
          onError={(e) => {
            console.error('原生播放器錯誤:', e);
            // 原生播放器失敗，嘗試下一個播放方式或切換頻道
            setTimeout(() => {
              if (availableMethods.length > 1) {
                switchToNextPlaybackMethod();
              } else {
                console.log('所有播放方式都失敗，切換到下一個頻道');
                onError?.('無法播放此頻道，正在切換...');
              }
            }, 1000);
          }}
          onLoadedMetadata={(e) => {
            const video = e.target as HTMLVideoElement;
            console.log('原生播放器元數據:', {
              width: video.videoWidth,
              height: video.videoHeight,
              duration: video.duration
            });
          }}
          onPlay={() => {
            console.log('原生播放器開始播放');
          }}
          onPause={() => {
            console.log('原生播放器暫停');
          }}
        >
          <source src={src} type={getVideoType(src)} />
          您的瀏覽器不支持視頻播放
        </video>
      )}

      {/* 加載指示器 */}
      {playerState.isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}

      {/* 自定義控制欄 (可選) */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 hover:opacity-100 transition-opacity">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-2">
            <Button
              size="icon"
              variant="ghost"
              onClick={togglePlay}
              className="text-white hover:bg-white/20"
            >
              {playerState.isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>

            <Button
              size="icon"
              variant="ghost"
              onClick={toggleMute}
              className="text-white hover:bg-white/20"
            >
              {playerState.volume === 0 ? (
                <VolumeX className="h-5 w-5" />
              ) : (
                <Volume2 className="h-5 w-5" />
              )}
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              size="icon"
              variant="ghost"
              onClick={reload}
              className="text-white hover:bg-white/20"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>

            <Button
              size="icon"
              variant="ghost"
              onClick={toggleFullscreen}
              className="text-white hover:bg-white/20"
            >
              {playerState.isFullscreen ? (
                <Minimize className="h-5 w-5" />
              ) : (
                <Maximize className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
