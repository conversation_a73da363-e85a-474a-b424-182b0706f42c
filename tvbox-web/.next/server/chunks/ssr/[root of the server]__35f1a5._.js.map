{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/stores/config.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport type {\n  ApiConfig,\n  SourceBean,\n  ParseBean,\n  AppSettings,\n  LiveChannelGroup,\n  IJKCode\n} from '@/types';\n\ninterface ConfigState {\n  // 配置數據\n  apiConfig: ApiConfig | null;\n  sources: SourceBean[];\n  parses: ParseBean[];\n  liveChannels: LiveChannelGroup[];\n  ijkCodes: IJKCode[];\n\n  // 當前選中的配置\n  homeSource: SourceBean | null;\n  defaultParse: ParseBean | null;\n\n  // 應用設置\n  settings: AppSettings;\n\n  // 加載狀態\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  setApiConfig: (config: ApiConfig) => void;\n  setHomeSource: (source: SourceBean) => void;\n  setDefaultParse: (parse: ParseBean) => void;\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  loadConfig: (apiUrl: string) => Promise<void>;\n  clearConfig: () => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n}\n\nconst defaultSettings: AppSettings = {\n  apiUrl: '',\n  homeSourceKey: '',\n  defaultParseKey: '',\n  playerType: 1, // 1: IJK\n  livePlayerType: 1,\n  searchView: 0, // 0: 列表\n  homeRec: 0, // 0: 豆瓣熱播\n  fastSearchMode: false,\n  showPreview: true,\n  debugMode: false,\n};\n\nexport const useConfigStore = create<ConfigState>()(\n  persist(\n    (set, get) => ({\n      // 初始狀態\n      apiConfig: null,\n      sources: [],\n      parses: [],\n      liveChannels: [],\n      ijkCodes: [],\n      homeSource: null,\n      defaultParse: null,\n      settings: defaultSettings,\n      isLoading: false,\n      error: null,\n\n      // Actions\n      setApiConfig: (config: ApiConfig) => {\n        set({\n          apiConfig: config,\n          sources: config.sites || [],\n          parses: config.parses || [],\n          liveChannels: config.lives || [],\n          ijkCodes: config.ijkCodes || [],\n        });\n\n        // 設置默認首頁源\n        const { settings } = get();\n        if (config.sites && config.sites.length > 0) {\n          const homeSource = settings.homeSourceKey\n            ? config.sites.find(s => s.key === settings.homeSourceKey)\n            : config.sites.find(s => s.filterable === 1) || config.sites[0];\n\n          if (homeSource) {\n            get().setHomeSource(homeSource);\n          }\n        }\n\n        // 設置默認解析器\n        if (config.parses && config.parses.length > 0) {\n          const defaultParse = settings.defaultParseKey\n            ? config.parses.find(p => p.name === settings.defaultParseKey)\n            : config.parses[0];\n\n          if (defaultParse) {\n            get().setDefaultParse(defaultParse);\n          }\n        }\n      },\n\n      setHomeSource: (source: SourceBean) => {\n        set({ homeSource: source });\n        get().updateSettings({ homeSourceKey: source.key });\n      },\n\n      setDefaultParse: (parse: ParseBean) => {\n        // 清除之前的默認標記\n        const { parses } = get();\n        const updatedParses = parses.map(p => ({ ...p, isDefault: false }));\n\n        // 設置新的默認解析器\n        parse.isDefault = true;\n        const newParses = updatedParses.map(p =>\n          p.name === parse.name ? parse : p\n        );\n\n        set({\n          defaultParse: parse,\n          parses: newParses\n        });\n        get().updateSettings({ defaultParseKey: parse.name });\n      },\n\n      updateSettings: (newSettings: Partial<AppSettings>) => {\n        set(state => ({\n          settings: { ...state.settings, ...newSettings }\n        }));\n      },\n\n      loadConfig: async (apiUrl: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          // 使用代理來避免 CORS 問題\n          const proxyUrl = `/api/proxy?url=${encodeURIComponent(apiUrl)}`;\n          const response = await fetch(proxyUrl, {\n            headers: {\n              'Accept': 'application/json, text/plain, */*'\n            }\n          });\n\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n          }\n\n          const configText = await response.text();\n          let config: ApiConfig;\n\n          try {\n            // 清理可能的 BOM 和空白字符\n            const cleanText = configText.trim().replace(/^\\uFEFF/, '');\n\n            // 檢查是否為空\n            if (!cleanText) {\n              throw new Error('配置文件為空');\n            }\n\n            // 檢查是否返回了 HTML 頁面\n            if (cleanText.startsWith('<!DOCTYPE') || cleanText.startsWith('<html')) {\n              console.error('服務器返回了 HTML 頁面而不是 JSON 配置文件');\n              console.log('返回的內容 (前 200 字符):', cleanText.substring(0, 200));\n              throw new Error('配置 URL 無效 - 服務器返回了網頁而不是配置文件。請檢查 URL 是否正確。');\n            }\n\n            // 檢查是否為 JSON 格式\n            if (!cleanText.startsWith('{') && !cleanText.startsWith('[')) {\n              console.error('配置文件不是 JSON 格式');\n              console.log('文件內容 (前 200 字符):', cleanText.substring(0, 200));\n              throw new Error('配置文件不是 JSON 格式。請確保 URL 指向有效的 TVBox 配置文件。');\n            }\n\n            // 嘗試解析 JSON\n            config = JSON.parse(cleanText);\n          } catch (parseError) {\n            console.error('JSON 解析錯誤:', parseError);\n            console.log('原始配置文本 (前 500 字符):', configText.substring(0, 500));\n\n            // 如果已經是我們自定義的錯誤，直接拋出\n            if (parseError instanceof Error && parseError.message.includes('配置 URL 無效')) {\n              throw parseError;\n            }\n\n            // 提供更詳細的錯誤信息\n            if (parseError instanceof SyntaxError) {\n              if (parseError.message.includes('Unexpected token')) {\n                throw new Error(`配置文件 JSON 格式錯誤。可能的原因：\n1. URL 指向的不是 JSON 文件\n2. 文件內容被截斷或損壞\n3. 服務器返回了錯誤頁面\n\n錯誤詳情: ${parseError.message}`);\n              } else {\n                throw new Error(`配置文件 JSON 格式錯誤: ${parseError.message}`);\n              }\n            } else {\n              throw new Error('配置文件格式錯誤，請檢查 JSON 格式');\n            }\n          }\n\n          // 驗證配置格式 - 更寬鬆的驗證\n          console.log('配置文件結構:', Object.keys(config));\n\n          // 檢查是否有基本的配置結構\n          if (typeof config !== 'object' || config === null) {\n            throw new Error('配置文件不是有效的 JSON 對象');\n          }\n\n          // 處理配置數據 - 提供默認值和容錯處理\n          const processedConfig: ApiConfig = {\n            spider: config.spider || '',\n            wallpaper: config.wallpaper || '',\n            sites: [],\n            lives: [],\n            parses: [],\n            flags: [],\n            ijk: [],\n            ads: [],\n            ...config,\n          };\n\n          // 處理 sites 數據\n          if (config.sites && Array.isArray(config.sites)) {\n            processedConfig.sites = config.sites.map((site: any, index: number) => ({\n              key: site.key || site.name || `site_${index}`,\n              name: site.name || site.key || `未知站點 ${index + 1}`,\n              type: typeof site.type === 'number' ? site.type : 0,\n              api: site.api || '',\n              searchable: site.searchable !== false ? 1 : 0,\n              quickSearch: site.quickSearch !== false ? 1 : 0,\n              filterable: site.filterable !== false ? 1 : 0,\n              ext: site.ext || '',\n              categories: Array.isArray(site.categories) ? site.categories : [],\n            }));\n          } else {\n            console.warn('配置文件中沒有找到有效的 sites 數據');\n            // 不拋出錯誤，允許沒有 sites 的配置\n          }\n\n          // 處理其他可選數據\n          if (config.lives && Array.isArray(config.lives)) {\n            processedConfig.lives = config.lives;\n          }\n\n          if (config.parses && Array.isArray(config.parses)) {\n            processedConfig.parses = config.parses;\n          }\n\n          console.log('處理後的配置:', {\n            sitesCount: processedConfig.sites.length,\n            livesCount: processedConfig.lives.length,\n            parsesCount: processedConfig.parses.length,\n          });\n\n          get().setApiConfig(processedConfig);\n          get().updateSettings({ apiUrl });\n\n          // 如果配置中包含直播源，也加載直播源\n          if (config.lives && config.lives.length > 0) {\n            // 這裡可以觸發直播源的加載\n            console.log('配置中包含直播源:', config.lives.length, '個分組');\n          }\n\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : '加載配置失敗';\n          set({ error: errorMessage });\n          throw error;\n        } finally {\n          set({ isLoading: false });\n        }\n      },\n\n      clearConfig: () => {\n        set({\n          apiConfig: null,\n          sources: [],\n          parses: [],\n          liveChannels: [],\n          ijkCodes: [],\n          homeSource: null,\n          defaultParse: null,\n          error: null,\n        });\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n      },\n    }),\n    {\n      name: 'tvbox-config',\n      partialize: (state) => ({\n        settings: state.settings,\n        // 只持久化設置，配置數據每次重新加載\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAwCA,MAAM,kBAA+B;IACnC,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,gBAAgB;IAChB,YAAY;IACZ,SAAS;IACT,gBAAgB;IAChB,aAAa;IACb,WAAW;AACb;AAEO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACjC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,WAAW;QACX,SAAS,EAAE;QACX,QAAQ,EAAE;QACV,cAAc,EAAE;QAChB,UAAU,EAAE;QACZ,YAAY;QACZ,cAAc;QACd,UAAU;QACV,WAAW;QACX,OAAO;QAEP,UAAU;QACV,cAAc,CAAC;YACb,IAAI;gBACF,WAAW;gBACX,SAAS,OAAO,KAAK,IAAI,EAAE;gBAC3B,QAAQ,OAAO,MAAM,IAAI,EAAE;gBAC3B,cAAc,OAAO,KAAK,IAAI,EAAE;gBAChC,UAAU,OAAO,QAAQ,IAAI,EAAE;YACjC;YAEA,UAAU;YACV,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC3C,MAAM,aAAa,SAAS,aAAa,GACrC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,SAAS,aAAa,IACvD,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,MAAM,OAAO,KAAK,CAAC,EAAE;gBAEjE,IAAI,YAAY;oBACd,MAAM,aAAa,CAAC;gBACtB;YACF;YAEA,UAAU;YACV,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7C,MAAM,eAAe,SAAS,eAAe,GACzC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,eAAe,IAC3D,OAAO,MAAM,CAAC,EAAE;gBAEpB,IAAI,cAAc;oBAChB,MAAM,eAAe,CAAC;gBACxB;YACF;QACF;QAEA,eAAe,CAAC;YACd,IAAI;gBAAE,YAAY;YAAO;YACzB,MAAM,cAAc,CAAC;gBAAE,eAAe,OAAO,GAAG;YAAC;QACnD;QAEA,iBAAiB,CAAC;YAChB,YAAY;YACZ,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA,IAAK,CAAC;oBAAE,GAAG,CAAC;oBAAE,WAAW;gBAAM,CAAC;YAEjE,YAAY;YACZ,MAAM,SAAS,GAAG;YAClB,MAAM,YAAY,cAAc,GAAG,CAAC,CAAA,IAClC,EAAE,IAAI,KAAK,MAAM,IAAI,GAAG,QAAQ;YAGlC,IAAI;gBACF,cAAc;gBACd,QAAQ;YACV;YACA,MAAM,cAAc,CAAC;gBAAE,iBAAiB,MAAM,IAAI;YAAC;QACrD;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,UAAU;wBAAE,GAAG,MAAM,QAAQ;wBAAE,GAAG,WAAW;oBAAC;gBAChD,CAAC;QACH;QAEA,YAAY,OAAO;YACjB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,kBAAkB;gBAClB,MAAM,WAAW,CAAC,eAAe,EAAE,mBAAmB,SAAS;gBAC/D,MAAM,WAAW,MAAM,MAAM,UAAU;oBACrC,SAAS;wBACP,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBACnE;gBAEA,MAAM,aAAa,MAAM,SAAS,IAAI;gBACtC,IAAI;gBAEJ,IAAI;oBACF,kBAAkB;oBAClB,MAAM,YAAY,WAAW,IAAI,GAAG,OAAO,CAAC,WAAW;oBAEvD,SAAS;oBACT,IAAI,CAAC,WAAW;wBACd,MAAM,IAAI,MAAM;oBAClB;oBAEA,kBAAkB;oBAClB,IAAI,UAAU,UAAU,CAAC,gBAAgB,UAAU,UAAU,CAAC,UAAU;wBACtE,QAAQ,KAAK,CAAC;wBACd,QAAQ,GAAG,CAAC,qBAAqB,UAAU,SAAS,CAAC,GAAG;wBACxD,MAAM,IAAI,MAAM;oBAClB;oBAEA,gBAAgB;oBAChB,IAAI,CAAC,UAAU,UAAU,CAAC,QAAQ,CAAC,UAAU,UAAU,CAAC,MAAM;wBAC5D,QAAQ,KAAK,CAAC;wBACd,QAAQ,GAAG,CAAC,oBAAoB,UAAU,SAAS,CAAC,GAAG;wBACvD,MAAM,IAAI,MAAM;oBAClB;oBAEA,YAAY;oBACZ,SAAS,KAAK,KAAK,CAAC;gBACtB,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,cAAc;oBAC5B,QAAQ,GAAG,CAAC,sBAAsB,WAAW,SAAS,CAAC,GAAG;oBAE1D,qBAAqB;oBACrB,IAAI,sBAAsB,SAAS,WAAW,OAAO,CAAC,QAAQ,CAAC,cAAc;wBAC3E,MAAM;oBACR;oBAEA,aAAa;oBACb,IAAI,sBAAsB,aAAa;wBACrC,IAAI,WAAW,OAAO,CAAC,QAAQ,CAAC,qBAAqB;4BACnD,MAAM,IAAI,MAAM,CAAC;;;;;MAK3B,EAAE,WAAW,OAAO,EAAE;wBACd,OAAO;4BACL,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,WAAW,OAAO,EAAE;wBACzD;oBACF,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF;gBAEA,kBAAkB;gBAClB,QAAQ,GAAG,CAAC,WAAW,OAAO,IAAI,CAAC;gBAEnC,eAAe;gBACf,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,sBAAsB;gBACtB,MAAM,kBAA6B;oBACjC,QAAQ,OAAO,MAAM,IAAI;oBACzB,WAAW,OAAO,SAAS,IAAI;oBAC/B,OAAO,EAAE;oBACT,OAAO,EAAE;oBACT,QAAQ,EAAE;oBACV,OAAO,EAAE;oBACT,KAAK,EAAE;oBACP,KAAK,EAAE;oBACP,GAAG,MAAM;gBACX;gBAEA,cAAc;gBACd,IAAI,OAAO,KAAK,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG;oBAC/C,gBAAgB,KAAK,GAAG,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,MAAW,QAAkB,CAAC;4BACtE,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO;4BAC7C,MAAM,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG;4BAClD,MAAM,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAG;4BAClD,KAAK,KAAK,GAAG,IAAI;4BACjB,YAAY,KAAK,UAAU,KAAK,QAAQ,IAAI;4BAC5C,aAAa,KAAK,WAAW,KAAK,QAAQ,IAAI;4BAC9C,YAAY,KAAK,UAAU,KAAK,QAAQ,IAAI;4BAC5C,KAAK,KAAK,GAAG,IAAI;4BACjB,YAAY,MAAM,OAAO,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,EAAE;wBACnE,CAAC;gBACH,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACb,uBAAuB;gBACzB;gBAEA,WAAW;gBACX,IAAI,OAAO,KAAK,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG;oBAC/C,gBAAgB,KAAK,GAAG,OAAO,KAAK;gBACtC;gBAEA,IAAI,OAAO,MAAM,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;oBACjD,gBAAgB,MAAM,GAAG,OAAO,MAAM;gBACxC;gBAEA,QAAQ,GAAG,CAAC,WAAW;oBACrB,YAAY,gBAAgB,KAAK,CAAC,MAAM;oBACxC,YAAY,gBAAgB,KAAK,CAAC,MAAM;oBACxC,aAAa,gBAAgB,MAAM,CAAC,MAAM;gBAC5C;gBAEA,MAAM,YAAY,CAAC;gBACnB,MAAM,cAAc,CAAC;oBAAE;gBAAO;gBAE9B,oBAAoB;gBACpB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG;oBAC3C,eAAe;oBACf,QAAQ,GAAG,CAAC,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE;gBAChD;YAEF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,aAAa;YACX,IAAI;gBACF,WAAW;gBACX,SAAS,EAAE;gBACX,QAAQ,EAAE;gBACV,cAAc,EAAE;gBAChB,UAAU,EAAE;gBACZ,YAAY;gBACZ,cAAc;gBACd,OAAO;YACT;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;QAE1B,CAAC;AACH"}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Search, Settings, Home, Tv, History, Heart } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { useConfigStore } from '@/stores/config';\nimport { cn } from '@/lib/utils';\n\nconst Header: React.FC = () => {\n  const pathname = usePathname();\n  const { homeSource } = useConfigStore();\n\n  const navItems = [\n    { href: '/', label: '首頁', icon: Home },\n    { href: '/search', label: '搜索', icon: Search },\n    { href: '/live', label: '直播', icon: Tv },\n    { href: '/history', label: '歷史', icon: History },\n    { href: '/favorites', label: '收藏', icon: Heart },\n    { href: '/settings', label: '設置', icon: Settings },\n  ];\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between\">\n        {/* Logo */}\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"h-8 w-8 rounded bg-primary flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-sm\">TV</span>\n            </div>\n            <span className=\"font-bold text-xl\">TVBox</span>\n          </Link>\n          \n          {homeSource && (\n            <div className=\"hidden md:flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>當前源:</span>\n              <span className=\"font-medium\">{homeSource.name}</span>\n            </div>\n          )}\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-1\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n            \n            return (\n              <Link key={item.href} href={item.href}>\n                <Button\n                  variant={isActive ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  className={cn(\n                    \"flex items-center space-x-2\",\n                    isActive && \"bg-primary text-primary-foreground\"\n                  )}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{item.label}</span>\n                </Button>\n              </Link>\n            );\n          })}\n        </nav>\n\n        {/* Quick Search */}\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"hidden lg:flex items-center space-x-2\">\n            <Input\n              placeholder=\"快速搜索...\"\n              className=\"w-64\"\n              onKeyDown={(e) => {\n                if (e.key === 'Enter') {\n                  const keyword = (e.target as HTMLInputElement).value.trim();\n                  if (keyword) {\n                    window.location.href = `/search?q=${encodeURIComponent(keyword)}`;\n                  }\n                }\n              }}\n            />\n          </div>\n          \n          <Link href=\"/search\" className=\"lg:hidden\">\n            <Button variant=\"ghost\" size=\"icon\">\n              <Search className=\"h-5 w-5\" />\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div className=\"md:hidden border-t\">\n        <div className=\"container\">\n          <nav className=\"flex items-center justify-around py-2\">\n            {navItems.slice(0, 5).map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link key={item.href} href={item.href}>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className={cn(\n                      \"flex flex-col items-center space-y-1 h-auto py-2 px-3\",\n                      isActive && \"text-primary\"\n                    )}\n                  >\n                    <Icon className=\"h-4 w-4\" />\n                    <span className=\"text-xs\">{item.label}</span>\n                  </Button>\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;;;;AAWA,MAAM,SAAmB;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;IAEpC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAM,MAAM,mMAAA,CAAA,OAAI;QAAC;QACrC;YAAE,MAAM;YAAW,OAAO;YAAM,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC7C;YAAE,MAAM;YAAS,OAAO;YAAM,MAAM,8LAAA,CAAA,KAAE;QAAC;QACvC;YAAE,MAAM;YAAY,OAAO;YAAM,MAAM,wMAAA,CAAA,UAAO;QAAC;QAC/C;YAAE,MAAM;YAAc,OAAO;YAAM,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC/C;YAAE,MAAM;YAAa,OAAO;YAAM,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAClD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;4BAGrC,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDAAe,WAAW,IAAI;;;;;;;;;;;;;;;;;;kCAMpD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,WAAW,YAAY;oCAChC,MAAK;oCACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YAAY;;sDAGd,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAVV,KAAK,IAAI;;;;;wBAcxB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,WAAU;oCACV,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB,MAAM,UAAU,AAAC,EAAE,MAAM,CAAsB,KAAK,CAAC,IAAI;4CACzD,IAAI,SAAS;gDACX,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,UAAU;4CACnE;wCACF;oCACF;;;;;;;;;;;0CAIJ,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;4BACzB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,YAAY;;sDAGd,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAW,KAAK,KAAK;;;;;;;;;;;;+BAV9B,KAAK,IAAI;;;;;wBAcxB;;;;;;;;;;;;;;;;;;;;;;AAMZ;uCAEe"}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}