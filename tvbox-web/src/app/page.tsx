'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Settings, RefreshCw, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import MovieGrid from '@/components/movie/movie-grid';
import { useConfigStore } from '@/stores/config';
import { apiService } from '@/lib/api';
import type { Movie, MovieSort } from '@/types';

export default function Home() {
  const router = useRouter();
  const {
    apiConfig,
    homeSource,
    settings,
    isLoading: configLoading,
    error: configError,
    loadConfig
  } = useConfigStore();

  const [categories, setCategories] = useState<MovieSort['sortData']>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [movies, setMovies] = useState<Movie[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiUrl, setApiUrl] = useState(settings.apiUrl || '');

  // 初始化配置
  useEffect(() => {
    if (!apiConfig && settings.apiUrl) {
      loadConfig(settings.apiUrl).catch(console.error);
    }
  }, [apiConfig, settings.apiUrl, loadConfig]);

  // 加載分類
  useEffect(() => {
    if (!homeSource) return;

    const loadCategories = async () => {
      try {
        const response = await apiService.getCategories(homeSource);
        if (response.success && response.data?.sortData) {
          setCategories(response.data.sortData);
          // 選擇第一個分類
          if (response.data.sortData.length > 0) {
            setSelectedCategory(response.data.sortData[0].id);
          }
        }
      } catch (error) {
        console.error('加載分類失敗:', error);
      }
    };

    loadCategories();
  }, [homeSource]);

  // 加載影片列表
  useEffect(() => {
    if (!homeSource || !selectedCategory) return;

    const loadMovies = async (page: number = 1, append: boolean = false) => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.getMovieList(homeSource, selectedCategory, page);

        if (response.success && response.data) {
          const newMovies = response.data.list || [];
          setMovies(prev => append ? [...prev, ...newMovies] : newMovies);
          setTotalPages(response.data.pagecount || 1);
          setCurrentPage(page);
        } else {
          setError(response.error || '加載失敗');
        }
      } catch (error: any) {
        setError(error.message || '加載失敗');
      } finally {
        setLoading(false);
      }
    };

    loadMovies(1);
  }, [homeSource, selectedCategory]);

  // 加載更多
  const handleLoadMore = () => {
    if (currentPage < totalPages && !loading) {
      const nextPage = currentPage + 1;
      const loadMovies = async () => {
        setLoading(true);
        try {
          const response = await apiService.getMovieList(homeSource!, selectedCategory, nextPage);
          if (response.success && response.data) {
            setMovies(prev => [...prev, ...(response.data!.list || [])]);
            setCurrentPage(nextPage);
          }
        } catch (error) {
          console.error('加載更多失敗:', error);
        } finally {
          setLoading(false);
        }
      };
      loadMovies();
    }
  };

  // 播放影片
  const handleMoviePlay = (movie: Movie) => {
    router.push(`/detail/${homeSource?.key}/${movie.vodId}?autoplay=true`);
  };

  // 加載配置
  const handleLoadConfig = async () => {
    if (!apiUrl.trim()) return;

    try {
      await loadConfig(apiUrl.trim());
    } catch (error) {
      console.error('加載配置失敗:', error);
    }
  };

  // 如果沒有配置，顯示配置頁面
  if (!apiConfig) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-6 w-6" />
                <span>配置 TVBox</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  配置地址
                </label>
                <div className="flex space-x-2">
                  <Input
                    placeholder="請輸入配置文件 URL..."
                    value={apiUrl}
                    onChange={(e) => setApiUrl(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleLoadConfig();
                      }
                    }}
                  />
                  <Button
                    onClick={handleLoadConfig}
                    disabled={configLoading || !apiUrl.trim()}
                  >
                    {configLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      '加載'
                    )}
                  </Button>
                </div>
              </div>

              {configError && (
                <div className="flex items-center space-x-2 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{configError}</span>
                </div>
              )}

              <div className="text-sm text-muted-foreground">
                <p>請輸入有效的 TVBox 配置文件地址，例如：</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>https://example.com/config.json</li>
                  <li>支持 JSON 格式的配置文件</li>
                  <li>支持 GitHub 直鏈（自動轉換 blob 鏈接）</li>
                </ul>

                <div className="mt-4 p-3 bg-muted rounded-lg space-y-2">
                  <p className="font-medium mb-2">快速開始：</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setApiUrl('/test-config.json');
                    }}
                    className="w-full"
                  >
                    使用測試配置
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/live')}
                    className="w-full"
                  >
                    前往直播頁面
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 分類選擇 */}
      {categories.length > 0 && (
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* 錯誤提示 */}
      {error && (
        <div className="mb-6">
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-destructive">
                <AlertCircle className="h-5 w-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 影片網格 */}
      <MovieGrid
        movies={movies}
        sourceKey={homeSource?.key || ''}
        loading={loading}
        onMoviePlay={handleMoviePlay}
        onLoadMore={handleLoadMore}
        hasMore={currentPage < totalPages}
      />
    </div>
  );
}
