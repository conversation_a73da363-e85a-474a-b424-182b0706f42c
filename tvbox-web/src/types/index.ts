// 對應 Android SourceBean
export interface SourceBean {
  key: string;
  name: string;
  api: string;
  type: number; // 0: xml, 1: json, 3: Spider
  searchable: number; // 是否可搜索
  quickSearch: number; // 是否可以快速搜索
  filterable: number; // 是否可以站點選擇
  playerUrl?: string; // 站點解析Url
  ext?: string; // 擴展數據
  jar?: string; // 自定義jar
  categories?: string[]; // 分類&排序
  playerType?: number; // 0: system, 1: ijkplayer, 2: exoplayer, 10: mxplayer
  clickSelector?: string; // 需要點擊播放的嗅探站點selector
  style?: string; // 展示風格
}

// 對應 Android ParseBean
export interface ParseBean {
  name: string;
  url: string;
  ext?: string;
  type: number; // 0: 嗅探, 1: 解析
  isDefault?: boolean;
}

// 對應 Android VodInfo
export interface VodInfo {
  id: string;
  name: string;
  pic: string;
  note?: string;
  year?: string;
  area?: string;
  lang?: string;
  actor?: string;
  director?: string;
  des?: string;
  type?: string;
  playFlag?: string;
  playUrl?: string;
  vodPlayFrom?: string[];
  vodPlayUrl?: string[];
  seriesFlag?: Map<string, string>;
  playIndex?: number;
  reverseSort?: boolean;
  sourceKey?: string;
}

// 對應 Android Movie
export interface Movie {
  vodId: string;
  vodName: string;
  vodPic: string;
  vodRemarks?: string;
  vodYear?: string;
  vodArea?: string;
  vodLang?: string;
  vodActor?: string;
  vodDirector?: string;
  vodContent?: string;
  typeId?: number;
  typeName?: string;
}

// 對應 Android MovieSort
export interface MovieSort {
  sortData: SortData[];
}

export interface SortData {
  id: string;
  name: string;
  filters?: FilterData[];
}

export interface FilterData {
  key: string;
  name: string;
  value: FilterValue[];
}

export interface FilterValue {
  n: string; // name
  v: string; // value
}

// 對應 Android LiveChannelGroup
export interface LiveChannelGroup {
  groupName: string;
  groupIndex: number;
  liveChannels: LiveChannelItem[];
  groupPasswordProtected?: boolean;
  groupPassword?: string;
}

// 對應 Android LiveChannelItem
export interface LiveChannelItem {
  channelName: string;
  channelUrls: string[];
  channelIndex: number;
  channelNum?: number;
  channelLogo?: string;
  channelEpgUrl?: string;
  groupIndex?: number;
}

// 對應 Android IJKCode
export interface IJKCode {
  name: string;
  option: IJKOption[];
}

export interface IJKOption {
  category: number;
  name: string;
  value: number;
}

// API 配置接口
export interface ApiConfig {
  spider?: string;
  wallpaper?: string;
  sites: SourceBean[];
  parses: ParseBean[];
  hosts?: Record<string, string>;
  lives?: LiveChannelGroup[];
  rules?: string[];
  doh?: DohConfig[];
  flags?: string[];
  ijkCodes?: IJKCode[];
}

export interface DohConfig {
  name: string;
  url: string;
}

// 播放相關
export interface PlayInfo {
  url: string;
  headers?: Record<string, string>;
  userAgent?: string;
  referer?: string;
  parse?: boolean;
  jx?: number;
}

// 搜索結果
export interface SearchResult {
  list: Movie[];
  sourceKey: string;
  sourceName: string;
}

// 應用設置
export interface AppSettings {
  apiUrl: string;
  homeSourceKey: string;
  defaultParseKey: string;
  playerType: number;
  livePlayerType: number;
  searchView: number; // 0: 列表, 1: 縮略圖
  homeRec: number; // 0: 豆瓣熱播, 1: 數據源推薦, 2: 歷史
  fastSearchMode: boolean;
  showPreview: boolean;
  debugMode: boolean;
}

// 歷史記錄
export interface HistoryItem {
  vodId: string;
  vodName: string;
  vodPic: string;
  sourceKey: string;
  playFlag: string;
  playIndex: number;
  position: number;
  duration: number;
  updateTime: number;
}

// 收藏記錄
export interface CollectItem {
  vodId: string;
  vodName: string;
  vodPic: string;
  sourceKey: string;
  updateTime: number;
}

// 播放狀態
export interface PlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  isFullscreen: boolean;
  isLoading: boolean;
  error?: string;
}

// 網絡請求配置
export interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  data?: any;
  timeout?: number;
  userAgent?: string;
  referer?: string;
}

// 響應數據
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
