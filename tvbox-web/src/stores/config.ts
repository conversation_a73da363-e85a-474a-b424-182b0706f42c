import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  ApiConfig,
  SourceBean,
  ParseBean,
  AppSettings,
  LiveChannelGroup,
  IJKCode
} from '@/types';

interface ConfigState {
  // 配置數據
  apiConfig: ApiConfig | null;
  sources: SourceBean[];
  parses: ParseBean[];
  liveChannels: LiveChannelGroup[];
  ijkCodes: IJKCode[];

  // 當前選中的配置
  homeSource: SourceBean | null;
  defaultParse: ParseBean | null;

  // 應用設置
  settings: AppSettings;

  // 加載狀態
  isLoading: boolean;
  error: string | null;

  // Actions
  setApiConfig: (config: ApiConfig) => void;
  setHomeSource: (source: SourceBean) => void;
  setDefaultParse: (parse: ParseBean) => void;
  updateSettings: (settings: Partial<AppSettings>) => void;
  loadConfig: (apiUrl: string) => Promise<void>;
  clearConfig: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const defaultSettings: AppSettings = {
  apiUrl: '',
  homeSourceKey: '',
  defaultParseKey: '',
  playerType: 1, // 1: IJK
  livePlayerType: 1,
  searchView: 0, // 0: 列表
  homeRec: 0, // 0: 豆瓣熱播
  fastSearchMode: false,
  showPreview: true,
  debugMode: false,
};

export const useConfigStore = create<ConfigState>()(
  persist(
    (set, get) => ({
      // 初始狀態
      apiConfig: null,
      sources: [],
      parses: [],
      liveChannels: [],
      ijkCodes: [],
      homeSource: null,
      defaultParse: null,
      settings: defaultSettings,
      isLoading: false,
      error: null,

      // Actions
      setApiConfig: (config: ApiConfig) => {
        set({
          apiConfig: config,
          sources: config.sites || [],
          parses: config.parses || [],
          liveChannels: config.lives || [],
          ijkCodes: config.ijkCodes || [],
        });

        // 設置默認首頁源
        const { settings } = get();
        if (config.sites && config.sites.length > 0) {
          const homeSource = settings.homeSourceKey
            ? config.sites.find(s => s.key === settings.homeSourceKey)
            : config.sites.find(s => s.filterable === 1) || config.sites[0];

          if (homeSource) {
            get().setHomeSource(homeSource);
          }
        }

        // 設置默認解析器
        if (config.parses && config.parses.length > 0) {
          const defaultParse = settings.defaultParseKey
            ? config.parses.find(p => p.name === settings.defaultParseKey)
            : config.parses[0];

          if (defaultParse) {
            get().setDefaultParse(defaultParse);
          }
        }
      },

      setHomeSource: (source: SourceBean) => {
        set({ homeSource: source });
        get().updateSettings({ homeSourceKey: source.key });
      },

      setDefaultParse: (parse: ParseBean) => {
        // 清除之前的默認標記
        const { parses } = get();
        const updatedParses = parses.map(p => ({ ...p, isDefault: false }));

        // 設置新的默認解析器
        parse.isDefault = true;
        const newParses = updatedParses.map(p =>
          p.name === parse.name ? parse : p
        );

        set({
          defaultParse: parse,
          parses: newParses
        });
        get().updateSettings({ defaultParseKey: parse.name });
      },

      updateSettings: (newSettings: Partial<AppSettings>) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings }
        }));
      },

      loadConfig: async (apiUrl: string) => {
        set({ isLoading: true, error: null });

        try {
          // 處理 GitHub URL 轉換
          let processedUrl = apiUrl.trim();

          // 自動轉換 GitHub blob URL 為 raw URL
          if (processedUrl.includes('github.com') && processedUrl.includes('/blob/')) {
            const originalUrl = processedUrl;
            processedUrl = processedUrl
              .replace('github.com', 'raw.githubusercontent.com')
              .replace('/blob/', '/');
            console.log('GitHub URL 自動轉換:', originalUrl, '->', processedUrl);
          }

          // 使用代理來避免 CORS 問題
          const proxyUrl = `/api/proxy?url=${encodeURIComponent(processedUrl)}`;
          const response = await fetch(proxyUrl, {
            headers: {
              'Accept': 'application/json, text/plain, */*'
            }
          });

          if (!response.ok) {
            // 嘗試獲取詳細錯誤信息
            let errorDetails = '';
            try {
              const errorResponse = await response.json();
              if (errorResponse.error) {
                errorDetails = errorResponse.error;

                // 如果是代理檢測到的 HTML 內容錯誤
                if (errorResponse.contentType === 'text/html') {
                  throw new Error(`配置 URL 返回了網頁而不是 JSON 文件。請檢查 URL 是否正確指向配置文件。\n\n預覽內容: ${errorResponse.preview || ''}`);
                }

                // 如果有詳細錯誤信息
                if (errorResponse.details) {
                  throw new Error(`${errorDetails}\n\n詳細信息: ${errorResponse.details}`);
                }

                throw new Error(errorDetails);
              }
            } catch (parseError) {
              // 如果無法解析錯誤響應，使用默認錯誤
            }

            throw new Error(`HTTP ${response.status}: ${response.statusText}${errorDetails ? ` - ${errorDetails}` : ''}`);
          }

          const configText = await response.text();
          let config: ApiConfig;

          try {
            // 清理可能的 BOM 和空白字符
            const cleanText = configText.trim().replace(/^\uFEFF/, '');

            // 檢查是否為空
            if (!cleanText) {
              throw new Error('配置文件為空');
            }

            // 檢查是否返回了 HTML 頁面
            if (cleanText.startsWith('<!DOCTYPE') || cleanText.startsWith('<html')) {
              console.error('服務器返回了 HTML 頁面而不是 JSON 配置文件');
              console.log('返回的內容 (前 200 字符):', cleanText.substring(0, 200));
              throw new Error('配置 URL 無效 - 服務器返回了網頁而不是配置文件。請檢查 URL 是否正確。');
            }

            // 檢查是否為 JSON 格式
            if (!cleanText.startsWith('{') && !cleanText.startsWith('[')) {
              console.error('配置文件不是 JSON 格式');
              console.log('文件內容 (前 200 字符):', cleanText.substring(0, 200));
              throw new Error('配置文件不是 JSON 格式。請確保 URL 指向有效的 TVBox 配置文件。');
            }

            // 嘗試解析 JSON
            config = JSON.parse(cleanText);
          } catch (parseError) {
            console.error('JSON 解析錯誤:', parseError);
            console.log('原始配置文本 (前 500 字符):', configText.substring(0, 500));

            // 如果已經是我們自定義的錯誤，直接拋出
            if (parseError instanceof Error && parseError.message.includes('配置 URL 無效')) {
              throw parseError;
            }

            // 提供更詳細的錯誤信息
            if (parseError instanceof SyntaxError) {
              if (parseError.message.includes('Unexpected token')) {
                throw new Error(`配置文件 JSON 格式錯誤。可能的原因：
1. URL 指向的不是 JSON 文件
2. 文件內容被截斷或損壞
3. 服務器返回了錯誤頁面

錯誤詳情: ${parseError.message}`);
              } else {
                throw new Error(`配置文件 JSON 格式錯誤: ${parseError.message}`);
              }
            } else {
              throw new Error('配置文件格式錯誤，請檢查 JSON 格式');
            }
          }

          // 驗證配置格式 - 更寬鬆的驗證
          console.log('配置文件結構:', Object.keys(config));

          // 檢查是否有基本的配置結構
          if (typeof config !== 'object' || config === null) {
            throw new Error('配置文件不是有效的 JSON 對象');
          }

          // 處理配置數據 - 提供默認值和容錯處理
          const processedConfig: ApiConfig = {
            spider: config.spider || '',
            wallpaper: config.wallpaper || '',
            sites: [],
            lives: [],
            parses: [],
            flags: [],
            ijk: [],
            ads: [],
            ...config,
          };

          // 處理 sites 數據
          if (config.sites && Array.isArray(config.sites)) {
            processedConfig.sites = config.sites.map((site: any, index: number) => ({
              key: site.key || site.name || `site_${index}`,
              name: site.name || site.key || `未知站點 ${index + 1}`,
              type: typeof site.type === 'number' ? site.type : 0,
              api: site.api || '',
              searchable: site.searchable !== false ? 1 : 0,
              quickSearch: site.quickSearch !== false ? 1 : 0,
              filterable: site.filterable !== false ? 1 : 0,
              ext: site.ext || '',
              categories: Array.isArray(site.categories) ? site.categories : [],
            }));
          } else {
            console.warn('配置文件中沒有找到有效的 sites 數據');
            // 不拋出錯誤，允許沒有 sites 的配置
          }

          // 處理其他可選數據
          if (config.lives && Array.isArray(config.lives)) {
            processedConfig.lives = config.lives;
          }

          if (config.parses && Array.isArray(config.parses)) {
            processedConfig.parses = config.parses;
          }

          console.log('處理後的配置:', {
            sitesCount: processedConfig.sites.length,
            livesCount: processedConfig.lives.length,
            parsesCount: processedConfig.parses.length,
          });

          get().setApiConfig(processedConfig);
          get().updateSettings({ apiUrl });

          // 如果配置中包含直播源，也加載直播源
          if (config.lives && config.lives.length > 0) {
            // 這裡可以觸發直播源的加載
            console.log('配置中包含直播源:', config.lives.length, '個分組');
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '加載配置失敗';
          set({ error: errorMessage });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      clearConfig: () => {
        set({
          apiConfig: null,
          sources: [],
          parses: [],
          liveChannels: [],
          ijkCodes: [],
          homeSource: null,
          defaultParse: null,
          error: null,
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },
    }),
    {
      name: 'tvbox-config',
      partialize: (state) => ({
        settings: state.settings,
        // 只持久化設置，配置數據每次重新加載
      }),
    }
  )
);
