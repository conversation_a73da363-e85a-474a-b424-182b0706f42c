'use client';

import React, { useEffect, useState } from 'react';
import { Plus, RefreshCw, AlertCircle, Settings, List, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import VideoPlayer from '@/components/player/video-player';
import ChannelList from '@/components/live/channel-list';
import { useLiveStore } from '@/stores/live';
import { cn } from '@/lib/utils';

export default function LivePage() {
  const {
    liveGroups,
    currentChannel,
    currentLiveSourceUrl,
    liveSourceUrls,
    isLoading,
    error,
    loadLiveSource,
    nextChannel,
    previousChannel,
    clearLiveData,
  } = useLiveStore();

  const [newSourceUrl, setNewSourceUrl] = useState('');
  const [showChannelList, setShowChannelList] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 預設的 M3U 源地址
  const defaultM3UUrl = 'https://raw.githubusercontent.com/YanG-1989/m3u/main/Gather.m3u';

  // GitHub URL 轉換函數
  const convertGitHubUrl = (url: string): string => {
    // 如果是 GitHub blob URL，轉換為 raw URL
    if (url.includes('github.com') && url.includes('/blob/')) {
      return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/');
    }
    return url;
  };

  // 初始化時加載默認源
  useEffect(() => {
    if (liveGroups.length === 0 && !currentLiveSourceUrl) {
      handleLoadSource(defaultM3UUrl);
    }
  }, []);

  // 鍵盤快捷鍵
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return;

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          previousChannel();
          break;
        case 'ArrowDown':
          e.preventDefault();
          nextChannel();
          break;
        case 'l':
        case 'L':
          setShowChannelList(!showChannelList);
          break;
        case 'f':
        case 'F':
          setIsFullscreen(!isFullscreen);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [showChannelList, isFullscreen, nextChannel, previousChannel]);

  const handleLoadSource = async (url: string) => {
    try {
      // 轉換 GitHub URL
      const convertedUrl = convertGitHubUrl(url);
      await loadLiveSource(convertedUrl);
      setNewSourceUrl('');
    } catch (error) {
      console.error('加載直播源失敗:', error);
    }
  };

  const handleAddSource = () => {
    if (newSourceUrl.trim()) {
      handleLoadSource(newSourceUrl.trim());
    }
  };

  // 如果沒有加載任何源，顯示添加源界面
  if (liveGroups.length === 0 && !isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Monitor className="h-6 w-6" />
                <span>添加直播源</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  M3U 播放列表地址
                </label>
                <div className="flex space-x-2">
                  <Input
                    placeholder="請輸入 M3U 播放列表 URL..."
                    value={newSourceUrl}
                    onChange={(e) => setNewSourceUrl(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAddSource();
                      }
                    }}
                  />
                  <Button
                    onClick={handleAddSource}
                    disabled={isLoading || !newSourceUrl.trim()}
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* 快速添加默認源 */}
              <div>
                <Button
                  variant="outline"
                  onClick={() => handleLoadSource(defaultM3UUrl)}
                  disabled={isLoading}
                  className="w-full"
                >
                  加載默認直播源
                </Button>
              </div>

              {/* 歷史源 */}
              {liveSourceUrls.length > 0 && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    歷史源
                  </label>
                  <div className="space-y-2">
                    {liveSourceUrls.map((url, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleLoadSource(url)}
                          disabled={isLoading}
                          className="flex-1 justify-start truncate"
                        >
                          {url}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {error && (
                <div className="flex items-center space-x-2 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              <div className="text-sm text-muted-foreground">
                <p>支持標準的 M3U/M3U8 播放列表格式</p>
                <p>示例: https://example.com/playlist.m3u</p>
                <p>支持 GitHub 鏈接（會自動轉換為 raw 格式）</p>

                <div className="mt-3 p-3 bg-muted rounded-lg">
                  <p className="font-medium mb-2">快速測試：</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setNewSourceUrl('https://github.com/YanG-1989/m3u/blob/main/Gather.m3u')}
                    className="w-full text-xs"
                  >
                    使用示例 GitHub M3U 源
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* 頂部工具欄 */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-semibold">直播電視</h1>
              {currentChannel && (
                <div className="text-sm text-muted-foreground">
                  正在播放: {currentChannel.channelName}
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowChannelList(!showChannelList)}
              >
                <List className="h-4 w-4 mr-2" />
                {showChannelList ? '隱藏' : '顯示'}頻道列表
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setNewSourceUrl('');
                  // 這裡可以打開添加源的對話框
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                添加源
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={clearLiveData}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主內容區域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 播放器區域 */}
        <div className={cn(
          "flex-1 bg-black relative",
          !showChannelList && "w-full"
        )}>
          {currentChannel ? (
            <VideoPlayer
              src={currentChannel.channelUrls[0]}
              poster={currentChannel.channelLogo}
              autoplay={true}
              className="w-full h-full"
              onError={(error) => {
                console.error('播放錯誤:', error);
                // 可以嘗試切換到下一個源或下一個頻道
              }}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-white">
              <div className="text-center">
                <Monitor className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <div className="text-lg">請選擇頻道</div>
              </div>
            </div>
          )}

          {/* 頻道切換提示 */}
          <div className="absolute top-4 left-4 right-4 pointer-events-none">
            <div className="bg-black/50 text-white px-4 py-2 rounded-lg opacity-0 transition-opacity">
              <div className="text-sm">
                使用 ↑↓ 鍵切換頻道，L 鍵切換頻道列表，F 鍵全屏
              </div>
            </div>
          </div>
        </div>

        {/* 頻道列表側邊欄 */}
        {showChannelList && (
          <div className="w-80 border-l bg-background overflow-hidden flex flex-col">
            <div className="flex-1 overflow-y-auto p-4">
              <ChannelList
                onChannelSelect={(groupIndex, channelIndex) => {
                  // 頻道選擇已經在 ChannelList 內部處理
                }}
              />
            </div>
          </div>
        )}
      </div>

      {/* 加載狀態 */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 flex items-center space-x-3">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>加載直播源中...</span>
          </div>
        </div>
      )}
    </div>
  );
}
