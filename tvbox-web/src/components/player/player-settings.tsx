'use client';

import React, { useState } from 'react';
import { Settings, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { 
  usePlayerConfigStore, 
  PlayerType, 
  RenderType, 
  ScaleType,
  PLAYER_INFO,
  RENDER_INFO,
  SCALE_INFO,
  WEB_SUPPORTED_PLAYERS
} from '@/stores/player-config';

interface PlayerSettingsProps {
  className?: string;
}

const PlayerSettings: React.FC<PlayerSettingsProps> = ({ className }) => {
  const { 
    config, 
    ijkCodecs,
    updateConfig, 
    setPlayerType, 
    setRenderType, 
    setScaleType,
    setIJKCodec,
    resetToDefault,
    getPlayerName,
    getRenderName
  } = usePlayerConfigStore();

  const [isOpen, setIsOpen] = useState(false);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          size="icon"
          variant="ghost"
          className={className}
          title="播放器設置"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            播放器設置
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 播放器類型 */}
          <div className="space-y-2">
            <Label>播放器類型</Label>
            <Select
              value={config.playerType.toString()}
              onValueChange={(value) => setPlayerType(parseInt(value) as PlayerType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {WEB_SUPPORTED_PLAYERS.map((type) => (
                  <SelectItem key={type} value={type.toString()}>
                    {PLAYER_INFO[type]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              當前: {getPlayerName()}
            </p>
          </div>

          {/* 渲染器類型 */}
          <div className="space-y-2">
            <Label>渲染器類型</Label>
            <Select
              value={config.renderType.toString()}
              onValueChange={(value) => setRenderType(parseInt(value) as RenderType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(RENDER_INFO).map(([key, value]) => (
                  <SelectItem key={key} value={key}>
                    {value}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              當前: {getRenderName()}
            </p>
          </div>

          {/* 縮放類型 */}
          <div className="space-y-2">
            <Label>縮放類型</Label>
            <Select
              value={config.scaleType.toString()}
              onValueChange={(value) => setScaleType(parseInt(value) as ScaleType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(SCALE_INFO).map(([key, value]) => (
                  <SelectItem key={key} value={key}>
                    {value}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* IJK 解碼器 */}
          <div className="space-y-2">
            <Label>解碼器</Label>
            <Select
              value={config.ijkCodec}
              onValueChange={setIJKCodec}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ijkCodecs.map((codec) => (
                  <SelectItem key={codec.name} value={codec.name}>
                    {codec.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 開關設置 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="live-mode">直播模式</Label>
              <Switch
                id="live-mode"
                checked={config.isLive}
                onCheckedChange={(checked) => updateConfig({ isLive: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="enable-cache">啟用緩存</Label>
              <Switch
                id="enable-cache"
                checked={config.enableCache}
                onCheckedChange={(checked) => updateConfig({ enableCache: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="hardware-acceleration">硬件加速</Label>
              <Switch
                id="hardware-acceleration"
                checked={config.enableHardwareAcceleration}
                onCheckedChange={(checked) => updateConfig({ enableHardwareAcceleration: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="enable-subtitle">啟用字幕</Label>
              <Switch
                id="enable-subtitle"
                checked={config.enableSubtitle}
                onCheckedChange={(checked) => updateConfig({ enableSubtitle: checked })}
              />
            </div>
          </div>

          {/* 數值設置 */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>緩存時長 (毫秒): {config.maxCachedDuration}</Label>
              <Slider
                value={[config.maxCachedDuration]}
                onValueChange={([value]) => updateConfig({ maxCachedDuration: value })}
                min={1000}
                max={10000}
                step={500}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>超時時間 (毫秒): {config.timeout}</Label>
              <Slider
                value={[config.timeout]}
                onValueChange={([value]) => updateConfig({ timeout: value })}
                min={10000}
                max={120000}
                step={5000}
                className="w-full"
              />
            </div>
          </div>

          {/* 操作按鈕 */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={resetToDefault}
              className="flex-1"
            >
              重置默認
            </Button>
            <Button
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              確定
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PlayerSettings;
