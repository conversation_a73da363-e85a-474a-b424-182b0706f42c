import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { LiveChannelGroup, LiveChannelItem } from '@/types';
import { M3UParser, type M3UPlaylist } from '@/lib/m3u-parser';

interface LiveState {
  // 直播源數據
  liveGroups: LiveChannelGroup[];
  currentPlaylist: M3UPlaylist | null;

  // 當前播放狀態
  currentGroup: LiveChannelGroup | null;
  currentChannel: LiveChannelItem | null;
  currentChannelIndex: number;
  currentGroupIndex: number;

  // 直播源 URL 歷史
  liveSourceUrls: string[];
  currentLiveSourceUrl: string;

  // 過濾和搜索
  selectedGroupName: string | null;
  searchKeyword: string;

  // 加載狀態
  isLoading: boolean;
  error: string | null;

  // EPG 相關
  epgUrl: string | null;
  epgData: Record<string, any> | null;

  // Actions
  loadLiveSource: (url: string) => Promise<void>;
  setCurrentChannel: (groupIndex: number, channelIndex: number) => void;
  nextChannel: () => void;
  previousChannel: () => void;
  setSelectedGroup: (groupName: string | null) => void;
  setSearchKeyword: (keyword: string) => void;
  addLiveSourceUrl: (url: string) => void;
  removeLiveSourceUrl: (url: string) => void;
  clearLiveData: () => void;
  getFilteredGroups: () => LiveChannelGroup[];
  getChannelByIndex: (groupIndex: number, channelIndex: number) => LiveChannelItem | null;
}

export const useLiveStore = create<LiveState>()(
  persist(
    (set, get) => ({
      // 初始狀態
      liveGroups: [],
      currentPlaylist: null,
      currentGroup: null,
      currentChannel: null,
      currentChannelIndex: 0,
      currentGroupIndex: 0,
      liveSourceUrls: [],
      currentLiveSourceUrl: '',
      selectedGroupName: null,
      searchKeyword: '',
      isLoading: false,
      error: null,
      epgUrl: null,
      epgData: null,

      // Actions
      loadLiveSource: async (url: string) => {
        set({ isLoading: true, error: null });

        try {
          const playlist = await M3UParser.loadFromUrl(url);
          const liveGroups = M3UParser.toTVBoxFormat(playlist);

          set({
            currentPlaylist: playlist,
            liveGroups,
            currentLiveSourceUrl: url,
            epgUrl: playlist.epgUrl || null,
            currentGroupIndex: 0,
            currentChannelIndex: 0,
            selectedGroupName: null,
            searchKeyword: '',
          });

          // 設置第一個頻道為當前頻道
          if (liveGroups.length > 0 && liveGroups[0].liveChannels.length > 0) {
            set({
              currentGroup: liveGroups[0],
              currentChannel: liveGroups[0].liveChannels[0],
            });
          }

          // 添加到歷史記錄
          get().addLiveSourceUrl(url);

        } catch (error: any) {
          set({
            error: error.message || '加載直播源失敗',
            currentPlaylist: null,
            liveGroups: [],
          });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      setCurrentChannel: (groupIndex: number, channelIndex: number) => {
        const { liveGroups } = get();

        if (groupIndex >= 0 && groupIndex < liveGroups.length) {
          const group = liveGroups[groupIndex];

          if (channelIndex >= 0 && channelIndex < group.liveChannels.length) {
            const channel = group.liveChannels[channelIndex];

            set({
              currentGroup: group,
              currentChannel: channel,
              currentGroupIndex: groupIndex,
              currentChannelIndex: channelIndex,
            });
          }
        }
      },

      nextChannel: () => {
        const { currentGroupIndex, currentChannelIndex, liveGroups } = get();

        if (liveGroups.length === 0) return;

        const currentGroup = liveGroups[currentGroupIndex];
        let nextGroupIndex = currentGroupIndex;
        let nextChannelIndex = currentChannelIndex + 1;

        // 如果當前組的頻道已經到最後一個，切換到下一組的第一個頻道
        if (nextChannelIndex >= currentGroup.liveChannels.length) {
          nextGroupIndex = (currentGroupIndex + 1) % liveGroups.length;
          nextChannelIndex = 0;
        }

        get().setCurrentChannel(nextGroupIndex, nextChannelIndex);
      },

      previousChannel: () => {
        const { currentGroupIndex, currentChannelIndex, liveGroups } = get();

        if (liveGroups.length === 0) return;

        let prevGroupIndex = currentGroupIndex;
        let prevChannelIndex = currentChannelIndex - 1;

        // 如果當前組的頻道已經到第一個，切換到上一組的最後一個頻道
        if (prevChannelIndex < 0) {
          prevGroupIndex = currentGroupIndex === 0 ? liveGroups.length - 1 : currentGroupIndex - 1;
          prevChannelIndex = liveGroups[prevGroupIndex].liveChannels.length - 1;
        }

        get().setCurrentChannel(prevGroupIndex, prevChannelIndex);
      },

      setSelectedGroup: (groupName: string | null) => {
        set({ selectedGroupName: groupName });
      },

      setSearchKeyword: (keyword: string) => {
        set({ searchKeyword: keyword });
      },

      addLiveSourceUrl: (url: string) => {
        set(state => {
          const newUrls = [url, ...state.liveSourceUrls.filter(u => u !== url)].slice(0, 10);
          return { liveSourceUrls: newUrls };
        });
      },

      removeLiveSourceUrl: (url: string) => {
        set(state => ({
          liveSourceUrls: state.liveSourceUrls.filter(u => u !== url)
        }));
      },

      // 檢測並移除失效的頻道
      checkAndRemoveInvalidChannel: async (groupIndex: number, channelIndex: number) => {
        const state = get();
        if (groupIndex >= state.liveGroups.length || channelIndex >= state.liveGroups[groupIndex].liveChannels.length) {
          return;
        }

        const group = state.liveGroups[groupIndex];
        const channel = group.liveChannels[channelIndex];

        console.log(`檢測頻道有效性: ${channel.channelName}`);

        try {
          // 檢測所有 URL 是否有效
          const validUrls = [];
          for (const url of channel.channelUrls) {
            try {
              // 使用代理來檢測 URL 有效性
              const proxyUrl = `/api/proxy?url=${encodeURIComponent(url)}`;
              const response = await fetch(proxyUrl, {
                method: 'HEAD',
                signal: AbortSignal.timeout(5000)
              });

              if (response.ok) {
                validUrls.push(url);
              } else {
                console.log(`URL 失效 (${response.status}): ${url}`);
              }
            } catch (error) {
              console.log(`URL 失效 (網絡錯誤): ${url}`);
            }
          }

          if (validUrls.length === 0) {
            // 所有 URL 都失效，移除整個頻道
            console.log(`頻道 "${channel.channelName}" 所有源都已失效，自動移除`);

            const updatedGroups = [...state.liveGroups];
            updatedGroups[groupIndex].liveChannels.splice(channelIndex, 1);

            // 如果組內沒有頻道了，移除整個組
            if (updatedGroups[groupIndex].liveChannels.length === 0) {
              console.log(`組 "${group.groupName}" 已無有效頻道，自動移除`);
              updatedGroups.splice(groupIndex, 1);
            }

            set({ liveGroups: updatedGroups });

            // 如果當前播放的就是這個頻道，切換到下一個
            if (state.currentGroupIndex === groupIndex && state.currentChannelIndex === channelIndex) {
              setTimeout(() => get().nextChannel(), 100);
            }

            return true; // 表示頻道已被移除
          } else if (validUrls.length < channel.channelUrls.length) {
            // 部分 URL 失效，只保留有效的
            console.log(`頻道 "${channel.channelName}" 部分源失效，保留 ${validUrls.length} 個有效源`);

            const updatedGroups = [...state.liveGroups];
            updatedGroups[groupIndex].liveChannels[channelIndex] = {
              ...channel,
              channelUrls: validUrls
            };

            set({ liveGroups: updatedGroups });
          }

          return false; // 表示頻道仍然有效
        } catch (error) {
          console.error('檢測頻道有效性時出錯:', error);
          return false;
        }
      },

      clearLiveData: () => {
        set({
          liveGroups: [],
          currentPlaylist: null,
          currentGroup: null,
          currentChannel: null,
          currentChannelIndex: 0,
          currentGroupIndex: 0,
          selectedGroupName: null,
          searchKeyword: '',
          error: null,
          epgUrl: null,
          epgData: null,
        });
      },

      getFilteredGroups: () => {
        const { liveGroups, selectedGroupName, searchKeyword } = get();

        let filteredGroups = liveGroups;

        // 按組名過濾
        if (selectedGroupName) {
          filteredGroups = filteredGroups.filter(group => group.groupName === selectedGroupName);
        }

        // 按關鍵詞過濾頻道
        if (searchKeyword.trim()) {
          const keyword = searchKeyword.toLowerCase();
          filteredGroups = filteredGroups.map(group => ({
            ...group,
            liveChannels: group.liveChannels.filter(channel =>
              channel.channelName.toLowerCase().includes(keyword)
            ),
          })).filter(group => group.liveChannels.length > 0);
        }

        return filteredGroups;
      },

      getChannelByIndex: (groupIndex: number, channelIndex: number) => {
        const { liveGroups } = get();

        if (groupIndex >= 0 && groupIndex < liveGroups.length) {
          const group = liveGroups[groupIndex];
          if (channelIndex >= 0 && channelIndex < group.liveChannels.length) {
            return group.liveChannels[channelIndex];
          }
        }

        return null;
      },
    }),
    {
      name: 'tvbox-live',
      partialize: (state) => ({
        liveSourceUrls: state.liveSourceUrls,
        currentLiveSourceUrl: state.currentLiveSourceUrl,
      }),
    }
  )
);
