import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import type {
  SourceBean,
  Movie,
  VodInfo,
  MovieSort,
  SearchResult,
  PlayInfo,
  ParseBean,
  RequestConfig,
  ApiResponse
} from '@/types';

class ApiService {
  private axiosInstance: AxiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'TVBox/1.0.0 (Web)',
        'Accept': 'application/json, text/plain, */*',
      },
    });

    // 請求攔截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 添加自定義 headers
        if (config.userAgent) {
          config.headers['User-Agent'] = config.userAgent;
        }
        if (config.referer) {
          config.headers['Referer'] = config.referer;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 響應攔截器
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );
  }

  // 通用請求方法
  private async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    try {
      // 使用代理來避免 CORS 問題
      const proxyUrl = `/api/proxy?url=${encodeURIComponent(config.url)}`;

      const response = await fetch(proxyUrl, {
        method: config.method || 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          ...config.headers,
        },
        body: config.data ? JSON.stringify(config.data) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.text();
      let parsedData;

      try {
        parsedData = JSON.parse(data);
      } catch {
        // 如果不是 JSON，返回原始文本
        parsedData = data;
      }

      return {
        success: true,
        data: parsedData,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '請求失敗',
        message: error.response?.data?.message || error.message,
      };
    }
  }

  // 獲取分類列表
  async getCategories(source: SourceBean): Promise<ApiResponse<MovieSort>> {
    const url = this.buildApiUrl(source, 'categories');
    return this.request<MovieSort>({
      url,
      headers: this.getSourceHeaders(source),
    });
  }

  // 獲取影片列表
  async getMovieList(
    source: SourceBean,
    categoryId: string,
    page: number = 1,
    filters?: Record<string, string>
  ): Promise<ApiResponse<{ list: Movie[]; page: number; pagecount: number; total: number }>> {
    const url = this.buildApiUrl(source, 'list', {
      t: categoryId,
      pg: page.toString(),
      ...filters,
    });

    return this.request({
      url,
      headers: this.getSourceHeaders(source),
    });
  }

  // 獲取影片詳情
  async getMovieDetail(source: SourceBean, vodId: string): Promise<ApiResponse<VodInfo>> {
    const url = this.buildApiUrl(source, 'detail', { ids: vodId });

    const response = await this.request<{ list: VodInfo[] }>({
      url,
      headers: this.getSourceHeaders(source),
    });

    if (response.success && response.data?.list?.[0]) {
      return {
        success: true,
        data: {
          ...response.data.list[0],
          sourceKey: source.key,
        },
      };
    }

    return {
      success: false,
      error: '獲取詳情失敗',
    };
  }

  // 搜索影片
  async searchMovies(source: SourceBean, keyword: string, page: number = 1): Promise<ApiResponse<SearchResult>> {
    if (!source.searchable) {
      return {
        success: false,
        error: '該源不支持搜索',
      };
    }

    const url = this.buildApiUrl(source, 'search', {
      wd: keyword,
      pg: page.toString(),
    });

    const response = await this.request<{ list: Movie[] }>({
      url,
      headers: this.getSourceHeaders(source),
    });

    if (response.success) {
      return {
        success: true,
        data: {
          list: response.data?.list || [],
          sourceKey: source.key,
          sourceName: source.name,
        },
      };
    }

    return response;
  }

  // 獲取播放地址
  async getPlayUrl(
    source: SourceBean,
    playFlag: string,
    playUrl: string,
    vipFlags?: string[]
  ): Promise<ApiResponse<PlayInfo>> {
    const url = this.buildApiUrl(source, 'play', {
      flag: playFlag,
      play: playUrl,
    });

    const response = await this.request<{ url: string; parse?: number; header?: Record<string, string> }>({
      url,
      headers: this.getSourceHeaders(source),
    });

    if (response.success && response.data?.url) {
      return {
        success: true,
        data: {
          url: response.data.url,
          headers: response.data.header,
          parse: response.data.parse === 1,
        },
      };
    }

    return {
      success: false,
      error: '獲取播放地址失敗',
    };
  }

  // 解析播放地址
  async parsePlayUrl(parse: ParseBean, url: string): Promise<ApiResponse<PlayInfo>> {
    try {
      let parseUrl = parse.url;

      if (parse.type === 0) {
        // 嗅探類型，直接返回原地址
        return {
          success: true,
          data: {
            url,
            parse: false,
          },
        };
      } else {
        // 解析類型
        if (parseUrl.includes('?')) {
          parseUrl += `&url=${encodeURIComponent(url)}`;
        } else {
          parseUrl += `?url=${encodeURIComponent(url)}`;
        }

        const response = await this.request<{ url: string; header?: Record<string, string> }>({
          url: parseUrl,
        });

        if (response.success && response.data?.url) {
          return {
            success: true,
            data: {
              url: response.data.url,
              headers: response.data.header,
              parse: true,
            },
          };
        }
      }

      return {
        success: false,
        error: '解析失敗',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '解析失敗',
      };
    }
  }

  // 構建 API URL
  private buildApiUrl(source: SourceBean, action: string, params?: Record<string, string>): string {
    let url = source.api;

    // 根據源類型構建不同的 URL
    if (source.type === 0) {
      // XML 類型
      url += `?ac=${action}`;
    } else if (source.type === 1) {
      // JSON 類型
      url += `?ac=${action}`;
    } else if (source.type === 3) {
      // Spider 類型，需要特殊處理
      url += `?ac=${action}`;
    }

    // 添加參數
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url += `&${key}=${encodeURIComponent(value)}`;
      });
    }

    return url;
  }

  // 獲取源的請求頭
  private getSourceHeaders(source: SourceBean): Record<string, string> {
    const headers: Record<string, string> = {};

    if (source.ext) {
      try {
        const ext = JSON.parse(source.ext);
        if (ext.header) {
          Object.assign(headers, ext.header);
        }
      } catch (error) {
        console.warn('解析源擴展配置失敗:', error);
      }
    }

    return headers;
  }
}

export const apiService = new ApiService();
