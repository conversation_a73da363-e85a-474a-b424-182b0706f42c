import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 播放器類型枚舉 (對應 Android PlayerHelper)
export enum PlayerType {
  SYSTEM = 0,      // 系統播放器 (對應 AndroidMediaPlayer)
  IJK = 1,         // IJK播放器 (對應 Video.js with HLS)
  EXO = 2,         // Exo播放器 (對應 原生 HTML5)
  MX = 10,         // MX播放器 (Web 不支持)
  REEX = 11,       // Reex播放器 (Web 不支持)
  KODI = 12,       // Kodi播放器 (Web 不支持)
  REMOTE = 13,     // 附近TVBox (Web 不支持)
  VLC = 14,        // VLC播放器 (Web 不支持)
}

// 渲染器類型枚舉 (對應 Android RenderView)
export enum RenderType {
  TEXTURE = 0,     // TextureView (對應 Video.js)
  SURFACE = 1,     // SurfaceView (對應 原生 HTML5)
}

// 縮放類型枚舉 (對應 Android ScreenScale)
export enum ScaleType {
  DEFAULT = 0,     // 默認
  ORIGINAL = 1,    // 原始尺寸
  MATCH_PARENT = 2, // 填充父容器
  CENTER_CROP = 3, // 居中裁剪
  CENTER_INSIDE = 4, // 居中適應
}

// IJK 解碼器配置 (對應 Android IJKCode)
export interface IJKCodec {
  name: string;
  options: Record<string, string>;
  selected: boolean;
}

// 播放器配置接口
export interface PlayerConfig {
  playerType: PlayerType;
  renderType: RenderType;
  scaleType: ScaleType;
  ijkCodec: string;
  isLive: boolean;
  enableCache: boolean;
  enableHardwareAcceleration: boolean;
  maxCachedDuration: number;
  bufferSize: number;
  timeout: number;
  userAgent: string;
  enableSubtitle: boolean;
  enableAudioFocus: boolean;
  enableOrientation: boolean;
  playOnMobileNetwork: boolean;
}

// 默認 IJK 解碼器配置 (對應 Android 默認配置)
export const DEFAULT_IJK_CODECS: IJKCodec[] = [
  {
    name: '硬解码',
    selected: true,
    options: {
      'player|mediacodec': '1',
      'player|mediacodec-auto-rotate': '1',
      'player|mediacodec-handle-resolution-change': '1',
      'codec|skip_loop_filter': '48',
    }
  },
  {
    name: '软解码',
    selected: false,
    options: {
      'player|mediacodec': '0',
      'codec|skip_loop_filter': '8',
      'format|analyzeduration': '2000000',
    }
  },
  {
    name: '自动',
    selected: false,
    options: {
      'player|mediacodec': '1',
      'player|mediacodec-auto-rotate': '1',
      'format|analyzeduration': '1000000',
    }
  }
];

// 播放器信息映射 (對應 Android getPlayersInfo)
export const PLAYER_INFO: Record<PlayerType, string> = {
  [PlayerType.SYSTEM]: '系統播放器',
  [PlayerType.IJK]: 'Video.js播放器',
  [PlayerType.EXO]: '原生HTML5播放器',
  [PlayerType.MX]: 'MX播放器',
  [PlayerType.REEX]: 'Reex播放器',
  [PlayerType.KODI]: 'Kodi播放器',
  [PlayerType.REMOTE]: '附近TVBox',
  [PlayerType.VLC]: 'VLC播放器',
};

// 渲染器信息映射
export const RENDER_INFO: Record<RenderType, string> = {
  [RenderType.TEXTURE]: 'Video.js渲染',
  [RenderType.SURFACE]: '原生HTML5渲染',
};

// 縮放類型信息映射
export const SCALE_INFO: Record<ScaleType, string> = {
  [ScaleType.DEFAULT]: '默認',
  [ScaleType.ORIGINAL]: '原始尺寸',
  [ScaleType.MATCH_PARENT]: '填充',
  [ScaleType.CENTER_CROP]: '裁剪',
  [ScaleType.CENTER_INSIDE]: '適應',
};

// Web 支持的播放器類型
export const WEB_SUPPORTED_PLAYERS = [
  PlayerType.SYSTEM,
  PlayerType.IJK,
  PlayerType.EXO,
];

// 默認播放器配置
export const DEFAULT_PLAYER_CONFIG: PlayerConfig = {
  playerType: PlayerType.IJK,
  renderType: RenderType.TEXTURE,
  scaleType: ScaleType.DEFAULT,
  ijkCodec: '硬解码',
  isLive: false,
  enableCache: true,
  enableHardwareAcceleration: true,
  maxCachedDuration: 3000,
  bufferSize: 1024 * 1024,
  timeout: 60000,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  enableSubtitle: true,
  enableAudioFocus: true,
  enableOrientation: true,
  playOnMobileNetwork: true,
};

// 播放器配置 Store
interface PlayerConfigStore {
  config: PlayerConfig;
  ijkCodecs: IJKCodec[];
  
  // 更新配置
  updateConfig: (config: Partial<PlayerConfig>) => void;
  
  // 設置播放器類型
  setPlayerType: (type: PlayerType) => void;
  
  // 設置渲染器類型
  setRenderType: (type: RenderType) => void;
  
  // 設置縮放類型
  setScaleType: (type: ScaleType) => void;
  
  // 設置 IJK 解碼器
  setIJKCodec: (name: string) => void;
  
  // 獲取當前 IJK 解碼器
  getCurrentIJKCodec: () => IJKCodec | null;
  
  // 重置為默認配置
  resetToDefault: () => void;
  
  // 獲取播放器名稱
  getPlayerName: (type?: PlayerType) => string;
  
  // 獲取渲染器名稱
  getRenderName: (type?: RenderType) => string;
  
  // 檢查播放器是否支持
  isPlayerSupported: (type: PlayerType) => boolean;
}

export const usePlayerConfigStore = create<PlayerConfigStore>()(
  persist(
    (set, get) => ({
      config: DEFAULT_PLAYER_CONFIG,
      ijkCodecs: DEFAULT_IJK_CODECS,
      
      updateConfig: (newConfig) => {
        set((state) => ({
          config: { ...state.config, ...newConfig }
        }));
      },
      
      setPlayerType: (type) => {
        set((state) => ({
          config: { ...state.config, playerType: type }
        }));
      },
      
      setRenderType: (type) => {
        set((state) => ({
          config: { ...state.config, renderType: type }
        }));
      },
      
      setScaleType: (type) => {
        set((state) => ({
          config: { ...state.config, scaleType: type }
        }));
      },
      
      setIJKCodec: (name) => {
        set((state) => {
          const updatedCodecs = state.ijkCodecs.map(codec => ({
            ...codec,
            selected: codec.name === name
          }));
          
          return {
            config: { ...state.config, ijkCodec: name },
            ijkCodecs: updatedCodecs
          };
        });
      },
      
      getCurrentIJKCodec: () => {
        const { ijkCodecs, config } = get();
        return ijkCodecs.find(codec => codec.name === config.ijkCodec) || ijkCodecs[0];
      },
      
      resetToDefault: () => {
        set({
          config: DEFAULT_PLAYER_CONFIG,
          ijkCodecs: DEFAULT_IJK_CODECS
        });
      },
      
      getPlayerName: (type) => {
        const playerType = type ?? get().config.playerType;
        return PLAYER_INFO[playerType] || '未知播放器';
      },
      
      getRenderName: (type) => {
        const renderType = type ?? get().config.renderType;
        return RENDER_INFO[renderType] || '未知渲染器';
      },
      
      isPlayerSupported: (type) => {
        return WEB_SUPPORTED_PLAYERS.includes(type);
      },
    }),
    {
      name: 'player-config-storage',
      version: 1,
    }
  )
);
