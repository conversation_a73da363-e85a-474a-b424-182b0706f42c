'use client';

import React, { useRef, useEffect, useState } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
// 導入 HLS 支持
import '@videojs/http-streaming';
import { Play, Pause, Volume2, VolumeX, Maximize, Minimize, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePlayerStore } from '@/stores/player';
import { useLiveStore } from '@/stores/live';
import type { PlayInfo } from '@/types';
import { cn } from '@/lib/utils';

interface VideoPlayerProps {
  src?: string;
  poster?: string;
  className?: string;
  autoplay?: boolean;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onEnded?: () => void;
  onError?: (error: string) => void;
  // 添加頻道信息用於失效檢測
  channelInfo?: {
    groupIndex: number;
    channelIndex: number;
    channelName: string;
  };
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  className,
  autoplay = false,
  onTimeUpdate,
  onEnded,
  onError,
  channelInfo,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const { playerState, updatePlayerState } = usePlayerStore();
  const { checkAndRemoveInvalidChannel } = useLiveStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [useNativePlayer, setUseNativePlayer] = useState(false);

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current || isInitialized) return;

    const player = videojs(videoRef.current, {
      controls: true,
      responsive: true,
      fluid: true,
      playbackRates: [0.5, 1, 1.25, 1.5, 2],
      poster: poster,
      preload: 'metadata', // 改為 metadata 減少初始加載
      // 優化技術順序，優先使用 HLS
      techOrder: ['html5'],
      html5: {
        vhs: {
          overrideNative: false, // 允許原生 HLS 支持
          enableLowInitialPlaylist: true,
          smoothQualityChange: true,
          useBandwidthFromLocalStorage: true,
          // 增加重試次數和超時時間
          xhr: {
            timeout: 60000, // 增加超時時間
            beforeRequest: (options: any) => {
              // 添加更多 headers 支持
              options.headers = {
                ...options.headers,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': window.location.origin,
              };
              return options;
            },
          },
          // 強制軟件解碼
          allowSeeksWithinUnsafeLiveWindow: true,
          // 處理視頻編解碼器問題
          experimentalBufferBasedABR: true,
          // 增加錯誤恢復
          handleManifestRedirects: true,
        },
        // 啟用原生軌道以確保視頻正常顯示
        nativeVideoTracks: true,
        nativeAudioTracks: true,
        nativeTextTracks: false,
        // 增加格式支持
        hls: {
          withCredentials: false,
        },
      },
      // 添加 CORS 支持
      crossOrigin: 'anonymous',
      // 增加錯誤重試
      retryOnError: true,
      // 確保視頻元素屬性
      muted: false,
      autoplay: false,
      // 增加源選擇器
      sources: [],
    });

    playerRef.current = player;
    setIsInitialized(true);

    // 事件監聽
    player.on('loadstart', () => {
      updatePlayerState({ isLoading: true, error: undefined });
    });

    player.on('canplay', () => {
      updatePlayerState({ isLoading: false });

      // 檢查視頻和音頻軌道
      const videoTracks = player.videoTracks();
      const audioTracks = player.audioTracks();

      console.log('視頻軌道數量:', videoTracks ? videoTracks.length : 0);
      console.log('音頻軌道數量:', audioTracks ? audioTracks.length : 0);

      // 確保音量設置
      if (player.muted()) {
        console.log('播放器被靜音，恢復音量');
        player.muted(false);
      }

      // 設置默認音量
      if (player.volume() === 0) {
        player.volume(1.0);
        console.log('設置默認音量為 100%');
      }

      if (videoTracks && videoTracks.length === 0) {
        console.warn('警告: 沒有檢測到視頻軌道，可能只有音頻');
      }

      if (audioTracks && audioTracks.length === 0) {
        console.warn('警告: 沒有檢測到音頻軌道');
      }

      // 確保視頻元素可見
      const videoElement = player.el().querySelector('video');
      if (videoElement) {
        videoElement.style.display = 'block';
        videoElement.style.width = '100%';
        videoElement.style.height = '100%';
        console.log('視頻元素尺寸:', videoElement.videoWidth, 'x', videoElement.videoHeight);

        // 檢查音頻屬性
        console.log('音頻屬性:', {
          muted: videoElement.muted,
          volume: videoElement.volume,
          hasAudio: videoElement.mozHasAudio || videoElement.webkitAudioDecodedByteCount > 0
        });
      }
    });

    player.on('loadedmetadata', () => {
      const videoElement = player.el().querySelector('video');
      if (videoElement) {
        console.log('視頻元數據加載完成:');
        console.log('- 視頻尺寸:', videoElement.videoWidth, 'x', videoElement.videoHeight);
        console.log('- 持續時間:', videoElement.duration);
        console.log('- 是否有視頻軌道:', videoElement.videoWidth > 0 && videoElement.videoHeight > 0);

        // 如果沒有視頻尺寸，可能是純音頻流
        if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
          console.warn('檢測到純音頻流或視頻軌道問題');
          updatePlayerState({ error: '此頻道可能是純音頻流或視頻編碼不支持' });
        }
      }
    });

    player.on('play', () => {
      updatePlayerState({ isPlaying: true });
    });

    player.on('pause', () => {
      updatePlayerState({ isPlaying: false });
    });

    player.on('timeupdate', () => {
      const currentTime = player.currentTime();
      const duration = player.duration();
      updatePlayerState({ currentTime, duration });
      onTimeUpdate?.(currentTime, duration);
    });

    player.on('volumechange', () => {
      updatePlayerState({ volume: player.volume() });
    });

    player.on('ratechange', () => {
      updatePlayerState({ playbackRate: player.playbackRate() });
    });

    player.on('fullscreenchange', () => {
      updatePlayerState({ isFullscreen: player.isFullscreen() });
    });

    player.on('ended', () => {
      updatePlayerState({ isPlaying: false });
      onEnded?.();
    });

    player.on('error', (e: any) => {
      const error = player.error();
      let errorMessage = '播放錯誤';
      let shouldRetry = false;
      let shouldSwitchToNative = false;

      if (error) {
        switch (error.code) {
          case 1: // MEDIA_ERR_ABORTED
            errorMessage = '播放被中止';
            break;
          case 2: // MEDIA_ERR_NETWORK
            errorMessage = '網絡錯誤 - 無法加載視頻流';
            shouldRetry = true;
            break;
          case 3: // MEDIA_ERR_DECODE
            errorMessage = '解碼錯誤 - 視頻格式不支持';
            shouldSwitchToNative = true;
            break;
          case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
            errorMessage = '媒體格式不支持或源不可用';
            shouldSwitchToNative = true;
            shouldRetry = true;
            break;
          default:
            errorMessage = `播放錯誤 (代碼: ${error.code})`;
            shouldRetry = true;
        }

        // 檢查是否是 HLS 相關錯誤
        if (error.message && error.message.includes('HLS')) {
          errorMessage = 'HLS 流加載失敗';
          shouldSwitchToNative = true;
          shouldRetry = true;
        }

        // 檢查是否是 CORS 錯誤
        if (error.message && (error.message.includes('CORS') || error.message.includes('cross-origin'))) {
          errorMessage = '跨域訪問錯誤';
          shouldSwitchToNative = true;
        }

        if (error.message) {
          console.error('詳細錯誤信息:', error.message);
        }
      }

      console.error('Video.js 播放錯誤:', error);

      // 對於格式不支持的錯誤，自動切換到原生播放器
      if (shouldSwitchToNative && !useNativePlayer) {
        console.log('Video.js 不支持此格式，自動切換到原生播放器...');
        setUseNativePlayer(true);
        updatePlayerState({ error: undefined, isLoading: true });
        return;
      }

      // 顯示錯誤信息
      updatePlayerState({ error: errorMessage, isLoading: false });
      onError?.(errorMessage);

      // 對於網絡錯誤，嘗試自動重試
      if (shouldRetry && src && !shouldSwitchToNative) {
        if (retryCount < 2) {
          console.log(`嘗試自動重試播放... (${retryCount + 1}/2)`);
          setRetryCount(prev => prev + 1);
          setTimeout(() => {
            if (playerRef.current && src) {
              // 嘗試不同的源格式
              const sources = [
                { src, type: getVideoType(src) },
                { src, type: 'application/x-mpegURL' },
                { src, type: 'video/mp4' },
              ];
              playerRef.current.src(sources);
              playerRef.current.load();
            }
          }, 3000);
        } else if (channelInfo) {
          console.log(`重試失敗，檢測頻道 "${channelInfo.channelName}" 是否失效...`);
          checkAndRemoveInvalidChannel(channelInfo.groupIndex, channelInfo.channelIndex)
            .then((removed) => {
              if (removed) {
                console.log(`頻道 "${channelInfo.channelName}" 已自動移除`);
              }
            })
            .catch((err) => {
              console.error('檢測頻道失效時出錯:', err);
            });
        }
      }
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
        setIsInitialized(false);
      }
    };
  }, []);

  // 更新播放源
  useEffect(() => {
    if (!playerRef.current || !src) return;

    const player = playerRef.current;

    // 重置狀態
    setRetryCount(0);
    setUseNativePlayer(false);
    updatePlayerState({
      error: undefined,
      isLoading: true,
      isPlaying: false
    });

    console.log('切換到新頻道:', src);

    try {
      // 停止當前播放
      player.pause();

      // 準備多種格式的源
      const sources = [
        { src: src, type: getVideoType(src) },
        { src: src, type: 'application/x-mpegURL' }, // HLS
        { src: src, type: 'video/mp4' }, // MP4
        { src: src, type: 'application/vnd.apple.mpegurl' }, // Apple HLS
      ];

      // 設置多個播放源讓播放器自動選擇
      player.src(sources);

      // 重新加載
      player.load();

      if (autoplay) {
        player.ready(() => {
          // 確保音量設置正確
          player.volume(1.0);
          player.muted(false);

          player.play().catch((error: any) => {
            console.warn('自動播放失敗:', error);
            // 如果自動播放失敗，暫時靜音播放，然後恢復音量
            player.muted(true);
            player.play().then(() => {
              // 播放成功後，延遲恢復音量
              setTimeout(() => {
                player.muted(false);
                console.log('已恢復音量');
              }, 1000);
            }).catch((e: any) => {
              console.warn('靜音自動播放也失敗:', e);
            });
          });
        });
      }
    } catch (error) {
      console.error('設置播放源失敗:', error);
      updatePlayerState({ error: '設置播放源失敗', isLoading: false });
      onError?.('設置播放源失敗');
    }
  }, [src, autoplay]);

  // 獲取視頻類型
  const getVideoType = (url: string): string => {
    const urlLower = url.toLowerCase();

    // HLS 格式
    if (urlLower.includes('.m3u8') || urlLower.includes('m3u8')) {
      return 'application/x-mpegURL';
    }

    // DASH 格式
    if (urlLower.includes('.mpd')) {
      return 'application/dash+xml';
    }

    // 常見視頻格式
    if (urlLower.includes('.mp4')) return 'video/mp4';
    if (urlLower.includes('.webm')) return 'video/webm';
    if (urlLower.includes('.ogg')) return 'video/ogg';
    if (urlLower.includes('.avi')) return 'video/x-msvideo';
    if (urlLower.includes('.mov')) return 'video/quicktime';
    if (urlLower.includes('.wmv')) return 'video/x-ms-wmv';
    if (urlLower.includes('.flv')) return 'video/x-flv';
    if (urlLower.includes('.mkv')) return 'video/x-matroska';

    // 流媒體協議
    if (urlLower.startsWith('rtmp://') || urlLower.startsWith('rtmps://')) {
      return 'rtmp/mp4';
    }
    if (urlLower.startsWith('rtsp://')) {
      return 'application/x-rtsp';
    }

    // 默認嘗試 HLS，因為很多直播流都是 HLS
    return 'application/x-mpegURL';
  };

  // 播放/暫停
  const togglePlay = () => {
    if (!playerRef.current) return;

    if (playerState.isPlaying) {
      playerRef.current.pause();
    } else {
      playerRef.current.play();
    }
  };

  // 靜音/取消靜音
  const toggleMute = () => {
    if (!playerRef.current) return;
    playerRef.current.muted(!playerRef.current.muted());
  };

  // 全屏/退出全屏
  const toggleFullscreen = () => {
    if (!playerRef.current) return;

    if (playerState.isFullscreen) {
      playerRef.current.exitFullscreen();
    } else {
      playerRef.current.requestFullscreen();
    }
  };

  // 重新加載
  const reload = () => {
    if (!playerRef.current || !src) return;

    const currentTime = playerRef.current.currentTime();
    playerRef.current.load();
    playerRef.current.ready(() => {
      playerRef.current.currentTime(currentTime);
    });
  };

  // 恢復音頻
  const restoreAudio = () => {
    if (playerRef.current) {
      playerRef.current.muted(false);
      playerRef.current.volume(1.0);
      console.log('手動恢復音頻');
    }

    // 同時處理原生播放器
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.muted = false;
      video.volume = 1.0;
    });

    updatePlayerState({ volume: 1.0 });
  };

  return (
    <div
      ref={containerRef}
      className={cn("relative bg-black rounded-lg overflow-hidden", className)}
    >
      {/* Video.js 播放器 */}
      <div data-vjs-player>
        <video
          ref={videoRef}
          className="video-js vjs-default-skin w-full h-full"
          playsInline
          webkit-playsinline="true"
          x5-playsinline="true"
          x5-video-player-type="h5"
          x5-video-player-fullscreen="true"
          data-setup="{}"
          style={{
            width: '100%',
            height: '100%',
            display: 'block',
            backgroundColor: '#000'
          }}
        />
      </div>

      {/* 錯誤提示 */}
      {playerState.error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80">
          <div className="text-center text-white">
            <div className="text-lg mb-4">{playerState.error}</div>
            <div className="space-x-2">
              <Button onClick={reload} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                重新加載
              </Button>
              <Button onClick={restoreAudio} variant="outline" className="bg-green-600 hover:bg-green-700">
                <Volume2 className="h-4 w-4 mr-2" />
                恢復音頻
              </Button>
              {!useNativePlayer && (
                <Button
                  onClick={() => {
                    setUseNativePlayer(true);
                    updatePlayerState({ error: undefined });
                  }}
                  variant="outline"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  嘗試原生播放器
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 原生 HTML5 播放器 */}
      {useNativePlayer && src && (
        <video
          key={src} // 確保每次 src 變化時重新創建元素
          className="absolute inset-0 w-full h-full"
          controls
          autoPlay={autoplay}
          playsInline
          crossOrigin="anonymous"
          preload="metadata"
          muted={false} // 確保不靜音
          volume={1.0} // 設置音量為最大
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            backgroundColor: '#000'
          }}
          onError={(e) => {
            const video = e.target as HTMLVideoElement;
            const error = video.error;
            let errorMsg = '原生播放器錯誤';

            if (error) {
              switch (error.code) {
                case 1:
                  errorMsg = '播放被中止';
                  break;
                case 2:
                  errorMsg = '網絡錯誤';
                  break;
                case 3:
                  errorMsg = '解碼錯誤';
                  break;
                case 4:
                  errorMsg = '格式不支持';
                  break;
                default:
                  errorMsg = `播放錯誤 (代碼: ${error.code})`;
              }
            }

            console.error('原生播放器錯誤:', error);
            updatePlayerState({ error: errorMsg, isLoading: false });
            onError?.(errorMsg);
          }}
          onLoadedMetadata={(e) => {
            const video = e.target as HTMLVideoElement;
            console.log('原生播放器元數據:', {
              width: video.videoWidth,
              height: video.videoHeight,
              duration: video.duration
            });
            updatePlayerState({ isLoading: false });
          }}
          onLoadStart={() => {
            updatePlayerState({ isLoading: true, error: undefined });
          }}
          onCanPlay={() => {
            updatePlayerState({ isLoading: false });
          }}
          onPlay={() => {
            console.log('原生播放器開始播放');
            updatePlayerState({ isPlaying: true });

            // 確保音頻設置正確
            const video = document.querySelector('video') as HTMLVideoElement;
            if (video) {
              video.muted = false;
              video.volume = 1.0;
              console.log('原生播放器音頻狀態:', {
                muted: video.muted,
                volume: video.volume
              });
            }
          }}
          onPause={() => {
            console.log('原生播放器暫停');
            updatePlayerState({ isPlaying: false });
          }}
          onTimeUpdate={(e) => {
            const video = e.target as HTMLVideoElement;
            updatePlayerState({
              currentTime: video.currentTime,
              duration: video.duration
            });
            onTimeUpdate?.(video.currentTime, video.duration);
          }}
        >
          {/* 提供多種格式的源 */}
          <source src={src} type="application/x-mpegURL" />
          <source src={src} type="application/vnd.apple.mpegurl" />
          <source src={src} type="video/mp4" />
          <source src={src} type={getVideoType(src)} />
          您的瀏覽器不支持視頻播放
        </video>
      )}

      {/* 加載指示器 */}
      {playerState.isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}

      {/* 浮動音頻控制按鈕 */}
      <div className="absolute top-4 right-4 opacity-0 hover:opacity-100 transition-opacity">
        <Button
          size="icon"
          variant="ghost"
          onClick={restoreAudio}
          className="text-white hover:bg-white/20 bg-black/50"
          title="確保音頻開啟"
        >
          <Volume2 className="h-4 w-4" />
        </Button>
      </div>

      {/* 自定義控制欄 (可選) */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 hover:opacity-100 transition-opacity">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-2">
            <Button
              size="icon"
              variant="ghost"
              onClick={togglePlay}
              className="text-white hover:bg-white/20"
            >
              {playerState.isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>

            <Button
              size="icon"
              variant="ghost"
              onClick={toggleMute}
              className="text-white hover:bg-white/20"
            >
              {playerState.volume === 0 ? (
                <VolumeX className="h-5 w-5" />
              ) : (
                <Volume2 className="h-5 w-5" />
              )}
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              size="icon"
              variant="ghost"
              onClick={reload}
              className="text-white hover:bg-white/20"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>

            <Button
              size="icon"
              variant="ghost"
              onClick={toggleFullscreen}
              className="text-white hover:bg-white/20"
            >
              {playerState.isFullscreen ? (
                <Minimize className="h-5 w-5" />
              ) : (
                <Maximize className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
