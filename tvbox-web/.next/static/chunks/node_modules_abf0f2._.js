(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_abf0f2._.js", {

"[project]/node_modules/global/window.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
var win;
if (typeof window !== "undefined") {
    win = window;
} else if (typeof global !== "undefined") {
    win = global;
} else if (typeof self !== "undefined") {
    win = self;
} else {
    win = {};
}
module.exports = win;
}}),
"[project]/node_modules/global/document.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
var topLevel = typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : {};
var minDoc = {};
var doccy;
if (typeof document !== 'undefined') {
    doccy = document;
} else {
    doccy = topLevel['__GLOBAL_DOCUMENT_CACHE@4'];
    if (!doccy) {
        doccy = topLevel['__GLOBAL_DOCUMENT_CACHE@4'] = minDoc;
    }
}
module.exports = doccy;
}}),
"[project]/node_modules/@babel/runtime/helpers/extends.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
function _extends() {
    return module.exports = _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined), module.exports.__esModule = true, module.exports["default"] = module.exports, _extends.apply(null, arguments);
}
module.exports = _extends, module.exports.__esModule = true, module.exports["default"] = module.exports;
}}),
"[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>_extends)
});
function _extends() {
    return _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined), _extends.apply(null, arguments);
}
;
}}),
"[project]/node_modules/is-function/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
module.exports = isFunction;
var toString = Object.prototype.toString;
function isFunction(fn) {
    if (!fn) {
        return false;
    }
    var string = toString.call(fn);
    return string === '[object Function]' || typeof fn === 'function' && string !== '[object RegExp]' || typeof window !== 'undefined' && // IE8 and below
    (fn === window.setTimeout || fn === window.alert || fn === window.confirm || fn === window.prompt);
}
;
}}),
"[project]/node_modules/@videojs/xhr/lib/interceptors.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
function _createForOfIteratorHelperLoose(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++){
        arr2[i] = arr[i];
    }
    return arr2;
}
var InterceptorsStorage = /*#__PURE__*/ function() {
    function InterceptorsStorage() {
        this.typeToInterceptorsMap_ = new Map();
        this.enabled_ = false;
    }
    var _proto = InterceptorsStorage.prototype;
    _proto.getIsEnabled = function getIsEnabled() {
        return this.enabled_;
    };
    _proto.enable = function enable() {
        this.enabled_ = true;
    };
    _proto.disable = function disable() {
        this.enabled_ = false;
    };
    _proto.reset = function reset() {
        this.typeToInterceptorsMap_ = new Map();
        this.enabled_ = false;
    };
    _proto.addInterceptor = function addInterceptor(type, interceptor) {
        if (!this.typeToInterceptorsMap_.has(type)) {
            this.typeToInterceptorsMap_.set(type, new Set());
        }
        var interceptorsSet = this.typeToInterceptorsMap_.get(type);
        if (interceptorsSet.has(interceptor)) {
            // already have this interceptor
            return false;
        }
        interceptorsSet.add(interceptor);
        return true;
    };
    _proto.removeInterceptor = function removeInterceptor(type, interceptor) {
        var interceptorsSet = this.typeToInterceptorsMap_.get(type);
        if (interceptorsSet && interceptorsSet.has(interceptor)) {
            interceptorsSet.delete(interceptor);
            return true;
        }
        return false;
    };
    _proto.clearInterceptorsByType = function clearInterceptorsByType(type) {
        var interceptorsSet = this.typeToInterceptorsMap_.get(type);
        if (!interceptorsSet) {
            return false;
        }
        this.typeToInterceptorsMap_.delete(type);
        this.typeToInterceptorsMap_.set(type, new Set());
        return true;
    };
    _proto.clear = function clear() {
        if (!this.typeToInterceptorsMap_.size) {
            return false;
        }
        this.typeToInterceptorsMap_ = new Map();
        return true;
    };
    _proto.getForType = function getForType(type) {
        return this.typeToInterceptorsMap_.get(type) || new Set();
    };
    _proto.execute = function execute(type, payload) {
        var interceptors = this.getForType(type);
        for(var _iterator = _createForOfIteratorHelperLoose(interceptors), _step; !(_step = _iterator()).done;){
            var interceptor = _step.value;
            try {
                payload = interceptor(payload);
            } catch (e) {}
        }
        return payload;
    };
    return InterceptorsStorage;
}();
module.exports = InterceptorsStorage;
}}),
"[project]/node_modules/@videojs/xhr/lib/retry.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
var RetryManager = /*#__PURE__*/ function() {
    function RetryManager() {
        this.maxAttempts_ = 1;
        this.delayFactor_ = 0.1;
        this.fuzzFactor_ = 0.1;
        this.initialDelay_ = 1000;
        this.enabled_ = false;
    }
    var _proto = RetryManager.prototype;
    _proto.getIsEnabled = function getIsEnabled() {
        return this.enabled_;
    };
    _proto.enable = function enable() {
        this.enabled_ = true;
    };
    _proto.disable = function disable() {
        this.enabled_ = false;
    };
    _proto.reset = function reset() {
        this.maxAttempts_ = 1;
        this.delayFactor_ = 0.1;
        this.fuzzFactor_ = 0.1;
        this.initialDelay_ = 1000;
        this.enabled_ = false;
    };
    _proto.getMaxAttempts = function getMaxAttempts() {
        return this.maxAttempts_;
    };
    _proto.setMaxAttempts = function setMaxAttempts(maxAttempts) {
        this.maxAttempts_ = maxAttempts;
    };
    _proto.getDelayFactor = function getDelayFactor() {
        return this.delayFactor_;
    };
    _proto.setDelayFactor = function setDelayFactor(delayFactor) {
        this.delayFactor_ = delayFactor;
    };
    _proto.getFuzzFactor = function getFuzzFactor() {
        return this.fuzzFactor_;
    };
    _proto.setFuzzFactor = function setFuzzFactor(fuzzFactor) {
        this.fuzzFactor_ = fuzzFactor;
    };
    _proto.getInitialDelay = function getInitialDelay() {
        return this.initialDelay_;
    };
    _proto.setInitialDelay = function setInitialDelay(initialDelay) {
        this.initialDelay_ = initialDelay;
    };
    _proto.createRetry = function createRetry(_temp) {
        var _ref = _temp === void 0 ? {} : _temp, maxAttempts = _ref.maxAttempts, delayFactor = _ref.delayFactor, fuzzFactor = _ref.fuzzFactor, initialDelay = _ref.initialDelay;
        return new Retry({
            maxAttempts: maxAttempts || this.maxAttempts_,
            delayFactor: delayFactor || this.delayFactor_,
            fuzzFactor: fuzzFactor || this.fuzzFactor_,
            initialDelay: initialDelay || this.initialDelay_
        });
    };
    return RetryManager;
}();
var Retry = /*#__PURE__*/ function() {
    function Retry(options) {
        this.maxAttempts_ = options.maxAttempts;
        this.delayFactor_ = options.delayFactor;
        this.fuzzFactor_ = options.fuzzFactor;
        this.currentDelay_ = options.initialDelay;
        this.currentAttempt_ = 1;
    }
    var _proto2 = Retry.prototype;
    _proto2.moveToNextAttempt = function moveToNextAttempt() {
        this.currentAttempt_++;
        var delayDelta = this.currentDelay_ * this.delayFactor_;
        this.currentDelay_ = this.currentDelay_ + delayDelta;
    };
    _proto2.shouldRetry = function shouldRetry() {
        return this.currentAttempt_ < this.maxAttempts_;
    };
    _proto2.getCurrentDelay = function getCurrentDelay() {
        return this.currentDelay_;
    };
    _proto2.getCurrentMinPossibleDelay = function getCurrentMinPossibleDelay() {
        return (1 - this.fuzzFactor_) * this.currentDelay_;
    };
    _proto2.getCurrentMaxPossibleDelay = function getCurrentMaxPossibleDelay() {
        return (1 + this.fuzzFactor_) * this.currentDelay_;
    } /**
   * For example fuzzFactor is 0.1
   * This means ±10% deviation
   * So if we have delay as 1000
   * This function can generate any value from 900 to 1100
   */ ;
    _proto2.getCurrentFuzzedDelay = function getCurrentFuzzedDelay() {
        var lowValue = this.getCurrentMinPossibleDelay();
        var highValue = this.getCurrentMaxPossibleDelay();
        return lowValue + Math.random() * (highValue - lowValue);
    };
    return Retry;
}();
module.exports = RetryManager;
}}),
"[project]/node_modules/@videojs/xhr/lib/http-handler.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
var window = __turbopack_require__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
var httpResponseHandler = function httpResponseHandler(callback, decodeResponseBody) {
    if (decodeResponseBody === void 0) {
        decodeResponseBody = false;
    }
    return function(err, response, responseBody) {
        // if the XHR failed, return that error
        if (err) {
            callback(err);
            return;
        } // if the HTTP status code is 4xx or 5xx, the request also failed
        if (response.statusCode >= 400 && response.statusCode <= 599) {
            var cause = responseBody;
            if (decodeResponseBody) {
                if (window.TextDecoder) {
                    var charset = getCharset(response.headers && response.headers['content-type']);
                    try {
                        cause = new TextDecoder(charset).decode(responseBody);
                    } catch (e) {}
                } else {
                    cause = String.fromCharCode.apply(null, new Uint8Array(responseBody));
                }
            }
            callback({
                cause: cause
            });
            return;
        } // otherwise, request succeeded
        callback(null, responseBody);
    };
};
function getCharset(contentTypeHeader) {
    if (contentTypeHeader === void 0) {
        contentTypeHeader = '';
    }
    return contentTypeHeader.toLowerCase().split(';').reduce(function(charset, contentType) {
        var _contentType$split = contentType.split('='), type = _contentType$split[0], value = _contentType$split[1];
        if (type.trim() === 'charset') {
            return value.trim();
        }
        return charset;
    }, 'utf-8');
}
module.exports = httpResponseHandler;
}}),
"[project]/node_modules/@videojs/xhr/lib/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
var window = __turbopack_require__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
var _extends = __turbopack_require__("[project]/node_modules/@babel/runtime/helpers/extends.js [app-client] (ecmascript)");
var isFunction = __turbopack_require__("[project]/node_modules/is-function/index.js [app-client] (ecmascript)");
var InterceptorsStorage = __turbopack_require__("[project]/node_modules/@videojs/xhr/lib/interceptors.js [app-client] (ecmascript)");
var RetryManager = __turbopack_require__("[project]/node_modules/@videojs/xhr/lib/retry.js [app-client] (ecmascript)");
createXHR.httpHandler = __turbopack_require__("[project]/node_modules/@videojs/xhr/lib/http-handler.js [app-client] (ecmascript)");
createXHR.requestInterceptorsStorage = new InterceptorsStorage();
createXHR.responseInterceptorsStorage = new InterceptorsStorage();
createXHR.retryManager = new RetryManager();
/**
 * @license
 * slighly modified parse-headers 2.0.2 <https://github.com/kesla/parse-headers/>
 * Copyright (c) 2014 David Björklund
 * Available under the MIT license
 * <https://github.com/kesla/parse-headers/blob/master/LICENCE>
 */ var parseHeaders = function parseHeaders(headers) {
    var result = {};
    if (!headers) {
        return result;
    }
    headers.trim().split('\n').forEach(function(row) {
        var index = row.indexOf(':');
        var key = row.slice(0, index).trim().toLowerCase();
        var value = row.slice(index + 1).trim();
        if (typeof result[key] === 'undefined') {
            result[key] = value;
        } else if (Array.isArray(result[key])) {
            result[key].push(value);
        } else {
            result[key] = [
                result[key],
                value
            ];
        }
    });
    return result;
};
module.exports = createXHR; // Allow use of default import syntax in TypeScript
module.exports.default = createXHR;
createXHR.XMLHttpRequest = window.XMLHttpRequest || noop;
createXHR.XDomainRequest = "withCredentials" in new createXHR.XMLHttpRequest() ? createXHR.XMLHttpRequest : window.XDomainRequest;
forEachArray([
    "get",
    "put",
    "post",
    "patch",
    "head",
    "delete"
], function(method) {
    createXHR[method === "delete" ? "del" : method] = function(uri, options, callback) {
        options = initParams(uri, options, callback);
        options.method = method.toUpperCase();
        return _createXHR(options);
    };
});
function forEachArray(array, iterator) {
    for(var i = 0; i < array.length; i++){
        iterator(array[i]);
    }
}
function isEmpty(obj) {
    for(var i in obj){
        if (obj.hasOwnProperty(i)) return false;
    }
    return true;
}
function initParams(uri, options, callback) {
    var params = uri;
    if (isFunction(options)) {
        callback = options;
        if (typeof uri === "string") {
            params = {
                uri: uri
            };
        }
    } else {
        params = _extends({}, options, {
            uri: uri
        });
    }
    params.callback = callback;
    return params;
}
function createXHR(uri, options, callback) {
    options = initParams(uri, options, callback);
    return _createXHR(options);
}
function _createXHR(options) {
    if (typeof options.callback === "undefined") {
        throw new Error("callback argument missing");
    } // call all registered request interceptors for a given request type:
    if (options.requestType && createXHR.requestInterceptorsStorage.getIsEnabled()) {
        var requestInterceptorPayload = {
            uri: options.uri || options.url,
            headers: options.headers || {},
            body: options.body,
            metadata: options.metadata || {},
            retry: options.retry,
            timeout: options.timeout
        };
        var updatedPayload = createXHR.requestInterceptorsStorage.execute(options.requestType, requestInterceptorPayload);
        options.uri = updatedPayload.uri;
        options.headers = updatedPayload.headers;
        options.body = updatedPayload.body;
        options.metadata = updatedPayload.metadata;
        options.retry = updatedPayload.retry;
        options.timeout = updatedPayload.timeout;
    }
    var called = false;
    var callback = function cbOnce(err, response, body) {
        if (!called) {
            called = true;
            options.callback(err, response, body);
        }
    };
    function readystatechange() {
        // do not call load 2 times when response interceptors are enabled
        // why do we even need this 2nd load?
        if (xhr.readyState === 4 && !createXHR.responseInterceptorsStorage.getIsEnabled()) {
            setTimeout(loadFunc, 0);
        }
    }
    function getBody() {
        // Chrome with requestType=blob throws errors arround when even testing access to responseText
        var body = undefined;
        if (xhr.response) {
            body = xhr.response;
        } else {
            body = xhr.responseText || getXml(xhr);
        }
        if (isJson) {
            try {
                body = JSON.parse(body);
            } catch (e) {}
        }
        return body;
    }
    function errorFunc(evt) {
        clearTimeout(timeoutTimer);
        clearTimeout(options.retryTimeout);
        if (!(evt instanceof Error)) {
            evt = new Error("" + (evt || "Unknown XMLHttpRequest Error"));
        }
        evt.statusCode = 0; // we would like to retry on error:
        if (!aborted && createXHR.retryManager.getIsEnabled() && options.retry && options.retry.shouldRetry()) {
            options.retryTimeout = setTimeout(function() {
                options.retry.moveToNextAttempt(); // we want to re-use the same options and the same xhr object:
                options.xhr = xhr;
                _createXHR(options);
            }, options.retry.getCurrentFuzzedDelay());
            return;
        } // call all registered response interceptors for a given request type:
        if (options.requestType && createXHR.responseInterceptorsStorage.getIsEnabled()) {
            var responseInterceptorPayload = {
                headers: failureResponse.headers || {},
                body: failureResponse.body,
                responseUrl: xhr.responseURL,
                responseType: xhr.responseType
            };
            var _updatedPayload = createXHR.responseInterceptorsStorage.execute(options.requestType, responseInterceptorPayload);
            failureResponse.body = _updatedPayload.body;
            failureResponse.headers = _updatedPayload.headers;
        }
        return callback(evt, failureResponse);
    } // will load the data & process the response in a special response object
    function loadFunc() {
        if (aborted) return;
        var status;
        clearTimeout(timeoutTimer);
        clearTimeout(options.retryTimeout);
        if (options.useXDR && xhr.status === undefined) {
            //IE8 CORS GET successful response doesn't have a status field, but body is fine
            status = 200;
        } else {
            status = xhr.status === 1223 ? 204 : xhr.status;
        }
        var response = failureResponse;
        var err = null;
        if (status !== 0) {
            response = {
                body: getBody(),
                statusCode: status,
                method: method,
                headers: {},
                url: uri,
                rawRequest: xhr
            };
            if (xhr.getAllResponseHeaders) {
                //remember xhr can in fact be XDR for CORS in IE
                response.headers = parseHeaders(xhr.getAllResponseHeaders());
            }
        } else {
            err = new Error("Internal XMLHttpRequest Error");
        } // call all registered response interceptors for a given request type:
        if (options.requestType && createXHR.responseInterceptorsStorage.getIsEnabled()) {
            var responseInterceptorPayload = {
                headers: response.headers || {},
                body: response.body,
                responseUrl: xhr.responseURL,
                responseType: xhr.responseType
            };
            var _updatedPayload2 = createXHR.responseInterceptorsStorage.execute(options.requestType, responseInterceptorPayload);
            response.body = _updatedPayload2.body;
            response.headers = _updatedPayload2.headers;
        }
        return callback(err, response, response.body);
    }
    var xhr = options.xhr || null;
    if (!xhr) {
        if (options.cors || options.useXDR) {
            xhr = new createXHR.XDomainRequest();
        } else {
            xhr = new createXHR.XMLHttpRequest();
        }
    }
    var key;
    var aborted;
    var uri = xhr.url = options.uri || options.url;
    var method = xhr.method = options.method || "GET";
    var body = options.body || options.data;
    var headers = xhr.headers = options.headers || {};
    var sync = !!options.sync;
    var isJson = false;
    var timeoutTimer;
    var failureResponse = {
        body: undefined,
        headers: {},
        statusCode: 0,
        method: method,
        url: uri,
        rawRequest: xhr
    };
    if ("json" in options && options.json !== false) {
        isJson = true;
        headers["accept"] || headers["Accept"] || (headers["Accept"] = "application/json"); //Don't override existing accept header declared by user
        if (method !== "GET" && method !== "HEAD") {
            headers["content-type"] || headers["Content-Type"] || (headers["Content-Type"] = "application/json"); //Don't override existing accept header declared by user
            body = JSON.stringify(options.json === true ? body : options.json);
        }
    }
    xhr.onreadystatechange = readystatechange;
    xhr.onload = loadFunc;
    xhr.onerror = errorFunc; // IE9 must have onprogress be set to a unique function.
    xhr.onprogress = function() {};
    xhr.onabort = function() {
        aborted = true;
        clearTimeout(options.retryTimeout);
    };
    xhr.ontimeout = errorFunc;
    xhr.open(method, uri, !sync, options.username, options.password); //has to be after open
    if (!sync) {
        xhr.withCredentials = !!options.withCredentials;
    } // Cannot set timeout with sync request
    // not setting timeout on the xhr object, because of old webkits etc. not handling that correctly
    // both npm's request and jquery 1.x use this kind of timeout, so this is being consistent
    if (!sync && options.timeout > 0) {
        timeoutTimer = setTimeout(function() {
            if (aborted) return;
            aborted = true; //IE9 may still call readystatechange
            xhr.abort("timeout");
            var e = new Error("XMLHttpRequest timeout");
            e.code = "ETIMEDOUT";
            errorFunc(e);
        }, options.timeout);
    }
    if (xhr.setRequestHeader) {
        for(key in headers){
            if (headers.hasOwnProperty(key)) {
                xhr.setRequestHeader(key, headers[key]);
            }
        }
    } else if (options.headers && !isEmpty(options.headers)) {
        throw new Error("Headers cannot be set on an XDomainRequest object");
    }
    if ("responseType" in options) {
        xhr.responseType = options.responseType;
    }
    if ("beforeSend" in options && typeof options.beforeSend === "function") {
        options.beforeSend(xhr);
    } // Microsoft Edge browser sends "undefined" when send is called with undefined value.
    // XMLHttpRequest spec says to pass null as body to indicate no body
    // See https://github.com/naugtur/xhr/issues/100.
    xhr.send(body || null);
    return xhr;
}
function getXml(xhr) {
    // xhr.responseXML will throw Exception "InvalidStateError" or "DOMException"
    // See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/responseXML.
    try {
        if (xhr.responseType === "document") {
            return xhr.responseXML;
        }
        var firefoxBugTakenEffect = xhr.responseXML && xhr.responseXML.documentElement.nodeName === "parsererror";
        if (xhr.responseType === "" && !firefoxBugTakenEffect) {
            return xhr.responseXML;
        }
    } catch (e) {}
    return null;
}
function noop() {}
}}),
"[project]/node_modules/videojs-vtt.js/lib/vtt.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
/**
 * Copyright 2013 vtt.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /* -*- Mode: Java; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */ /* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab: */ var document = __turbopack_require__("[project]/node_modules/global/document.js [app-client] (ecmascript)");
var _objCreate = Object.create || function() {
    function F() {}
    return function(o) {
        if (arguments.length !== 1) {
            throw new Error('Object.create shim only accepts one parameter.');
        }
        F.prototype = o;
        return new F();
    };
}();
// Creates a new ParserError object from an errorData object. The errorData
// object should have default code and message properties. The default message
// property can be overriden by passing in a message parameter.
// See ParsingError.Errors below for acceptable errors.
function ParsingError(errorData, message) {
    this.name = "ParsingError";
    this.code = errorData.code;
    this.message = message || errorData.message;
}
ParsingError.prototype = _objCreate(Error.prototype);
ParsingError.prototype.constructor = ParsingError;
// ParsingError metadata for acceptable ParsingErrors.
ParsingError.Errors = {
    BadSignature: {
        code: 0,
        message: "Malformed WebVTT signature."
    },
    BadTimeStamp: {
        code: 1,
        message: "Malformed time stamp."
    }
};
// Try to parse input as a time stamp.
function parseTimeStamp(input) {
    function computeSeconds(h, m, s, f) {
        return (h | 0) * 3600 + (m | 0) * 60 + (s | 0) + (f | 0) / 1000;
    }
    var m = input.match(/^(\d+):(\d{1,2})(:\d{1,2})?\.(\d{3})/);
    if (!m) {
        return null;
    }
    if (m[3]) {
        // Timestamp takes the form of [hours]:[minutes]:[seconds].[milliseconds]
        return computeSeconds(m[1], m[2], m[3].replace(":", ""), m[4]);
    } else if (m[1] > 59) {
        // Timestamp takes the form of [hours]:[minutes].[milliseconds]
        // First position is hours as it's over 59.
        return computeSeconds(m[1], m[2], 0, m[4]);
    } else {
        // Timestamp takes the form of [minutes]:[seconds].[milliseconds]
        return computeSeconds(0, m[1], m[2], m[4]);
    }
}
// A settings object holds key/value pairs and will ignore anything but the first
// assignment to a specific key.
function Settings() {
    this.values = _objCreate(null);
}
Settings.prototype = {
    // Only accept the first assignment to any key.
    set: function(k, v) {
        if (!this.get(k) && v !== "") {
            this.values[k] = v;
        }
    },
    // Return the value for a key, or a default value.
    // If 'defaultKey' is passed then 'dflt' is assumed to be an object with
    // a number of possible default values as properties where 'defaultKey' is
    // the key of the property that will be chosen; otherwise it's assumed to be
    // a single value.
    get: function(k, dflt, defaultKey) {
        if (defaultKey) {
            return this.has(k) ? this.values[k] : dflt[defaultKey];
        }
        return this.has(k) ? this.values[k] : dflt;
    },
    // Check whether we have a value for a key.
    has: function(k) {
        return k in this.values;
    },
    // Accept a setting if its one of the given alternatives.
    alt: function(k, v, a) {
        for(var n = 0; n < a.length; ++n){
            if (v === a[n]) {
                this.set(k, v);
                break;
            }
        }
    },
    // Accept a setting if its a valid (signed) integer.
    integer: function(k, v) {
        if (/^-?\d+$/.test(v)) {
            this.set(k, parseInt(v, 10));
        }
    },
    // Accept a setting if its a valid percentage.
    percent: function(k, v) {
        var m;
        if (m = v.match(/^([\d]{1,3})(\.[\d]*)?%$/)) {
            v = parseFloat(v);
            if (v >= 0 && v <= 100) {
                this.set(k, v);
                return true;
            }
        }
        return false;
    }
};
// Helper function to parse input into groups separated by 'groupDelim', and
// interprete each group as a key/value pair separated by 'keyValueDelim'.
function parseOptions(input, callback, keyValueDelim, groupDelim) {
    var groups = groupDelim ? input.split(groupDelim) : [
        input
    ];
    for(var i in groups){
        if (typeof groups[i] !== "string") {
            continue;
        }
        var kv = groups[i].split(keyValueDelim);
        if (kv.length !== 2) {
            continue;
        }
        var k = kv[0].trim();
        var v = kv[1].trim();
        callback(k, v);
    }
}
function parseCue(input, cue, regionList) {
    // Remember the original input if we need to throw an error.
    var oInput = input;
    // 4.1 WebVTT timestamp
    function consumeTimeStamp() {
        var ts = parseTimeStamp(input);
        if (ts === null) {
            throw new ParsingError(ParsingError.Errors.BadTimeStamp, "Malformed timestamp: " + oInput);
        }
        // Remove time stamp from input.
        input = input.replace(/^[^\sa-zA-Z-]+/, "");
        return ts;
    }
    // 4.4.2 WebVTT cue settings
    function consumeCueSettings(input, cue) {
        var settings = new Settings();
        parseOptions(input, function(k, v) {
            switch(k){
                case "region":
                    // Find the last region we parsed with the same region id.
                    for(var i = regionList.length - 1; i >= 0; i--){
                        if (regionList[i].id === v) {
                            settings.set(k, regionList[i].region);
                            break;
                        }
                    }
                    break;
                case "vertical":
                    settings.alt(k, v, [
                        "rl",
                        "lr"
                    ]);
                    break;
                case "line":
                    var vals = v.split(","), vals0 = vals[0];
                    settings.integer(k, vals0);
                    settings.percent(k, vals0) ? settings.set("snapToLines", false) : null;
                    settings.alt(k, vals0, [
                        "auto"
                    ]);
                    if (vals.length === 2) {
                        settings.alt("lineAlign", vals[1], [
                            "start",
                            "center",
                            "end"
                        ]);
                    }
                    break;
                case "position":
                    vals = v.split(",");
                    settings.percent(k, vals[0]);
                    if (vals.length === 2) {
                        settings.alt("positionAlign", vals[1], [
                            "start",
                            "center",
                            "end"
                        ]);
                    }
                    break;
                case "size":
                    settings.percent(k, v);
                    break;
                case "align":
                    settings.alt(k, v, [
                        "start",
                        "center",
                        "end",
                        "left",
                        "right"
                    ]);
                    break;
            }
        }, /:/, /\s/);
        // Apply default values for any missing fields.
        cue.region = settings.get("region", null);
        cue.vertical = settings.get("vertical", "");
        try {
            cue.line = settings.get("line", "auto");
        } catch (e) {}
        cue.lineAlign = settings.get("lineAlign", "start");
        cue.snapToLines = settings.get("snapToLines", true);
        cue.size = settings.get("size", 100);
        // Safari still uses the old middle value and won't accept center
        try {
            cue.align = settings.get("align", "center");
        } catch (e) {
            cue.align = settings.get("align", "middle");
        }
        try {
            cue.position = settings.get("position", "auto");
        } catch (e) {
            cue.position = settings.get("position", {
                start: 0,
                left: 0,
                center: 50,
                middle: 50,
                end: 100,
                right: 100
            }, cue.align);
        }
        cue.positionAlign = settings.get("positionAlign", {
            start: "start",
            left: "start",
            center: "center",
            middle: "center",
            end: "end",
            right: "end"
        }, cue.align);
    }
    function skipWhitespace() {
        input = input.replace(/^\s+/, "");
    }
    // 4.1 WebVTT cue timings.
    skipWhitespace();
    cue.startTime = consumeTimeStamp(); // (1) collect cue start time
    skipWhitespace();
    if (input.substr(0, 3) !== "-->") {
        throw new ParsingError(ParsingError.Errors.BadTimeStamp, "Malformed time stamp (time stamps must be separated by '-->'): " + oInput);
    }
    input = input.substr(3);
    skipWhitespace();
    cue.endTime = consumeTimeStamp(); // (5) collect cue end time
    // 4.1 WebVTT cue settings list.
    skipWhitespace();
    consumeCueSettings(input, cue);
}
// When evaluating this file as part of a Webpack bundle for server
// side rendering, `document` is an empty object.
var TEXTAREA_ELEMENT = document.createElement && document.createElement("textarea");
var TAG_NAME = {
    c: "span",
    i: "i",
    b: "b",
    u: "u",
    ruby: "ruby",
    rt: "rt",
    v: "span",
    lang: "span"
};
// 5.1 default text color
// 5.2 default text background color is equivalent to text color with bg_ prefix
var DEFAULT_COLOR_CLASS = {
    white: 'rgba(255,255,255,1)',
    lime: 'rgba(0,255,0,1)',
    cyan: 'rgba(0,255,255,1)',
    red: 'rgba(255,0,0,1)',
    yellow: 'rgba(255,255,0,1)',
    magenta: 'rgba(255,0,255,1)',
    blue: 'rgba(0,0,255,1)',
    black: 'rgba(0,0,0,1)'
};
var TAG_ANNOTATION = {
    v: "title",
    lang: "lang"
};
var NEEDS_PARENT = {
    rt: "ruby"
};
// Parse content into a document fragment.
function parseContent(window, input) {
    function nextToken() {
        // Check for end-of-string.
        if (!input) {
            return null;
        }
        // Consume 'n' characters from the input.
        function consume(result) {
            input = input.substr(result.length);
            return result;
        }
        var m = input.match(/^([^<]*)(<[^>]*>?)?/);
        // If there is some text before the next tag, return it, otherwise return
        // the tag.
        return consume(m[1] ? m[1] : m[2]);
    }
    function unescape(s) {
        TEXTAREA_ELEMENT.innerHTML = s;
        s = TEXTAREA_ELEMENT.textContent;
        TEXTAREA_ELEMENT.textContent = "";
        return s;
    }
    function shouldAdd(current, element) {
        return !NEEDS_PARENT[element.localName] || NEEDS_PARENT[element.localName] === current.localName;
    }
    // Create an element for this tag.
    function createElement(type, annotation) {
        var tagName = TAG_NAME[type];
        if (!tagName) {
            return null;
        }
        var element = window.document.createElement(tagName);
        var name = TAG_ANNOTATION[type];
        if (name && annotation) {
            element[name] = annotation.trim();
        }
        return element;
    }
    var rootDiv = window.document.createElement("div"), current = rootDiv, t, tagStack = [];
    while((t = nextToken()) !== null){
        if (t[0] === '<') {
            if (t[1] === "/") {
                // If the closing tag matches, move back up to the parent node.
                if (tagStack.length && tagStack[tagStack.length - 1] === t.substr(2).replace(">", "")) {
                    tagStack.pop();
                    current = current.parentNode;
                }
                continue;
            }
            var ts = parseTimeStamp(t.substr(1, t.length - 2));
            var node;
            if (ts) {
                // Timestamps are lead nodes as well.
                node = window.document.createProcessingInstruction("timestamp", ts);
                current.appendChild(node);
                continue;
            }
            var m = t.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);
            // If we can't parse the tag, skip to the next tag.
            if (!m) {
                continue;
            }
            // Try to construct an element, and ignore the tag if we couldn't.
            node = createElement(m[1], m[3]);
            if (!node) {
                continue;
            }
            // Determine if the tag should be added based on the context of where it
            // is placed in the cuetext.
            if (!shouldAdd(current, node)) {
                continue;
            }
            // Set the class list (as a list of classes, separated by space).
            if (m[2]) {
                var classes = m[2].split('.');
                classes.forEach(function(cl) {
                    var bgColor = /^bg_/.test(cl);
                    // slice out `bg_` if it's a background color
                    var colorName = bgColor ? cl.slice(3) : cl;
                    if (DEFAULT_COLOR_CLASS.hasOwnProperty(colorName)) {
                        var propName = bgColor ? 'background-color' : 'color';
                        var propValue = DEFAULT_COLOR_CLASS[colorName];
                        node.style[propName] = propValue;
                    }
                });
                node.className = classes.join(' ');
            }
            // Append the node to the current node, and enter the scope of the new
            // node.
            tagStack.push(m[1]);
            current.appendChild(node);
            current = node;
            continue;
        }
        // Text nodes are leaf nodes.
        current.appendChild(window.document.createTextNode(unescape(t)));
    }
    return rootDiv;
}
// This is a list of all the Unicode characters that have a strong
// right-to-left category. What this means is that these characters are
// written right-to-left for sure. It was generated by pulling all the strong
// right-to-left characters out of the Unicode data table. That table can
// found at: http://www.unicode.org/Public/UNIDATA/UnicodeData.txt
var strongRTLRanges = [
    [
        0x5be,
        0x5be
    ],
    [
        0x5c0,
        0x5c0
    ],
    [
        0x5c3,
        0x5c3
    ],
    [
        0x5c6,
        0x5c6
    ],
    [
        0x5d0,
        0x5ea
    ],
    [
        0x5f0,
        0x5f4
    ],
    [
        0x608,
        0x608
    ],
    [
        0x60b,
        0x60b
    ],
    [
        0x60d,
        0x60d
    ],
    [
        0x61b,
        0x61b
    ],
    [
        0x61e,
        0x64a
    ],
    [
        0x66d,
        0x66f
    ],
    [
        0x671,
        0x6d5
    ],
    [
        0x6e5,
        0x6e6
    ],
    [
        0x6ee,
        0x6ef
    ],
    [
        0x6fa,
        0x70d
    ],
    [
        0x70f,
        0x710
    ],
    [
        0x712,
        0x72f
    ],
    [
        0x74d,
        0x7a5
    ],
    [
        0x7b1,
        0x7b1
    ],
    [
        0x7c0,
        0x7ea
    ],
    [
        0x7f4,
        0x7f5
    ],
    [
        0x7fa,
        0x7fa
    ],
    [
        0x800,
        0x815
    ],
    [
        0x81a,
        0x81a
    ],
    [
        0x824,
        0x824
    ],
    [
        0x828,
        0x828
    ],
    [
        0x830,
        0x83e
    ],
    [
        0x840,
        0x858
    ],
    [
        0x85e,
        0x85e
    ],
    [
        0x8a0,
        0x8a0
    ],
    [
        0x8a2,
        0x8ac
    ],
    [
        0x200f,
        0x200f
    ],
    [
        0xfb1d,
        0xfb1d
    ],
    [
        0xfb1f,
        0xfb28
    ],
    [
        0xfb2a,
        0xfb36
    ],
    [
        0xfb38,
        0xfb3c
    ],
    [
        0xfb3e,
        0xfb3e
    ],
    [
        0xfb40,
        0xfb41
    ],
    [
        0xfb43,
        0xfb44
    ],
    [
        0xfb46,
        0xfbc1
    ],
    [
        0xfbd3,
        0xfd3d
    ],
    [
        0xfd50,
        0xfd8f
    ],
    [
        0xfd92,
        0xfdc7
    ],
    [
        0xfdf0,
        0xfdfc
    ],
    [
        0xfe70,
        0xfe74
    ],
    [
        0xfe76,
        0xfefc
    ],
    [
        0x10800,
        0x10805
    ],
    [
        0x10808,
        0x10808
    ],
    [
        0x1080a,
        0x10835
    ],
    [
        0x10837,
        0x10838
    ],
    [
        0x1083c,
        0x1083c
    ],
    [
        0x1083f,
        0x10855
    ],
    [
        0x10857,
        0x1085f
    ],
    [
        0x10900,
        0x1091b
    ],
    [
        0x10920,
        0x10939
    ],
    [
        0x1093f,
        0x1093f
    ],
    [
        0x10980,
        0x109b7
    ],
    [
        0x109be,
        0x109bf
    ],
    [
        0x10a00,
        0x10a00
    ],
    [
        0x10a10,
        0x10a13
    ],
    [
        0x10a15,
        0x10a17
    ],
    [
        0x10a19,
        0x10a33
    ],
    [
        0x10a40,
        0x10a47
    ],
    [
        0x10a50,
        0x10a58
    ],
    [
        0x10a60,
        0x10a7f
    ],
    [
        0x10b00,
        0x10b35
    ],
    [
        0x10b40,
        0x10b55
    ],
    [
        0x10b58,
        0x10b72
    ],
    [
        0x10b78,
        0x10b7f
    ],
    [
        0x10c00,
        0x10c48
    ],
    [
        0x1ee00,
        0x1ee03
    ],
    [
        0x1ee05,
        0x1ee1f
    ],
    [
        0x1ee21,
        0x1ee22
    ],
    [
        0x1ee24,
        0x1ee24
    ],
    [
        0x1ee27,
        0x1ee27
    ],
    [
        0x1ee29,
        0x1ee32
    ],
    [
        0x1ee34,
        0x1ee37
    ],
    [
        0x1ee39,
        0x1ee39
    ],
    [
        0x1ee3b,
        0x1ee3b
    ],
    [
        0x1ee42,
        0x1ee42
    ],
    [
        0x1ee47,
        0x1ee47
    ],
    [
        0x1ee49,
        0x1ee49
    ],
    [
        0x1ee4b,
        0x1ee4b
    ],
    [
        0x1ee4d,
        0x1ee4f
    ],
    [
        0x1ee51,
        0x1ee52
    ],
    [
        0x1ee54,
        0x1ee54
    ],
    [
        0x1ee57,
        0x1ee57
    ],
    [
        0x1ee59,
        0x1ee59
    ],
    [
        0x1ee5b,
        0x1ee5b
    ],
    [
        0x1ee5d,
        0x1ee5d
    ],
    [
        0x1ee5f,
        0x1ee5f
    ],
    [
        0x1ee61,
        0x1ee62
    ],
    [
        0x1ee64,
        0x1ee64
    ],
    [
        0x1ee67,
        0x1ee6a
    ],
    [
        0x1ee6c,
        0x1ee72
    ],
    [
        0x1ee74,
        0x1ee77
    ],
    [
        0x1ee79,
        0x1ee7c
    ],
    [
        0x1ee7e,
        0x1ee7e
    ],
    [
        0x1ee80,
        0x1ee89
    ],
    [
        0x1ee8b,
        0x1ee9b
    ],
    [
        0x1eea1,
        0x1eea3
    ],
    [
        0x1eea5,
        0x1eea9
    ],
    [
        0x1eeab,
        0x1eebb
    ],
    [
        0x10fffd,
        0x10fffd
    ]
];
function isStrongRTLChar(charCode) {
    for(var i = 0; i < strongRTLRanges.length; i++){
        var currentRange = strongRTLRanges[i];
        if (charCode >= currentRange[0] && charCode <= currentRange[1]) {
            return true;
        }
    }
    return false;
}
function determineBidi(cueDiv) {
    var nodeStack = [], text = "", charCode;
    if (!cueDiv || !cueDiv.childNodes) {
        return "ltr";
    }
    function pushNodes(nodeStack, node) {
        for(var i = node.childNodes.length - 1; i >= 0; i--){
            nodeStack.push(node.childNodes[i]);
        }
    }
    function nextTextNode(nodeStack) {
        if (!nodeStack || !nodeStack.length) {
            return null;
        }
        var node = nodeStack.pop(), text = node.textContent || node.innerText;
        if (text) {
            // TODO: This should match all unicode type B characters (paragraph
            // separator characters). See issue #115.
            var m = text.match(/^.*(\n|\r)/);
            if (m) {
                nodeStack.length = 0;
                return m[0];
            }
            return text;
        }
        if (node.tagName === "ruby") {
            return nextTextNode(nodeStack);
        }
        if (node.childNodes) {
            pushNodes(nodeStack, node);
            return nextTextNode(nodeStack);
        }
    }
    pushNodes(nodeStack, cueDiv);
    while(text = nextTextNode(nodeStack)){
        for(var i = 0; i < text.length; i++){
            charCode = text.charCodeAt(i);
            if (isStrongRTLChar(charCode)) {
                return "rtl";
            }
        }
    }
    return "ltr";
}
function computeLinePos(cue) {
    if (typeof cue.line === "number" && (cue.snapToLines || cue.line >= 0 && cue.line <= 100)) {
        return cue.line;
    }
    if (!cue.track || !cue.track.textTrackList || !cue.track.textTrackList.mediaElement) {
        return -1;
    }
    var track = cue.track, trackList = track.textTrackList, count = 0;
    for(var i = 0; i < trackList.length && trackList[i] !== track; i++){
        if (trackList[i].mode === "showing") {
            count++;
        }
    }
    return ++count * -1;
}
function StyleBox() {}
// Apply styles to a div. If there is no div passed then it defaults to the
// div on 'this'.
StyleBox.prototype.applyStyles = function(styles, div) {
    div = div || this.div;
    for(var prop in styles){
        if (styles.hasOwnProperty(prop)) {
            div.style[prop] = styles[prop];
        }
    }
};
StyleBox.prototype.formatStyle = function(val, unit) {
    return val === 0 ? 0 : val + unit;
};
// Constructs the computed display state of the cue (a div). Places the div
// into the overlay which should be a block level element (usually a div).
function CueStyleBox(window, cue, styleOptions) {
    StyleBox.call(this);
    this.cue = cue;
    // Parse our cue's text into a DOM tree rooted at 'cueDiv'. This div will
    // have inline positioning and will function as the cue background box.
    this.cueDiv = parseContent(window, cue.text);
    var styles = {
        color: "rgba(255, 255, 255, 1)",
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        position: "relative",
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        display: "inline",
        writingMode: cue.vertical === "" ? "horizontal-tb" : cue.vertical === "lr" ? "vertical-lr" : "vertical-rl",
        unicodeBidi: "plaintext"
    };
    this.applyStyles(styles, this.cueDiv);
    // Create an absolutely positioned div that will be used to position the cue
    // div. Note, all WebVTT cue-setting alignments are equivalent to the CSS
    // mirrors of them except middle instead of center on Safari.
    this.div = window.document.createElement("div");
    styles = {
        direction: determineBidi(this.cueDiv),
        writingMode: cue.vertical === "" ? "horizontal-tb" : cue.vertical === "lr" ? "vertical-lr" : "vertical-rl",
        unicodeBidi: "plaintext",
        textAlign: cue.align === "middle" ? "center" : cue.align,
        font: styleOptions.font,
        whiteSpace: "pre-line",
        position: "absolute"
    };
    this.applyStyles(styles);
    this.div.appendChild(this.cueDiv);
    // Calculate the distance from the reference edge of the viewport to the text
    // position of the cue box. The reference edge will be resolved later when
    // the box orientation styles are applied.
    var textPos = 0;
    switch(cue.positionAlign){
        case "start":
        case "line-left":
            textPos = cue.position;
            break;
        case "center":
            textPos = cue.position - cue.size / 2;
            break;
        case "end":
        case "line-right":
            textPos = cue.position - cue.size;
            break;
    }
    // Horizontal box orientation; textPos is the distance from the left edge of the
    // area to the left edge of the box and cue.size is the distance extending to
    // the right from there.
    if (cue.vertical === "") {
        this.applyStyles({
            left: this.formatStyle(textPos, "%"),
            width: this.formatStyle(cue.size, "%")
        });
    // Vertical box orientation; textPos is the distance from the top edge of the
    // area to the top edge of the box and cue.size is the height extending
    // downwards from there.
    } else {
        this.applyStyles({
            top: this.formatStyle(textPos, "%"),
            height: this.formatStyle(cue.size, "%")
        });
    }
    this.move = function(box) {
        this.applyStyles({
            top: this.formatStyle(box.top, "px"),
            bottom: this.formatStyle(box.bottom, "px"),
            left: this.formatStyle(box.left, "px"),
            right: this.formatStyle(box.right, "px"),
            height: this.formatStyle(box.height, "px"),
            width: this.formatStyle(box.width, "px")
        });
    };
}
CueStyleBox.prototype = _objCreate(StyleBox.prototype);
CueStyleBox.prototype.constructor = CueStyleBox;
// Represents the co-ordinates of an Element in a way that we can easily
// compute things with such as if it overlaps or intersects with another Element.
// Can initialize it with either a StyleBox or another BoxPosition.
function BoxPosition(obj) {
    // Either a BoxPosition was passed in and we need to copy it, or a StyleBox
    // was passed in and we need to copy the results of 'getBoundingClientRect'
    // as the object returned is readonly. All co-ordinate values are in reference
    // to the viewport origin (top left).
    var lh, height, width, top;
    if (obj.div) {
        height = obj.div.offsetHeight;
        width = obj.div.offsetWidth;
        top = obj.div.offsetTop;
        var rects = (rects = obj.div.childNodes) && (rects = rects[0]) && rects.getClientRects && rects.getClientRects();
        obj = obj.div.getBoundingClientRect();
        // In certain cases the outter div will be slightly larger then the sum of
        // the inner div's lines. This could be due to bold text, etc, on some platforms.
        // In this case we should get the average line height and use that. This will
        // result in the desired behaviour.
        lh = rects ? Math.max(rects[0] && rects[0].height || 0, obj.height / rects.length) : 0;
    }
    this.left = obj.left;
    this.right = obj.right;
    this.top = obj.top || top;
    this.height = obj.height || height;
    this.bottom = obj.bottom || top + (obj.height || height);
    this.width = obj.width || width;
    this.lineHeight = lh !== undefined ? lh : obj.lineHeight;
}
// Move the box along a particular axis. Optionally pass in an amount to move
// the box. If no amount is passed then the default is the line height of the
// box.
BoxPosition.prototype.move = function(axis, toMove) {
    toMove = toMove !== undefined ? toMove : this.lineHeight;
    switch(axis){
        case "+x":
            this.left += toMove;
            this.right += toMove;
            break;
        case "-x":
            this.left -= toMove;
            this.right -= toMove;
            break;
        case "+y":
            this.top += toMove;
            this.bottom += toMove;
            break;
        case "-y":
            this.top -= toMove;
            this.bottom -= toMove;
            break;
    }
};
// Check if this box overlaps another box, b2.
BoxPosition.prototype.overlaps = function(b2) {
    return this.left < b2.right && this.right > b2.left && this.top < b2.bottom && this.bottom > b2.top;
};
// Check if this box overlaps any other boxes in boxes.
BoxPosition.prototype.overlapsAny = function(boxes) {
    for(var i = 0; i < boxes.length; i++){
        if (this.overlaps(boxes[i])) {
            return true;
        }
    }
    return false;
};
// Check if this box is within another box.
BoxPosition.prototype.within = function(container) {
    return this.top >= container.top && this.bottom <= container.bottom && this.left >= container.left && this.right <= container.right;
};
// Check if this box is entirely within the container or it is overlapping
// on the edge opposite of the axis direction passed. For example, if "+x" is
// passed and the box is overlapping on the left edge of the container, then
// return true.
BoxPosition.prototype.overlapsOppositeAxis = function(container, axis) {
    switch(axis){
        case "+x":
            return this.left < container.left;
        case "-x":
            return this.right > container.right;
        case "+y":
            return this.top < container.top;
        case "-y":
            return this.bottom > container.bottom;
    }
};
// Find the percentage of the area that this box is overlapping with another
// box.
BoxPosition.prototype.intersectPercentage = function(b2) {
    var x = Math.max(0, Math.min(this.right, b2.right) - Math.max(this.left, b2.left)), y = Math.max(0, Math.min(this.bottom, b2.bottom) - Math.max(this.top, b2.top)), intersectArea = x * y;
    return intersectArea / (this.height * this.width);
};
// Convert the positions from this box to CSS compatible positions using
// the reference container's positions. This has to be done because this
// box's positions are in reference to the viewport origin, whereas, CSS
// values are in referecne to their respective edges.
BoxPosition.prototype.toCSSCompatValues = function(reference) {
    return {
        top: this.top - reference.top,
        bottom: reference.bottom - this.bottom,
        left: this.left - reference.left,
        right: reference.right - this.right,
        height: this.height,
        width: this.width
    };
};
// Get an object that represents the box's position without anything extra.
// Can pass a StyleBox, HTMLElement, or another BoxPositon.
BoxPosition.getSimpleBoxPosition = function(obj) {
    var height = obj.div ? obj.div.offsetHeight : obj.tagName ? obj.offsetHeight : 0;
    var width = obj.div ? obj.div.offsetWidth : obj.tagName ? obj.offsetWidth : 0;
    var top = obj.div ? obj.div.offsetTop : obj.tagName ? obj.offsetTop : 0;
    obj = obj.div ? obj.div.getBoundingClientRect() : obj.tagName ? obj.getBoundingClientRect() : obj;
    var ret = {
        left: obj.left,
        right: obj.right,
        top: obj.top || top,
        height: obj.height || height,
        bottom: obj.bottom || top + (obj.height || height),
        width: obj.width || width
    };
    return ret;
};
// Move a StyleBox to its specified, or next best, position. The containerBox
// is the box that contains the StyleBox, such as a div. boxPositions are
// a list of other boxes that the styleBox can't overlap with.
function moveBoxToLinePosition(window, styleBox, containerBox, boxPositions) {
    // Find the best position for a cue box, b, on the video. The axis parameter
    // is a list of axis, the order of which, it will move the box along. For example:
    // Passing ["+x", "-x"] will move the box first along the x axis in the positive
    // direction. If it doesn't find a good position for it there it will then move
    // it along the x axis in the negative direction.
    function findBestPosition(b, axis) {
        var bestPosition, specifiedPosition = new BoxPosition(b), percentage = 1; // Highest possible so the first thing we get is better.
        for(var i = 0; i < axis.length; i++){
            while(b.overlapsOppositeAxis(containerBox, axis[i]) || b.within(containerBox) && b.overlapsAny(boxPositions)){
                b.move(axis[i]);
            }
            // We found a spot where we aren't overlapping anything. This is our
            // best position.
            if (b.within(containerBox)) {
                return b;
            }
            var p = b.intersectPercentage(containerBox);
            // If we're outside the container box less then we were on our last try
            // then remember this position as the best position.
            if (percentage > p) {
                bestPosition = new BoxPosition(b);
                percentage = p;
            }
            // Reset the box position to the specified position.
            b = new BoxPosition(specifiedPosition);
        }
        return bestPosition || specifiedPosition;
    }
    var boxPosition = new BoxPosition(styleBox), cue = styleBox.cue, linePos = computeLinePos(cue), axis = [];
    // If we have a line number to align the cue to.
    if (cue.snapToLines) {
        var size;
        switch(cue.vertical){
            case "":
                axis = [
                    "+y",
                    "-y"
                ];
                size = "height";
                break;
            case "rl":
                axis = [
                    "+x",
                    "-x"
                ];
                size = "width";
                break;
            case "lr":
                axis = [
                    "-x",
                    "+x"
                ];
                size = "width";
                break;
        }
        var step = boxPosition.lineHeight, position = step * Math.round(linePos), maxPosition = containerBox[size] + step, initialAxis = axis[0];
        // If the specified intial position is greater then the max position then
        // clamp the box to the amount of steps it would take for the box to
        // reach the max position.
        if (Math.abs(position) > maxPosition) {
            position = position < 0 ? -1 : 1;
            position *= Math.ceil(maxPosition / step) * step;
        }
        // If computed line position returns negative then line numbers are
        // relative to the bottom of the video instead of the top. Therefore, we
        // need to increase our initial position by the length or width of the
        // video, depending on the writing direction, and reverse our axis directions.
        if (linePos < 0) {
            position += cue.vertical === "" ? containerBox.height : containerBox.width;
            axis = axis.reverse();
        }
        // Move the box to the specified position. This may not be its best
        // position.
        boxPosition.move(initialAxis, position);
    } else {
        // If we have a percentage line value for the cue.
        var calculatedPercentage = boxPosition.lineHeight / containerBox.height * 100;
        switch(cue.lineAlign){
            case "center":
                linePos -= calculatedPercentage / 2;
                break;
            case "end":
                linePos -= calculatedPercentage;
                break;
        }
        // Apply initial line position to the cue box.
        switch(cue.vertical){
            case "":
                styleBox.applyStyles({
                    top: styleBox.formatStyle(linePos, "%")
                });
                break;
            case "rl":
                styleBox.applyStyles({
                    left: styleBox.formatStyle(linePos, "%")
                });
                break;
            case "lr":
                styleBox.applyStyles({
                    right: styleBox.formatStyle(linePos, "%")
                });
                break;
        }
        axis = [
            "+y",
            "-x",
            "+x",
            "-y"
        ];
        // Get the box position again after we've applied the specified positioning
        // to it.
        boxPosition = new BoxPosition(styleBox);
    }
    var bestPosition = findBestPosition(boxPosition, axis);
    styleBox.move(bestPosition.toCSSCompatValues(containerBox));
}
function WebVTT() {
// Nothing
}
// Helper to allow strings to be decoded instead of the default binary utf8 data.
WebVTT.StringDecoder = function() {
    return {
        decode: function(data) {
            if (!data) {
                return "";
            }
            if (typeof data !== "string") {
                throw new Error("Error - expected string data.");
            }
            return decodeURIComponent(encodeURIComponent(data));
        }
    };
};
WebVTT.convertCueToDOMTree = function(window, cuetext) {
    if (!window || !cuetext) {
        return null;
    }
    return parseContent(window, cuetext);
};
var FONT_SIZE_PERCENT = 0.05;
var FONT_STYLE = "sans-serif";
var CUE_BACKGROUND_PADDING = "1.5%";
// Runs the processing model over the cues and regions passed to it.
// @param overlay A block level element (usually a div) that the computed cues
//                and regions will be placed into.
WebVTT.processCues = function(window, cues, overlay) {
    if (!window || !cues || !overlay) {
        return null;
    }
    // Remove all previous children.
    while(overlay.firstChild){
        overlay.removeChild(overlay.firstChild);
    }
    var paddedOverlay = window.document.createElement("div");
    paddedOverlay.style.position = "absolute";
    paddedOverlay.style.left = "0";
    paddedOverlay.style.right = "0";
    paddedOverlay.style.top = "0";
    paddedOverlay.style.bottom = "0";
    paddedOverlay.style.margin = CUE_BACKGROUND_PADDING;
    overlay.appendChild(paddedOverlay);
    // Determine if we need to compute the display states of the cues. This could
    // be the case if a cue's state has been changed since the last computation or
    // if it has not been computed yet.
    function shouldCompute(cues) {
        for(var i = 0; i < cues.length; i++){
            if (cues[i].hasBeenReset || !cues[i].displayState) {
                return true;
            }
        }
        return false;
    }
    // We don't need to recompute the cues' display states. Just reuse them.
    if (!shouldCompute(cues)) {
        for(var i = 0; i < cues.length; i++){
            paddedOverlay.appendChild(cues[i].displayState);
        }
        return;
    }
    var boxPositions = [], containerBox = BoxPosition.getSimpleBoxPosition(paddedOverlay), fontSize = Math.round(containerBox.height * FONT_SIZE_PERCENT * 100) / 100;
    var styleOptions = {
        font: fontSize + "px " + FONT_STYLE
    };
    (function() {
        var styleBox, cue;
        for(var i = 0; i < cues.length; i++){
            cue = cues[i];
            // Compute the intial position and styles of the cue div.
            styleBox = new CueStyleBox(window, cue, styleOptions);
            paddedOverlay.appendChild(styleBox.div);
            // Move the cue div to it's correct line position.
            moveBoxToLinePosition(window, styleBox, containerBox, boxPositions);
            // Remember the computed div so that we don't have to recompute it later
            // if we don't have too.
            cue.displayState = styleBox.div;
            boxPositions.push(BoxPosition.getSimpleBoxPosition(styleBox));
        }
    })();
};
WebVTT.Parser = function(window, vttjs, decoder) {
    if (!decoder) {
        decoder = vttjs;
        vttjs = {};
    }
    if (!vttjs) {
        vttjs = {};
    }
    this.window = window;
    this.vttjs = vttjs;
    this.state = "INITIAL";
    this.buffer = "";
    this.decoder = decoder || new TextDecoder("utf8");
    this.regionList = [];
};
WebVTT.Parser.prototype = {
    // If the error is a ParsingError then report it to the consumer if
    // possible. If it's not a ParsingError then throw it like normal.
    reportOrThrowError: function(e) {
        if (e instanceof ParsingError) {
            this.onparsingerror && this.onparsingerror(e);
        } else {
            throw e;
        }
    },
    parse: function(data) {
        var self = this;
        // If there is no data then we won't decode it, but will just try to parse
        // whatever is in buffer already. This may occur in circumstances, for
        // example when flush() is called.
        if (data) {
            // Try to decode the data that we received.
            self.buffer += self.decoder.decode(data, {
                stream: true
            });
        }
        function collectNextLine() {
            var buffer = self.buffer;
            var pos = 0;
            while(pos < buffer.length && buffer[pos] !== '\r' && buffer[pos] !== '\n'){
                ++pos;
            }
            var line = buffer.substr(0, pos);
            // Advance the buffer early in case we fail below.
            if (buffer[pos] === '\r') {
                ++pos;
            }
            if (buffer[pos] === '\n') {
                ++pos;
            }
            self.buffer = buffer.substr(pos);
            return line;
        }
        // 3.4 WebVTT region and WebVTT region settings syntax
        function parseRegion(input) {
            var settings = new Settings();
            parseOptions(input, function(k, v) {
                switch(k){
                    case "id":
                        settings.set(k, v);
                        break;
                    case "width":
                        settings.percent(k, v);
                        break;
                    case "lines":
                        settings.integer(k, v);
                        break;
                    case "regionanchor":
                    case "viewportanchor":
                        var xy = v.split(',');
                        if (xy.length !== 2) {
                            break;
                        }
                        // We have to make sure both x and y parse, so use a temporary
                        // settings object here.
                        var anchor = new Settings();
                        anchor.percent("x", xy[0]);
                        anchor.percent("y", xy[1]);
                        if (!anchor.has("x") || !anchor.has("y")) {
                            break;
                        }
                        settings.set(k + "X", anchor.get("x"));
                        settings.set(k + "Y", anchor.get("y"));
                        break;
                    case "scroll":
                        settings.alt(k, v, [
                            "up"
                        ]);
                        break;
                }
            }, /=/, /\s/);
            // Create the region, using default values for any values that were not
            // specified.
            if (settings.has("id")) {
                var region = new (self.vttjs.VTTRegion || self.window.VTTRegion)();
                region.width = settings.get("width", 100);
                region.lines = settings.get("lines", 3);
                region.regionAnchorX = settings.get("regionanchorX", 0);
                region.regionAnchorY = settings.get("regionanchorY", 100);
                region.viewportAnchorX = settings.get("viewportanchorX", 0);
                region.viewportAnchorY = settings.get("viewportanchorY", 100);
                region.scroll = settings.get("scroll", "");
                // Register the region.
                self.onregion && self.onregion(region);
                // Remember the VTTRegion for later in case we parse any VTTCues that
                // reference it.
                self.regionList.push({
                    id: settings.get("id"),
                    region: region
                });
            }
        }
        // draft-pantos-http-live-streaming-20
        // https://tools.ietf.org/html/draft-pantos-http-live-streaming-20#section-3.5
        // 3.5 WebVTT
        function parseTimestampMap(input) {
            var settings = new Settings();
            parseOptions(input, function(k, v) {
                switch(k){
                    case "MPEGT":
                        settings.integer(k + 'S', v);
                        break;
                    case "LOCA":
                        settings.set(k + 'L', parseTimeStamp(v));
                        break;
                }
            }, /[^\d]:/, /,/);
            self.ontimestampmap && self.ontimestampmap({
                "MPEGTS": settings.get("MPEGTS"),
                "LOCAL": settings.get("LOCAL")
            });
        }
        // 3.2 WebVTT metadata header syntax
        function parseHeader(input) {
            if (input.match(/X-TIMESTAMP-MAP/)) {
                // This line contains HLS X-TIMESTAMP-MAP metadata
                parseOptions(input, function(k, v) {
                    switch(k){
                        case "X-TIMESTAMP-MAP":
                            parseTimestampMap(v);
                            break;
                    }
                }, /=/);
            } else {
                parseOptions(input, function(k, v) {
                    switch(k){
                        case "Region":
                            // 3.3 WebVTT region metadata header syntax
                            parseRegion(v);
                            break;
                    }
                }, /:/);
            }
        }
        // 5.1 WebVTT file parsing.
        try {
            var line;
            if (self.state === "INITIAL") {
                // We can't start parsing until we have the first line.
                if (!/\r\n|\n/.test(self.buffer)) {
                    return this;
                }
                line = collectNextLine();
                var m = line.match(/^WEBVTT([ \t].*)?$/);
                if (!m || !m[0]) {
                    throw new ParsingError(ParsingError.Errors.BadSignature);
                }
                self.state = "HEADER";
            }
            var alreadyCollectedLine = false;
            while(self.buffer){
                // We can't parse a line until we have the full line.
                if (!/\r\n|\n/.test(self.buffer)) {
                    return this;
                }
                if (!alreadyCollectedLine) {
                    line = collectNextLine();
                } else {
                    alreadyCollectedLine = false;
                }
                switch(self.state){
                    case "HEADER":
                        // 13-18 - Allow a header (metadata) under the WEBVTT line.
                        if (/:/.test(line)) {
                            parseHeader(line);
                        } else if (!line) {
                            // An empty line terminates the header and starts the body (cues).
                            self.state = "ID";
                        }
                        continue;
                    case "NOTE":
                        // Ignore NOTE blocks.
                        if (!line) {
                            self.state = "ID";
                        }
                        continue;
                    case "ID":
                        // Check for the start of NOTE blocks.
                        if (/^NOTE($|[ \t])/.test(line)) {
                            self.state = "NOTE";
                            break;
                        }
                        // 19-29 - Allow any number of line terminators, then initialize new cue values.
                        if (!line) {
                            continue;
                        }
                        self.cue = new (self.vttjs.VTTCue || self.window.VTTCue)(0, 0, "");
                        // Safari still uses the old middle value and won't accept center
                        try {
                            self.cue.align = "center";
                        } catch (e) {
                            self.cue.align = "middle";
                        }
                        self.state = "CUE";
                        // 30-39 - Check if self line contains an optional identifier or timing data.
                        if (line.indexOf("-->") === -1) {
                            self.cue.id = line;
                            continue;
                        }
                    // Process line as start of a cue.
                    /*falls through*/ case "CUE":
                        // 40 - Collect cue timings and settings.
                        try {
                            parseCue(line, self.cue, self.regionList);
                        } catch (e) {
                            self.reportOrThrowError(e);
                            // In case of an error ignore rest of the cue.
                            self.cue = null;
                            self.state = "BADCUE";
                            continue;
                        }
                        self.state = "CUETEXT";
                        continue;
                    case "CUETEXT":
                        var hasSubstring = line.indexOf("-->") !== -1;
                        // 34 - If we have an empty line then report the cue.
                        // 35 - If we have the special substring '-->' then report the cue,
                        // but do not collect the line as we need to process the current
                        // one as a new cue.
                        if (!line || hasSubstring && (alreadyCollectedLine = true)) {
                            // We are done parsing self cue.
                            self.oncue && self.oncue(self.cue);
                            self.cue = null;
                            self.state = "ID";
                            continue;
                        }
                        if (self.cue.text) {
                            self.cue.text += "\n";
                        }
                        self.cue.text += line.replace(/\u2028/g, '\n').replace(/u2029/g, '\n');
                        continue;
                    case "BADCUE":
                        // 54-62 - Collect and discard the remaining cue.
                        if (!line) {
                            self.state = "ID";
                        }
                        continue;
                }
            }
        } catch (e) {
            self.reportOrThrowError(e);
            // If we are currently parsing a cue, report what we have.
            if (self.state === "CUETEXT" && self.cue && self.oncue) {
                self.oncue(self.cue);
            }
            self.cue = null;
            // Enter BADWEBVTT state if header was not parsed correctly otherwise
            // another exception occurred so enter BADCUE state.
            self.state = self.state === "INITIAL" ? "BADWEBVTT" : "BADCUE";
        }
        return this;
    },
    flush: function() {
        var self = this;
        try {
            // Finish decoding the stream.
            self.buffer += self.decoder.decode();
            // Synthesize the end of the current cue or region.
            if (self.cue || self.state === "HEADER") {
                self.buffer += "\n\n";
                self.parse();
            }
            // If we've flushed, parsed, and we're still on the INITIAL state then
            // that means we don't have enough of the stream to parse the first
            // line.
            if (self.state === "INITIAL") {
                throw new ParsingError(ParsingError.Errors.BadSignature);
            }
        } catch (e) {
            self.reportOrThrowError(e);
        }
        self.onflush && self.onflush();
        return this;
    }
};
module.exports = WebVTT;
}}),
"[project]/node_modules/videojs-vtt.js/lib/vttcue.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
/**
 * Copyright 2013 vtt.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var autoKeyword = "auto";
var directionSetting = {
    "": 1,
    "lr": 1,
    "rl": 1
};
var alignSetting = {
    "start": 1,
    "center": 1,
    "end": 1,
    "left": 1,
    "right": 1,
    "auto": 1,
    "line-left": 1,
    "line-right": 1
};
function findDirectionSetting(value) {
    if (typeof value !== "string") {
        return false;
    }
    var dir = directionSetting[value.toLowerCase()];
    return dir ? value.toLowerCase() : false;
}
function findAlignSetting(value) {
    if (typeof value !== "string") {
        return false;
    }
    var align = alignSetting[value.toLowerCase()];
    return align ? value.toLowerCase() : false;
}
function VTTCue(startTime, endTime, text) {
    /**
   * Shim implementation specific properties. These properties are not in
   * the spec.
   */ // Lets us know when the VTTCue's data has changed in such a way that we need
    // to recompute its display state. This lets us compute its display state
    // lazily.
    this.hasBeenReset = false;
    /**
   * VTTCue and TextTrackCue properties
   * http://dev.w3.org/html5/webvtt/#vttcue-interface
   */ var _id = "";
    var _pauseOnExit = false;
    var _startTime = startTime;
    var _endTime = endTime;
    var _text = text;
    var _region = null;
    var _vertical = "";
    var _snapToLines = true;
    var _line = "auto";
    var _lineAlign = "start";
    var _position = "auto";
    var _positionAlign = "auto";
    var _size = 100;
    var _align = "center";
    Object.defineProperties(this, {
        "id": {
            enumerable: true,
            get: function() {
                return _id;
            },
            set: function(value) {
                _id = "" + value;
            }
        },
        "pauseOnExit": {
            enumerable: true,
            get: function() {
                return _pauseOnExit;
            },
            set: function(value) {
                _pauseOnExit = !!value;
            }
        },
        "startTime": {
            enumerable: true,
            get: function() {
                return _startTime;
            },
            set: function(value) {
                if (typeof value !== "number") {
                    throw new TypeError("Start time must be set to a number.");
                }
                _startTime = value;
                this.hasBeenReset = true;
            }
        },
        "endTime": {
            enumerable: true,
            get: function() {
                return _endTime;
            },
            set: function(value) {
                if (typeof value !== "number") {
                    throw new TypeError("End time must be set to a number.");
                }
                _endTime = value;
                this.hasBeenReset = true;
            }
        },
        "text": {
            enumerable: true,
            get: function() {
                return _text;
            },
            set: function(value) {
                _text = "" + value;
                this.hasBeenReset = true;
            }
        },
        "region": {
            enumerable: true,
            get: function() {
                return _region;
            },
            set: function(value) {
                _region = value;
                this.hasBeenReset = true;
            }
        },
        "vertical": {
            enumerable: true,
            get: function() {
                return _vertical;
            },
            set: function(value) {
                var setting = findDirectionSetting(value);
                // Have to check for false because the setting an be an empty string.
                if (setting === false) {
                    throw new SyntaxError("Vertical: an invalid or illegal direction string was specified.");
                }
                _vertical = setting;
                this.hasBeenReset = true;
            }
        },
        "snapToLines": {
            enumerable: true,
            get: function() {
                return _snapToLines;
            },
            set: function(value) {
                _snapToLines = !!value;
                this.hasBeenReset = true;
            }
        },
        "line": {
            enumerable: true,
            get: function() {
                return _line;
            },
            set: function(value) {
                if (typeof value !== "number" && value !== autoKeyword) {
                    throw new SyntaxError("Line: an invalid number or illegal string was specified.");
                }
                _line = value;
                this.hasBeenReset = true;
            }
        },
        "lineAlign": {
            enumerable: true,
            get: function() {
                return _lineAlign;
            },
            set: function(value) {
                var setting = findAlignSetting(value);
                if (!setting) {
                    console.warn("lineAlign: an invalid or illegal string was specified.");
                } else {
                    _lineAlign = setting;
                    this.hasBeenReset = true;
                }
            }
        },
        "position": {
            enumerable: true,
            get: function() {
                return _position;
            },
            set: function(value) {
                if (value < 0 || value > 100) {
                    throw new Error("Position must be between 0 and 100.");
                }
                _position = value;
                this.hasBeenReset = true;
            }
        },
        "positionAlign": {
            enumerable: true,
            get: function() {
                return _positionAlign;
            },
            set: function(value) {
                var setting = findAlignSetting(value);
                if (!setting) {
                    console.warn("positionAlign: an invalid or illegal string was specified.");
                } else {
                    _positionAlign = setting;
                    this.hasBeenReset = true;
                }
            }
        },
        "size": {
            enumerable: true,
            get: function() {
                return _size;
            },
            set: function(value) {
                if (value < 0 || value > 100) {
                    throw new Error("Size must be between 0 and 100.");
                }
                _size = value;
                this.hasBeenReset = true;
            }
        },
        "align": {
            enumerable: true,
            get: function() {
                return _align;
            },
            set: function(value) {
                var setting = findAlignSetting(value);
                if (!setting) {
                    throw new SyntaxError("align: an invalid or illegal alignment string was specified.");
                }
                _align = setting;
                this.hasBeenReset = true;
            }
        }
    });
    /**
   * Other <track> spec defined properties
   */ // http://www.whatwg.org/specs/web-apps/current-work/multipage/the-video-element.html#text-track-cue-display-state
    this.displayState = undefined;
}
/**
 * VTTCue methods
 */ VTTCue.prototype.getCueAsHTML = function() {
    // Assume WebVTT.convertCueToDOMTree is on the global.
    return WebVTT.convertCueToDOMTree(window, this.text);
};
module.exports = VTTCue;
}}),
"[project]/node_modules/videojs-vtt.js/lib/vttregion.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
/**
 * Copyright 2013 vtt.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var scrollSetting = {
    "": true,
    "up": true
};
function findScrollSetting(value) {
    if (typeof value !== "string") {
        return false;
    }
    var scroll = scrollSetting[value.toLowerCase()];
    return scroll ? value.toLowerCase() : false;
}
function isValidPercentValue(value) {
    return typeof value === "number" && value >= 0 && value <= 100;
}
// VTTRegion shim http://dev.w3.org/html5/webvtt/#vttregion-interface
function VTTRegion() {
    var _width = 100;
    var _lines = 3;
    var _regionAnchorX = 0;
    var _regionAnchorY = 100;
    var _viewportAnchorX = 0;
    var _viewportAnchorY = 100;
    var _scroll = "";
    Object.defineProperties(this, {
        "width": {
            enumerable: true,
            get: function() {
                return _width;
            },
            set: function(value) {
                if (!isValidPercentValue(value)) {
                    throw new Error("Width must be between 0 and 100.");
                }
                _width = value;
            }
        },
        "lines": {
            enumerable: true,
            get: function() {
                return _lines;
            },
            set: function(value) {
                if (typeof value !== "number") {
                    throw new TypeError("Lines must be set to a number.");
                }
                _lines = value;
            }
        },
        "regionAnchorY": {
            enumerable: true,
            get: function() {
                return _regionAnchorY;
            },
            set: function(value) {
                if (!isValidPercentValue(value)) {
                    throw new Error("RegionAnchorX must be between 0 and 100.");
                }
                _regionAnchorY = value;
            }
        },
        "regionAnchorX": {
            enumerable: true,
            get: function() {
                return _regionAnchorX;
            },
            set: function(value) {
                if (!isValidPercentValue(value)) {
                    throw new Error("RegionAnchorY must be between 0 and 100.");
                }
                _regionAnchorX = value;
            }
        },
        "viewportAnchorY": {
            enumerable: true,
            get: function() {
                return _viewportAnchorY;
            },
            set: function(value) {
                if (!isValidPercentValue(value)) {
                    throw new Error("ViewportAnchorY must be between 0 and 100.");
                }
                _viewportAnchorY = value;
            }
        },
        "viewportAnchorX": {
            enumerable: true,
            get: function() {
                return _viewportAnchorX;
            },
            set: function(value) {
                if (!isValidPercentValue(value)) {
                    throw new Error("ViewportAnchorX must be between 0 and 100.");
                }
                _viewportAnchorX = value;
            }
        },
        "scroll": {
            enumerable: true,
            get: function() {
                return _scroll;
            },
            set: function(value) {
                var setting = findScrollSetting(value);
                // Have to check for false as an empty string is a legal value.
                if (setting === false) {
                    console.warn("Scroll: an invalid or illegal string was specified.");
                } else {
                    _scroll = setting;
                }
            }
        }
    });
}
module.exports = VTTRegion;
}}),
"[project]/node_modules/videojs-vtt.js/lib/browser-index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
/**
 * Copyright 2013 vtt.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // Default exports for Node. Export the extended versions of VTTCue and
// VTTRegion in Node since we likely want the capability to convert back and
// forth between JSON. If we don't then it's not that big of a deal since we're
// off browser.
var window = __turbopack_require__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
var vttjs = module.exports = {
    WebVTT: __turbopack_require__("[project]/node_modules/videojs-vtt.js/lib/vtt.js [app-client] (ecmascript)"),
    VTTCue: __turbopack_require__("[project]/node_modules/videojs-vtt.js/lib/vttcue.js [app-client] (ecmascript)"),
    VTTRegion: __turbopack_require__("[project]/node_modules/videojs-vtt.js/lib/vttregion.js [app-client] (ecmascript)")
};
window.vttjs = vttjs;
window.WebVTT = vttjs.WebVTT;
var cueShim = vttjs.VTTCue;
var regionShim = vttjs.VTTRegion;
var nativeVTTCue = window.VTTCue;
var nativeVTTRegion = window.VTTRegion;
vttjs.shim = function() {
    window.VTTCue = cueShim;
    window.VTTRegion = regionShim;
};
vttjs.restore = function() {
    window.VTTCue = nativeVTTCue;
    window.VTTRegion = nativeVTTRegion;
};
if (!window.VTTCue) {
    vttjs.shim();
}
}}),
"[project]/node_modules/@videojs/vhs-utils/es/resolve-url.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
;
var DEFAULT_LOCATION = 'https://example.com';
var resolveUrl = function resolveUrl(baseUrl, relativeUrl) {
    // return early if we don't need to resolve
    if (/^[a-z]+:/i.test(relativeUrl)) {
        return relativeUrl;
    } // if baseUrl is a data URI, ignore it and resolve everything relative to window.location
    if (/^data:/.test(baseUrl)) {
        baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].location && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].location.href || '';
    }
    var protocolLess = /^\/\//.test(baseUrl); // remove location if window.location isn't available (i.e. we're in node)
    // and if baseUrl isn't an absolute url
    var removeLocation = !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].location && !/\/\//i.test(baseUrl); // if the base URL is relative then combine with the current location
    baseUrl = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].URL(baseUrl, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].location || DEFAULT_LOCATION);
    var newUrl = new URL(relativeUrl, baseUrl); // if we're a protocol-less url, remove the protocol
    // and if we're location-less, remove the location
    // otherwise, return the url unmodified
    if (removeLocation) {
        return newUrl.href.slice(DEFAULT_LOCATION.length);
    } else if (protocolLess) {
        return newUrl.href.slice(newUrl.protocol.length);
    }
    return newUrl.href;
};
const __TURBOPACK__default__export__ = resolveUrl;
}}),
"[project]/node_modules/@videojs/vhs-utils/es/stream.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @file stream.js
 */ /**
 * A lightweight readable stream implemention that handles event dispatching.
 *
 * @class Stream
 */ __turbopack_esm__({
    "default": (()=>Stream)
});
var Stream = /*#__PURE__*/ function() {
    function Stream() {
        this.listeners = {};
    }
    /**
   * Add a listener for a specified event type.
   *
   * @param {string} type the event name
   * @param {Function} listener the callback to be invoked when an event of
   * the specified type occurs
   */ var _proto = Stream.prototype;
    _proto.on = function on(type, listener) {
        if (!this.listeners[type]) {
            this.listeners[type] = [];
        }
        this.listeners[type].push(listener);
    } /**
   * Remove a listener for a specified event type.
   *
   * @param {string} type the event name
   * @param {Function} listener  a function previously registered for this
   * type of event through `on`
   * @return {boolean} if we could turn it off or not
   */ ;
    _proto.off = function off(type, listener) {
        if (!this.listeners[type]) {
            return false;
        }
        var index = this.listeners[type].indexOf(listener); // TODO: which is better?
        // In Video.js we slice listener functions
        // on trigger so that it does not mess up the order
        // while we loop through.
        //
        // Here we slice on off so that the loop in trigger
        // can continue using it's old reference to loop without
        // messing up the order.
        this.listeners[type] = this.listeners[type].slice(0);
        this.listeners[type].splice(index, 1);
        return index > -1;
    } /**
   * Trigger an event of the specified type on this stream. Any additional
   * arguments to this function are passed as parameters to event listeners.
   *
   * @param {string} type the event name
   */ ;
    _proto.trigger = function trigger(type) {
        var callbacks = this.listeners[type];
        if (!callbacks) {
            return;
        } // Slicing the arguments on every invocation of this method
        // can add a significant amount of overhead. Avoid the
        // intermediate object creation for the common case of a
        // single callback argument
        if (arguments.length === 2) {
            var length = callbacks.length;
            for(var i = 0; i < length; ++i){
                callbacks[i].call(this, arguments[1]);
            }
        } else {
            var args = Array.prototype.slice.call(arguments, 1);
            var _length = callbacks.length;
            for(var _i = 0; _i < _length; ++_i){
                callbacks[_i].apply(this, args);
            }
        }
    } /**
   * Destroys the stream and cleans up.
   */ ;
    _proto.dispose = function dispose() {
        this.listeners = {};
    } /**
   * Forwards all `data` events on this stream to the destination stream. The
   * destination stream should provide a method `push` to receive the data
   * events as they arrive.
   *
   * @param {Stream} destination the stream that will receive all `data` events
   * @see http://nodejs.org/api/stream.html#stream_readable_pipe_destination_options
   */ ;
    _proto.pipe = function pipe(destination) {
        this.on('data', function(data) {
            destination.push(data);
        });
    };
    return Stream;
}();
;
}}),
"[project]/node_modules/@videojs/vhs-utils/es/decode-b64-to-uint8-array.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>decodeB64ToUint8Array)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$buffer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/buffer/index.js [app-client] (ecmascript)");
;
var atob = function atob(s) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].atob ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].atob(s) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$buffer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Buffer"].from(s, 'base64').toString('binary');
};
function decodeB64ToUint8Array(b64Text) {
    var decodedString = atob(b64Text);
    var array = new Uint8Array(decodedString.length);
    for(var i = 0; i < decodedString.length; i++){
        array[i] = decodedString.charCodeAt(i);
    }
    return array;
}
}}),
"[project]/node_modules/@videojs/vhs-utils/es/codecs.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "DEFAULT_AUDIO_CODEC": (()=>DEFAULT_AUDIO_CODEC),
    "DEFAULT_VIDEO_CODEC": (()=>DEFAULT_VIDEO_CODEC),
    "browserSupportsCodec": (()=>browserSupportsCodec),
    "codecsFromDefault": (()=>codecsFromDefault),
    "getMimeForCodec": (()=>getMimeForCodec),
    "isAudioCodec": (()=>isAudioCodec),
    "isTextCodec": (()=>isTextCodec),
    "isVideoCodec": (()=>isVideoCodec),
    "mapLegacyAvcCodecs": (()=>mapLegacyAvcCodecs),
    "muxerSupportsCodec": (()=>muxerSupportsCodec),
    "parseCodecs": (()=>parseCodecs),
    "translateLegacyCodec": (()=>translateLegacyCodec),
    "translateLegacyCodecs": (()=>translateLegacyCodecs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
;
var regexs = {
    // to determine mime types
    mp4: /^(av0?1|avc0?[1234]|vp0?9|flac|opus|mp3|mp4a|mp4v|stpp.ttml.im1t)/,
    webm: /^(vp0?[89]|av0?1|opus|vorbis)/,
    ogg: /^(vp0?[89]|theora|flac|opus|vorbis)/,
    // to determine if a codec is audio or video
    video: /^(av0?1|avc0?[1234]|vp0?[89]|hvc1|hev1|theora|mp4v)/,
    audio: /^(mp4a|flac|vorbis|opus|ac-[34]|ec-3|alac|mp3|speex|aac)/,
    text: /^(stpp.ttml.im1t)/,
    // mux.js support regex
    muxerVideo: /^(avc0?1)/,
    muxerAudio: /^(mp4a)/,
    // match nothing as muxer does not support text right now.
    // there cannot never be a character before the start of a string
    // so this matches nothing.
    muxerText: /a^/
};
var mediaTypes = [
    'video',
    'audio',
    'text'
];
var upperMediaTypes = [
    'Video',
    'Audio',
    'Text'
];
var translateLegacyCodec = function translateLegacyCodec(codec) {
    if (!codec) {
        return codec;
    }
    return codec.replace(/avc1\.(\d+)\.(\d+)/i, function(orig, profile, avcLevel) {
        var profileHex = ('00' + Number(profile).toString(16)).slice(-2);
        var avcLevelHex = ('00' + Number(avcLevel).toString(16)).slice(-2);
        return 'avc1.' + profileHex + '00' + avcLevelHex;
    });
};
var translateLegacyCodecs = function translateLegacyCodecs(codecs) {
    return codecs.map(translateLegacyCodec);
};
var mapLegacyAvcCodecs = function mapLegacyAvcCodecs(codecString) {
    return codecString.replace(/avc1\.(\d+)\.(\d+)/i, function(match) {
        return translateLegacyCodecs([
            match
        ])[0];
    });
};
var parseCodecs = function parseCodecs(codecString) {
    if (codecString === void 0) {
        codecString = '';
    }
    var codecs = codecString.split(',');
    var result = [];
    codecs.forEach(function(codec) {
        codec = codec.trim();
        var codecType;
        mediaTypes.forEach(function(name) {
            var match = regexs[name].exec(codec.toLowerCase());
            if (!match || match.length <= 1) {
                return;
            }
            codecType = name; // maintain codec case
            var type = codec.substring(0, match[1].length);
            var details = codec.replace(type, '');
            result.push({
                type: type,
                details: details,
                mediaType: name
            });
        });
        if (!codecType) {
            result.push({
                type: codec,
                details: '',
                mediaType: 'unknown'
            });
        }
    });
    return result;
};
var codecsFromDefault = function codecsFromDefault(master, audioGroupId) {
    if (!master.mediaGroups.AUDIO || !audioGroupId) {
        return null;
    }
    var audioGroup = master.mediaGroups.AUDIO[audioGroupId];
    if (!audioGroup) {
        return null;
    }
    for(var name in audioGroup){
        var audioType = audioGroup[name];
        if (audioType.default && audioType.playlists) {
            // codec should be the same for all playlists within the audio type
            return parseCodecs(audioType.playlists[0].attributes.CODECS);
        }
    }
    return null;
};
var isVideoCodec = function isVideoCodec(codec) {
    if (codec === void 0) {
        codec = '';
    }
    return regexs.video.test(codec.trim().toLowerCase());
};
var isAudioCodec = function isAudioCodec(codec) {
    if (codec === void 0) {
        codec = '';
    }
    return regexs.audio.test(codec.trim().toLowerCase());
};
var isTextCodec = function isTextCodec(codec) {
    if (codec === void 0) {
        codec = '';
    }
    return regexs.text.test(codec.trim().toLowerCase());
};
var getMimeForCodec = function getMimeForCodec(codecString) {
    if (!codecString || typeof codecString !== 'string') {
        return;
    }
    var codecs = codecString.toLowerCase().split(',').map(function(c) {
        return translateLegacyCodec(c.trim());
    }); // default to video type
    var type = 'video'; // only change to audio type if the only codec we have is
    // audio
    if (codecs.length === 1 && isAudioCodec(codecs[0])) {
        type = 'audio';
    } else if (codecs.length === 1 && isTextCodec(codecs[0])) {
        // text uses application/<container> for now
        type = 'application';
    } // default the container to mp4
    var container = 'mp4'; // every codec must be able to go into the container
    // for that container to be the correct one
    if (codecs.every(function(c) {
        return regexs.mp4.test(c);
    })) {
        container = 'mp4';
    } else if (codecs.every(function(c) {
        return regexs.webm.test(c);
    })) {
        container = 'webm';
    } else if (codecs.every(function(c) {
        return regexs.ogg.test(c);
    })) {
        container = 'ogg';
    }
    return type + "/" + container + ";codecs=\"" + codecString + "\"";
};
var browserSupportsCodec = function browserSupportsCodec(codecString, withMMS) {
    if (codecString === void 0) {
        codecString = '';
    }
    if (withMMS === void 0) {
        withMMS = false;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].MediaSource && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].MediaSource.isTypeSupported && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].MediaSource.isTypeSupported(getMimeForCodec(codecString)) || withMMS && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ManagedMediaSource && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ManagedMediaSource.isTypeSupported && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ManagedMediaSource.isTypeSupported(getMimeForCodec(codecString)) || false;
};
var muxerSupportsCodec = function muxerSupportsCodec(codecString) {
    if (codecString === void 0) {
        codecString = '';
    }
    return codecString.toLowerCase().split(',').every(function(codec) {
        codec = codec.trim(); // any match is supported.
        for(var i = 0; i < upperMediaTypes.length; i++){
            var type = upperMediaTypes[i];
            if (regexs["muxer" + type].test(codec)) {
                return true;
            }
        }
        return false;
    });
};
var DEFAULT_AUDIO_CODEC = 'mp4a.40.2';
var DEFAULT_VIDEO_CODEC = 'avc1.4d400d';
}}),
"[project]/node_modules/@videojs/vhs-utils/es/media-types.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "simpleTypeFromSourceType": (()=>simpleTypeFromSourceType)
});
var MPEGURL_REGEX = /^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i;
var DASH_REGEX = /^application\/dash\+xml/i;
var simpleTypeFromSourceType = function simpleTypeFromSourceType(type) {
    if (MPEGURL_REGEX.test(type)) {
        return 'hls';
    }
    if (DASH_REGEX.test(type)) {
        return 'dash';
    } // Denotes the special case of a manifest object passed to http-streaming instead of a
    // source URL.
    //
    // See https://en.wikipedia.org/wiki/Media_type for details on specifying media types.
    //
    // In this case, vnd stands for vendor, video.js for the organization, VHS for this
    // project, and the +json suffix identifies the structure of the media type.
    if (type === 'application/vnd.videojs.vhs+json') {
        return 'vhs-json';
    }
    return null;
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ENDIANNESS": (()=>ENDIANNESS),
    "IS_BIG_ENDIAN": (()=>IS_BIG_ENDIAN),
    "IS_LITTLE_ENDIAN": (()=>IS_LITTLE_ENDIAN),
    "bytesMatch": (()=>bytesMatch),
    "bytesToNumber": (()=>bytesToNumber),
    "bytesToString": (()=>bytesToString),
    "concatTypedArrays": (()=>concatTypedArrays),
    "countBits": (()=>countBits),
    "countBytes": (()=>countBytes),
    "isArrayBufferView": (()=>isArrayBufferView),
    "isTypedArray": (()=>isTypedArray),
    "numberToBytes": (()=>numberToBytes),
    "padStart": (()=>padStart),
    "reverseBytes": (()=>reverseBytes),
    "sliceBytes": (()=>sliceBytes),
    "stringToBytes": (()=>stringToBytes),
    "toBinaryString": (()=>toBinaryString),
    "toHexString": (()=>toHexString),
    "toUint8": (()=>toUint8)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/global/window.js [app-client] (ecmascript)"); // const log2 = Math.log2 ? Math.log2 : (x) => (Math.log(x) / Math.log(2));
;
var repeat = function repeat(str, len) {
    var acc = '';
    while(len--){
        acc += str;
    }
    return acc;
}; // count the number of bits it would take to represent a number
var countBits = function countBits(x) {
    return x.toString(2).length;
}; // count the number of whole bytes it would take to represent a number
var countBytes = function countBytes(x) {
    return Math.ceil(countBits(x) / 8);
};
var padStart = function padStart(b, len, str) {
    if (str === void 0) {
        str = ' ';
    }
    return (repeat(str, len) + b.toString()).slice(-len);
};
var isArrayBufferView = function isArrayBufferView(obj) {
    if (ArrayBuffer.isView === 'function') {
        return ArrayBuffer.isView(obj);
    }
    return obj && obj.buffer instanceof ArrayBuffer;
};
var isTypedArray = function isTypedArray(obj) {
    return isArrayBufferView(obj);
};
var toUint8 = function toUint8(bytes) {
    if (bytes instanceof Uint8Array) {
        return bytes;
    }
    if (!Array.isArray(bytes) && !isTypedArray(bytes) && !(bytes instanceof ArrayBuffer)) {
        // any non-number or NaN leads to empty uint8array
        // eslint-disable-next-line
        if (typeof bytes !== 'number' || typeof bytes === 'number' && bytes !== bytes) {
            bytes = 0;
        } else {
            bytes = [
                bytes
            ];
        }
    }
    return new Uint8Array(bytes && bytes.buffer || bytes, bytes && bytes.byteOffset || 0, bytes && bytes.byteLength || 0);
};
var toHexString = function toHexString(bytes) {
    bytes = toUint8(bytes);
    var str = '';
    for(var i = 0; i < bytes.length; i++){
        str += padStart(bytes[i].toString(16), 2, '0');
    }
    return str;
};
var toBinaryString = function toBinaryString(bytes) {
    bytes = toUint8(bytes);
    var str = '';
    for(var i = 0; i < bytes.length; i++){
        str += padStart(bytes[i].toString(2), 8, '0');
    }
    return str;
};
var BigInt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt || Number;
var BYTE_TABLE = [
    BigInt('0x1'),
    BigInt('0x100'),
    BigInt('0x10000'),
    BigInt('0x1000000'),
    BigInt('0x100000000'),
    BigInt('0x10000000000'),
    BigInt('0x1000000000000'),
    BigInt('0x100000000000000'),
    BigInt('0x10000000000000000')
];
var ENDIANNESS = function() {
    var a = new Uint16Array([
        0xFFCC
    ]);
    var b = new Uint8Array(a.buffer, a.byteOffset, a.byteLength);
    if (b[0] === 0xFF) {
        return 'big';
    }
    if (b[0] === 0xCC) {
        return 'little';
    }
    return 'unknown';
}();
var IS_BIG_ENDIAN = ENDIANNESS === 'big';
var IS_LITTLE_ENDIAN = ENDIANNESS === 'little';
var bytesToNumber = function bytesToNumber(bytes, _temp) {
    var _ref = _temp === void 0 ? {} : _temp, _ref$signed = _ref.signed, signed = _ref$signed === void 0 ? false : _ref$signed, _ref$le = _ref.le, le = _ref$le === void 0 ? false : _ref$le;
    bytes = toUint8(bytes);
    var fn = le ? 'reduce' : 'reduceRight';
    var obj = bytes[fn] ? bytes[fn] : Array.prototype[fn];
    var number = obj.call(bytes, function(total, byte, i) {
        var exponent = le ? i : Math.abs(i + 1 - bytes.length);
        return total + BigInt(byte) * BYTE_TABLE[exponent];
    }, BigInt(0));
    if (signed) {
        var max = BYTE_TABLE[bytes.length] / BigInt(2) - BigInt(1);
        number = BigInt(number);
        if (number > max) {
            number -= max;
            number -= max;
            number -= BigInt(2);
        }
    }
    return Number(number);
};
var numberToBytes = function numberToBytes(number, _temp2) {
    var _ref2 = _temp2 === void 0 ? {} : _temp2, _ref2$le = _ref2.le, le = _ref2$le === void 0 ? false : _ref2$le;
    // eslint-disable-next-line
    if (typeof number !== 'bigint' && typeof number !== 'number' || typeof number === 'number' && number !== number) {
        number = 0;
    }
    number = BigInt(number);
    var byteCount = countBytes(number);
    var bytes = new Uint8Array(new ArrayBuffer(byteCount));
    for(var i = 0; i < byteCount; i++){
        var byteIndex = le ? i : Math.abs(i + 1 - bytes.length);
        bytes[byteIndex] = Number(number / BYTE_TABLE[i] & BigInt(0xFF));
        if (number < 0) {
            bytes[byteIndex] = Math.abs(~bytes[byteIndex]);
            bytes[byteIndex] -= i === 0 ? 1 : 2;
        }
    }
    return bytes;
};
var bytesToString = function bytesToString(bytes) {
    if (!bytes) {
        return '';
    } // TODO: should toUint8 handle cases where we only have 8 bytes
    // but report more since this is a Uint16+ Array?
    bytes = Array.prototype.slice.call(bytes);
    var string = String.fromCharCode.apply(null, toUint8(bytes));
    try {
        return decodeURIComponent(escape(string));
    } catch (e) {
    // or full non string data. Just return the potentially garbled string.
    }
    return string;
};
var stringToBytes = function stringToBytes(string, stringIsBytes) {
    if (typeof string !== 'string' && string && typeof string.toString === 'function') {
        string = string.toString();
    }
    if (typeof string !== 'string') {
        return new Uint8Array();
    } // If the string already is bytes, we don't have to do this
    // otherwise we do this so that we split multi length characters
    // into individual bytes
    if (!stringIsBytes) {
        string = unescape(encodeURIComponent(string));
    }
    var view = new Uint8Array(string.length);
    for(var i = 0; i < string.length; i++){
        view[i] = string.charCodeAt(i);
    }
    return view;
};
var concatTypedArrays = function concatTypedArrays() {
    for(var _len = arguments.length, buffers = new Array(_len), _key = 0; _key < _len; _key++){
        buffers[_key] = arguments[_key];
    }
    buffers = buffers.filter(function(b) {
        return b && (b.byteLength || b.length) && typeof b !== 'string';
    });
    if (buffers.length <= 1) {
        // for 0 length we will return empty uint8
        // for 1 length we return the first uint8
        return toUint8(buffers[0]);
    }
    var totalLen = buffers.reduce(function(total, buf, i) {
        return total + (buf.byteLength || buf.length);
    }, 0);
    var tempBuffer = new Uint8Array(totalLen);
    var offset = 0;
    buffers.forEach(function(buf) {
        buf = toUint8(buf);
        tempBuffer.set(buf, offset);
        offset += buf.byteLength;
    });
    return tempBuffer;
};
var bytesMatch = function bytesMatch(a, b, _temp3) {
    var _ref3 = _temp3 === void 0 ? {} : _temp3, _ref3$offset = _ref3.offset, offset = _ref3$offset === void 0 ? 0 : _ref3$offset, _ref3$mask = _ref3.mask, mask = _ref3$mask === void 0 ? [] : _ref3$mask;
    a = toUint8(a);
    b = toUint8(b); // ie 11 does not support uint8 every
    var fn = b.every ? b.every : Array.prototype.every;
    return b.length && a.length - offset >= b.length && // ie 11 doesn't support every on uin8
    fn.call(b, function(bByte, i) {
        var aByte = mask[i] ? mask[i] & a[offset + i] : a[offset + i];
        return bByte === aByte;
    });
};
var sliceBytes = function sliceBytes(src, start, end) {
    if (Uint8Array.prototype.slice) {
        return Uint8Array.prototype.slice.call(src, start, end);
    }
    return new Uint8Array(Array.prototype.slice.call(src, start, end));
};
var reverseBytes = function reverseBytes(src) {
    if (src.reverse) {
        return src.reverse();
    }
    return Array.prototype.reverse.call(src);
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/media-groups.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Loops through all supported media groups in master and calls the provided
 * callback for each group
 *
 * @param {Object} master
 *        The parsed master manifest object
 * @param {string[]} groups
 *        The media groups to call the callback for
 * @param {Function} callback
 *        Callback to call for each media group
 */ __turbopack_esm__({
    "forEachMediaGroup": (()=>forEachMediaGroup)
});
var forEachMediaGroup = function forEachMediaGroup(master, groups, callback) {
    groups.forEach(function(mediaType) {
        for(var groupKey in master.mediaGroups[mediaType]){
            for(var labelKey in master.mediaGroups[mediaType][groupKey]){
                var mediaProperties = master.mediaGroups[mediaType][groupKey][labelKey];
                callback(mediaProperties, mediaType, groupKey, labelKey);
            }
        }
    });
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/id3-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "getId3Offset": (()=>getId3Offset),
    "getId3Size": (()=>getId3Size)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)");
;
var ID3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
    0x49,
    0x44,
    0x33
]);
var getId3Size = function getId3Size(bytes, offset) {
    if (offset === void 0) {
        offset = 0;
    }
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    var flags = bytes[offset + 5];
    var returnSize = bytes[offset + 6] << 21 | bytes[offset + 7] << 14 | bytes[offset + 8] << 7 | bytes[offset + 9];
    var footerPresent = (flags & 16) >> 4;
    if (footerPresent) {
        return returnSize + 20;
    }
    return returnSize + 10;
};
var getId3Offset = function getId3Offset(bytes, offset) {
    if (offset === void 0) {
        offset = 0;
    }
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    if (bytes.length - offset < 10 || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, ID3, {
        offset: offset
    })) {
        return offset;
    }
    offset += getId3Size(bytes, offset); // recursive check for id3 tags as some files
    // have multiple ID3 tag sections even though
    // they should not.
    return getId3Offset(bytes, offset);
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/codec-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "getAv1Codec": (()=>getAv1Codec),
    "getAvcCodec": (()=>getAvcCodec),
    "getHvcCodec": (()=>getHvcCodec)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)"); // https://aomediacodec.github.io/av1-isobmff/#av1codecconfigurationbox-syntax
;
var getAv1Codec = function getAv1Codec(bytes) {
    var codec = '';
    var profile = bytes[1] >>> 3;
    var level = bytes[1] & 0x1F;
    var tier = bytes[2] >>> 7;
    var highBitDepth = (bytes[2] & 0x40) >> 6;
    var twelveBit = (bytes[2] & 0x20) >> 5;
    var monochrome = (bytes[2] & 0x10) >> 4;
    var chromaSubsamplingX = (bytes[2] & 0x08) >> 3;
    var chromaSubsamplingY = (bytes[2] & 0x04) >> 2;
    var chromaSamplePosition = bytes[2] & 0x03;
    codec += profile + "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(level, 2, '0');
    if (tier === 0) {
        codec += 'M';
    } else if (tier === 1) {
        codec += 'H';
    }
    var bitDepth;
    if (profile === 2 && highBitDepth) {
        bitDepth = twelveBit ? 12 : 10;
    } else {
        bitDepth = highBitDepth ? 10 : 8;
    }
    codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(bitDepth, 2, '0'); // TODO: can we parse color range??
    codec += "." + monochrome;
    codec += "." + chromaSubsamplingX + chromaSubsamplingY + chromaSamplePosition;
    return codec;
};
var getAvcCodec = function getAvcCodec(bytes) {
    var profileId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toHexString"])(bytes[1]);
    var constraintFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toHexString"])(bytes[2] & 0xFC);
    var levelId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toHexString"])(bytes[3]);
    return "" + profileId + constraintFlags + levelId;
};
var getHvcCodec = function getHvcCodec(bytes) {
    var codec = '';
    var profileSpace = bytes[1] >> 6;
    var profileId = bytes[1] & 0x1F;
    var tierFlag = (bytes[1] & 0x20) >> 5;
    var profileCompat = bytes.subarray(2, 6);
    var constraintIds = bytes.subarray(6, 12);
    var levelId = bytes[12];
    if (profileSpace === 1) {
        codec += 'A';
    } else if (profileSpace === 2) {
        codec += 'B';
    } else if (profileSpace === 3) {
        codec += 'C';
    }
    codec += profileId + "."; // ffmpeg does this in big endian
    var profileCompatVal = parseInt((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toBinaryString"])(profileCompat).split('').reverse().join(''), 2); // apple does this in little endian...
    if (profileCompatVal > 255) {
        profileCompatVal = parseInt((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toBinaryString"])(profileCompat), 2);
    }
    codec += profileCompatVal.toString(16) + ".";
    if (tierFlag === 0) {
        codec += 'L';
    } else {
        codec += 'H';
    }
    codec += levelId;
    var constraints = '';
    for(var i = 0; i < constraintIds.length; i++){
        var v = constraintIds[i];
        if (v) {
            if (constraints) {
                constraints += '.';
            }
            constraints += v.toString(16);
        }
    }
    if (constraints) {
        codec += "." + constraints;
    }
    return codec;
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/opus-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "OPUS_HEAD": (()=>OPUS_HEAD),
    "parseOpusHead": (()=>parseOpusHead),
    "setOpusHead": (()=>setOpusHead)
});
var OPUS_HEAD = new Uint8Array([
    0x4f,
    0x70,
    0x75,
    0x73,
    0x48,
    0x65,
    0x61,
    0x64
]); // https://wiki.xiph.org/OggOpus
var parseOpusHead = function parseOpusHead(bytes) {
    var view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);
    var version = view.getUint8(0); // version 0, from mp4, does not use littleEndian.
    var littleEndian = version !== 0;
    var config = {
        version: version,
        channels: view.getUint8(1),
        preSkip: view.getUint16(2, littleEndian),
        sampleRate: view.getUint32(4, littleEndian),
        outputGain: view.getUint16(8, littleEndian),
        channelMappingFamily: view.getUint8(10)
    };
    if (config.channelMappingFamily > 0 && bytes.length > 10) {
        config.streamCount = view.getUint8(11);
        config.twoChannelStreamCount = view.getUint8(12);
        config.channelMapping = [];
        for(var c = 0; c < config.channels; c++){
            config.channelMapping.push(view.getUint8(13 + c));
        }
    }
    return config;
};
var setOpusHead = function setOpusHead(config) {
    var size = config.channelMappingFamily <= 0 ? 11 : 12 + config.channels;
    var view = new DataView(new ArrayBuffer(size));
    var littleEndian = config.version !== 0;
    view.setUint8(0, config.version);
    view.setUint8(1, config.channels);
    view.setUint16(2, config.preSkip, littleEndian);
    view.setUint32(4, config.sampleRate, littleEndian);
    view.setUint16(8, config.outputGain, littleEndian);
    view.setUint8(10, config.channelMappingFamily);
    if (config.channelMappingFamily > 0) {
        view.setUint8(11, config.streamCount);
        config.channelMapping.foreach(function(cm, i) {
            view.setUint8(12 + i, cm);
        });
    }
    return new Uint8Array(view.buffer);
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/mp4-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "addSampleDescription": (()=>addSampleDescription),
    "buildFrameTable": (()=>buildFrameTable),
    "findBox": (()=>findBox),
    "findNamedBox": (()=>findNamedBox),
    "parseDescriptors": (()=>parseDescriptors),
    "parseMediaInfo": (()=>parseMediaInfo),
    "parseTracks": (()=>parseTracks)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/codec-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$opus$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/opus-helpers.js [app-client] (ecmascript)");
;
;
;
var normalizePath = function normalizePath(path) {
    if (typeof path === 'string') {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stringToBytes"])(path);
    }
    if (typeof path === 'number') {
        return path;
    }
    return path;
};
var normalizePaths = function normalizePaths(paths) {
    if (!Array.isArray(paths)) {
        return [
            normalizePath(paths)
        ];
    }
    return paths.map(function(p) {
        return normalizePath(p);
    });
};
var DESCRIPTORS;
var parseDescriptors = function parseDescriptors(bytes) {
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    var results = [];
    var i = 0;
    while(bytes.length > i){
        var tag = bytes[i];
        var size = 0;
        var headerSize = 0; // tag
        headerSize++;
        var byte = bytes[headerSize]; // first byte
        headerSize++;
        while(byte & 0x80){
            size = (byte & 0x7F) << 7;
            byte = bytes[headerSize];
            headerSize++;
        }
        size += byte & 0x7F;
        for(var z = 0; z < DESCRIPTORS.length; z++){
            var _DESCRIPTORS$z = DESCRIPTORS[z], id = _DESCRIPTORS$z.id, parser = _DESCRIPTORS$z.parser;
            if (tag === id) {
                results.push(parser(bytes.subarray(headerSize, headerSize + size)));
                break;
            }
        }
        i += size + headerSize;
    }
    return results;
};
DESCRIPTORS = [
    {
        id: 0x03,
        parser: function parser(bytes) {
            var desc = {
                tag: 0x03,
                id: bytes[0] << 8 | bytes[1],
                flags: bytes[2],
                size: 3,
                dependsOnEsId: 0,
                ocrEsId: 0,
                descriptors: [],
                url: ''
            }; // depends on es id
            if (desc.flags & 0x80) {
                desc.dependsOnEsId = bytes[desc.size] << 8 | bytes[desc.size + 1];
                desc.size += 2;
            } // url
            if (desc.flags & 0x40) {
                var len = bytes[desc.size];
                desc.url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToString"])(bytes.subarray(desc.size + 1, desc.size + 1 + len));
                desc.size += len;
            } // ocr es id
            if (desc.flags & 0x20) {
                desc.ocrEsId = bytes[desc.size] << 8 | bytes[desc.size + 1];
                desc.size += 2;
            }
            desc.descriptors = parseDescriptors(bytes.subarray(desc.size)) || [];
            return desc;
        }
    },
    {
        id: 0x04,
        parser: function parser(bytes) {
            // DecoderConfigDescriptor
            var desc = {
                tag: 0x04,
                oti: bytes[0],
                streamType: bytes[1],
                bufferSize: bytes[2] << 16 | bytes[3] << 8 | bytes[4],
                maxBitrate: bytes[5] << 24 | bytes[6] << 16 | bytes[7] << 8 | bytes[8],
                avgBitrate: bytes[9] << 24 | bytes[10] << 16 | bytes[11] << 8 | bytes[12],
                descriptors: parseDescriptors(bytes.subarray(13))
            };
            return desc;
        }
    },
    {
        id: 0x05,
        parser: function parser(bytes) {
            // DecoderSpecificInfo
            return {
                tag: 0x05,
                bytes: bytes
            };
        }
    },
    {
        id: 0x06,
        parser: function parser(bytes) {
            // SLConfigDescriptor
            return {
                tag: 0x06,
                bytes: bytes
            };
        }
    }
];
var findBox = function findBox(bytes, paths, complete) {
    if (complete === void 0) {
        complete = false;
    }
    paths = normalizePaths(paths);
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    var results = [];
    if (!paths.length) {
        // short-circuit the search for empty paths
        return results;
    }
    var i = 0;
    while(i < bytes.length){
        var size = (bytes[i] << 24 | bytes[i + 1] << 16 | bytes[i + 2] << 8 | bytes[i + 3]) >>> 0;
        var type = bytes.subarray(i + 4, i + 8); // invalid box format.
        if (size === 0) {
            break;
        }
        var end = i + size;
        if (end > bytes.length) {
            // this box is bigger than the number of bytes we have
            // and complete is set, we cannot find any more boxes.
            if (complete) {
                break;
            }
            end = bytes.length;
        }
        var data = bytes.subarray(i + 8, end);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(type, paths[0])) {
            if (paths.length === 1) {
                // this is the end of the path and we've found the box we were
                // looking for
                results.push(data);
            } else {
                // recursively search for the next box along the path
                results.push.apply(results, findBox(data, paths.slice(1), complete));
            }
        }
        i = end;
    } // we've finished searching all of bytes
    return results;
};
var findNamedBox = function findNamedBox(bytes, name) {
    name = normalizePath(name);
    if (!name.length) {
        // short-circuit the search for empty paths
        return bytes.subarray(bytes.length);
    }
    var i = 0;
    while(i < bytes.length){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes.subarray(i, i + name.length), name)) {
            var size = (bytes[i - 4] << 24 | bytes[i - 3] << 16 | bytes[i - 2] << 8 | bytes[i - 1]) >>> 0;
            var end = size > 1 ? i + size : bytes.byteLength;
            return bytes.subarray(i + 4, end);
        }
        i++;
    } // we've finished searching all of bytes
    return bytes.subarray(bytes.length);
};
var parseSamples = function parseSamples(data, entrySize, parseEntry) {
    if (entrySize === void 0) {
        entrySize = 4;
    }
    if (parseEntry === void 0) {
        parseEntry = function parseEntry(d) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(d);
        };
    }
    var entries = [];
    if (!data || !data.length) {
        return entries;
    }
    var entryCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(data.subarray(4, 8));
    for(var i = 8; entryCount; i += entrySize, entryCount--){
        entries.push(parseEntry(data.subarray(i, i + entrySize)));
    }
    return entries;
};
var buildFrameTable = function buildFrameTable(stbl, timescale) {
    var keySamples = parseSamples(findBox(stbl, [
        'stss'
    ])[0]);
    var chunkOffsets = parseSamples(findBox(stbl, [
        'stco'
    ])[0]);
    var timeToSamples = parseSamples(findBox(stbl, [
        'stts'
    ])[0], 8, function(entry) {
        return {
            sampleCount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(entry.subarray(0, 4)),
            sampleDelta: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(entry.subarray(4, 8))
        };
    });
    var samplesToChunks = parseSamples(findBox(stbl, [
        'stsc'
    ])[0], 12, function(entry) {
        return {
            firstChunk: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(entry.subarray(0, 4)),
            samplesPerChunk: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(entry.subarray(4, 8)),
            sampleDescriptionIndex: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(entry.subarray(8, 12))
        };
    });
    var stsz = findBox(stbl, [
        'stsz'
    ])[0]; // stsz starts with a 4 byte sampleSize which we don't need
    var sampleSizes = parseSamples(stsz && stsz.length && stsz.subarray(4) || null);
    var frames = [];
    for(var chunkIndex = 0; chunkIndex < chunkOffsets.length; chunkIndex++){
        var samplesInChunk = void 0;
        for(var i = 0; i < samplesToChunks.length; i++){
            var sampleToChunk = samplesToChunks[i];
            var isThisOne = chunkIndex + 1 >= sampleToChunk.firstChunk && (i + 1 >= samplesToChunks.length || chunkIndex + 1 < samplesToChunks[i + 1].firstChunk);
            if (isThisOne) {
                samplesInChunk = sampleToChunk.samplesPerChunk;
                break;
            }
        }
        var chunkOffset = chunkOffsets[chunkIndex];
        for(var _i = 0; _i < samplesInChunk; _i++){
            var frameEnd = sampleSizes[frames.length]; // if we don't have key samples every frame is a keyframe
            var keyframe = !keySamples.length;
            if (keySamples.length && keySamples.indexOf(frames.length + 1) !== -1) {
                keyframe = true;
            }
            var frame = {
                keyframe: keyframe,
                start: chunkOffset,
                end: chunkOffset + frameEnd
            };
            for(var k = 0; k < timeToSamples.length; k++){
                var _timeToSamples$k = timeToSamples[k], sampleCount = _timeToSamples$k.sampleCount, sampleDelta = _timeToSamples$k.sampleDelta;
                if (frames.length <= sampleCount) {
                    // ms to ns
                    var lastTimestamp = frames.length ? frames[frames.length - 1].timestamp : 0;
                    frame.timestamp = lastTimestamp + sampleDelta / timescale * 1000;
                    frame.duration = sampleDelta;
                    break;
                }
            }
            frames.push(frame);
            chunkOffset += frameEnd;
        }
    }
    return frames;
};
var addSampleDescription = function addSampleDescription(track, bytes) {
    var codec = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToString"])(bytes.subarray(0, 4));
    if (track.type === 'video') {
        track.info = track.info || {};
        track.info.width = bytes[28] << 8 | bytes[29];
        track.info.height = bytes[30] << 8 | bytes[31];
    } else if (track.type === 'audio') {
        track.info = track.info || {};
        track.info.channels = bytes[20] << 8 | bytes[21];
        track.info.bitDepth = bytes[22] << 8 | bytes[23];
        track.info.sampleRate = bytes[28] << 8 | bytes[29];
    }
    if (codec === 'avc1') {
        var avcC = findNamedBox(bytes, 'avcC'); // AVCDecoderConfigurationRecord
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAvcCodec"])(avcC);
        track.info.avcC = avcC; // TODO: do we need to parse all this?
    /* {
      configurationVersion: avcC[0],
      profile: avcC[1],
      profileCompatibility: avcC[2],
      level: avcC[3],
      lengthSizeMinusOne: avcC[4] & 0x3
    };
     let spsNalUnitCount = avcC[5] & 0x1F;
    const spsNalUnits = track.info.avc.spsNalUnits = [];
     // past spsNalUnitCount
    let offset = 6;
     while (spsNalUnitCount--) {
      const nalLen = avcC[offset] << 8 | avcC[offset + 1];
       spsNalUnits.push(avcC.subarray(offset + 2, offset + 2 + nalLen));
       offset += nalLen + 2;
    }
    let ppsNalUnitCount = avcC[offset];
    const ppsNalUnits = track.info.avc.ppsNalUnits = [];
     // past ppsNalUnitCount
    offset += 1;
     while (ppsNalUnitCount--) {
      const nalLen = avcC[offset] << 8 | avcC[offset + 1];
       ppsNalUnits.push(avcC.subarray(offset + 2, offset + 2 + nalLen));
       offset += nalLen + 2;
    }*/ // HEVCDecoderConfigurationRecord
    } else if (codec === 'hvc1' || codec === 'hev1') {
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHvcCodec"])(findNamedBox(bytes, 'hvcC'));
    } else if (codec === 'mp4a' || codec === 'mp4v') {
        var esds = findNamedBox(bytes, 'esds');
        var esDescriptor = parseDescriptors(esds.subarray(4))[0];
        var decoderConfig = esDescriptor && esDescriptor.descriptors.filter(function(_ref) {
            var tag = _ref.tag;
            return tag === 0x04;
        })[0];
        if (decoderConfig) {
            // most codecs do not have a further '.'
            // such as 0xa5 for ac-3 and 0xa6 for e-ac-3
            codec += '.' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toHexString"])(decoderConfig.oti);
            if (decoderConfig.oti === 0x40) {
                codec += '.' + (decoderConfig.descriptors[0].bytes[0] >> 3).toString();
            } else if (decoderConfig.oti === 0x20) {
                codec += '.' + decoderConfig.descriptors[0].bytes[4].toString();
            } else if (decoderConfig.oti === 0xdd) {
                codec = 'vorbis';
            }
        } else if (track.type === 'audio') {
            codec += '.40.2';
        } else {
            codec += '.20.9';
        }
    } else if (codec === 'av01') {
        // AV1DecoderConfigurationRecord
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAv1Codec"])(findNamedBox(bytes, 'av1C'));
    } else if (codec === 'vp09') {
        // VPCodecConfigurationRecord
        var vpcC = findNamedBox(bytes, 'vpcC'); // https://www.webmproject.org/vp9/mp4/
        var profile = vpcC[0];
        var level = vpcC[1];
        var bitDepth = vpcC[2] >> 4;
        var chromaSubsampling = (vpcC[2] & 0x0F) >> 1;
        var videoFullRangeFlag = (vpcC[2] & 0x0F) >> 3;
        var colourPrimaries = vpcC[3];
        var transferCharacteristics = vpcC[4];
        var matrixCoefficients = vpcC[5];
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(profile, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(level, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(bitDepth, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(chromaSubsampling, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(colourPrimaries, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(transferCharacteristics, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(matrixCoefficients, 2, '0');
        codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(videoFullRangeFlag, 2, '0');
    } else if (codec === 'theo') {
        codec = 'theora';
    } else if (codec === 'spex') {
        codec = 'speex';
    } else if (codec === '.mp3') {
        codec = 'mp4a.40.34';
    } else if (codec === 'msVo') {
        codec = 'vorbis';
    } else if (codec === 'Opus') {
        codec = 'opus';
        var dOps = findNamedBox(bytes, 'dOps');
        track.info.opus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$opus$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseOpusHead"])(dOps); // TODO: should this go into the webm code??
        // Firefox requires a codecDelay for opus playback
        // see https://bugzilla.mozilla.org/show_bug.cgi?id=1276238
        track.info.codecDelay = 6500000;
    } else {
        codec = codec.toLowerCase();
    }
    /* eslint-enable */ // flac, ac-3, ec-3, opus
    track.codec = codec;
};
var parseTracks = function parseTracks(bytes, frameTable) {
    if (frameTable === void 0) {
        frameTable = true;
    }
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    var traks = findBox(bytes, [
        'moov',
        'trak'
    ], true);
    var tracks = [];
    traks.forEach(function(trak) {
        var track = {
            bytes: trak
        };
        var mdia = findBox(trak, [
            'mdia'
        ])[0];
        var hdlr = findBox(mdia, [
            'hdlr'
        ])[0];
        var trakType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToString"])(hdlr.subarray(8, 12));
        if (trakType === 'soun') {
            track.type = 'audio';
        } else if (trakType === 'vide') {
            track.type = 'video';
        } else {
            track.type = trakType;
        }
        var tkhd = findBox(trak, [
            'tkhd'
        ])[0];
        if (tkhd) {
            var view = new DataView(tkhd.buffer, tkhd.byteOffset, tkhd.byteLength);
            var tkhdVersion = view.getUint8(0);
            track.number = tkhdVersion === 0 ? view.getUint32(12) : view.getUint32(20);
        }
        var mdhd = findBox(mdia, [
            'mdhd'
        ])[0];
        if (mdhd) {
            // mdhd is a FullBox, meaning it will have its own version as the first byte
            var version = mdhd[0];
            var index = version === 0 ? 12 : 20;
            track.timescale = (mdhd[index] << 24 | mdhd[index + 1] << 16 | mdhd[index + 2] << 8 | mdhd[index + 3]) >>> 0;
        }
        var stbl = findBox(mdia, [
            'minf',
            'stbl'
        ])[0];
        var stsd = findBox(stbl, [
            'stsd'
        ])[0];
        var descriptionCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(stsd.subarray(4, 8));
        var offset = 8; // add codec and codec info
        while(descriptionCount--){
            var len = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(stsd.subarray(offset, offset + 4));
            var sampleDescriptor = stsd.subarray(offset + 4, offset + 4 + len);
            addSampleDescription(track, sampleDescriptor);
            offset += 4 + len;
        }
        if (frameTable) {
            track.frameTable = buildFrameTable(stbl, track.timescale);
        } // codec has no sub parameters
        tracks.push(track);
    });
    return tracks;
};
var parseMediaInfo = function parseMediaInfo(bytes) {
    var mvhd = findBox(bytes, [
        'moov',
        'mvhd'
    ], true)[0];
    if (!mvhd || !mvhd.length) {
        return;
    }
    var info = {}; // ms to ns
    // mvhd v1 has 8 byte duration and other fields too
    if (mvhd[0] === 1) {
        info.timestampScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(mvhd.subarray(20, 24));
        info.duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(mvhd.subarray(24, 32));
    } else {
        info.timestampScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(mvhd.subarray(12, 16));
        info.duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(mvhd.subarray(16, 20));
    }
    info.bytes = mvhd;
    return info;
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/ebml-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "EBML_TAGS": (()=>EBML_TAGS),
    "decodeBlock": (()=>decodeBlock),
    "findEbml": (()=>findEbml),
    "parseData": (()=>parseData),
    "parseTracks": (()=>parseTracks)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/codec-helpers.js [app-client] (ecmascript)"); // relevant specs for this parser:
;
;
var EBML_TAGS = {
    EBML: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x1A,
        0x45,
        0xDF,
        0xA3
    ]),
    DocType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x42,
        0x82
    ]),
    Segment: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x18,
        0x53,
        0x80,
        0x67
    ]),
    SegmentInfo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x15,
        0x49,
        0xA9,
        0x66
    ]),
    Tracks: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x16,
        0x54,
        0xAE,
        0x6B
    ]),
    Track: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xAE
    ]),
    TrackNumber: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xd7
    ]),
    DefaultDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x23,
        0xe3,
        0x83
    ]),
    TrackEntry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xAE
    ]),
    TrackType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x83
    ]),
    FlagDefault: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x88
    ]),
    CodecID: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x86
    ]),
    CodecPrivate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x63,
        0xA2
    ]),
    VideoTrack: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xe0
    ]),
    AudioTrack: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xe1
    ]),
    // Not used yet, but will be used for live webm/mkv
    // see https://www.matroska.org/technical/basics.html#block-structure
    // see https://www.matroska.org/technical/basics.html#simpleblock-structure
    Cluster: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x1F,
        0x43,
        0xB6,
        0x75
    ]),
    Timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xE7
    ]),
    TimestampScale: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x2A,
        0xD7,
        0xB1
    ]),
    BlockGroup: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xA0
    ]),
    BlockDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x9B
    ]),
    Block: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xA1
    ]),
    SimpleBlock: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0xA3
    ])
};
/**
 * This is a simple table to determine the length
 * of things in ebml. The length is one based (starts at 1,
 * rather than zero) and for every zero bit before a one bit
 * we add one to length. We also need this table because in some
 * case we have to xor all the length bits from another value.
 */ var LENGTH_TABLE = [
    128,
    64,
    32,
    16,
    8,
    4,
    2,
    1
];
var getLength = function getLength(byte) {
    var len = 1;
    for(var i = 0; i < LENGTH_TABLE.length; i++){
        if (byte & LENGTH_TABLE[i]) {
            break;
        }
        len++;
    }
    return len;
}; // length in ebml is stored in the first 4 to 8 bits
// of the first byte. 4 for the id length and 8 for the
// data size length. Length is measured by converting the number to binary
// then 1 + the number of zeros before a 1 is encountered starting
// from the left.
var getvint = function getvint(bytes, offset, removeLength, signed) {
    if (removeLength === void 0) {
        removeLength = true;
    }
    if (signed === void 0) {
        signed = false;
    }
    var length = getLength(bytes[offset]);
    var valueBytes = bytes.subarray(offset, offset + length); // NOTE that we do **not** subarray here because we need to copy these bytes
    // as they will be modified below to remove the dataSizeLen bits and we do not
    // want to modify the original data. normally we could just call slice on
    // uint8array but ie 11 does not support that...
    if (removeLength) {
        valueBytes = Array.prototype.slice.call(bytes, offset, offset + length);
        valueBytes[0] ^= LENGTH_TABLE[length - 1];
    }
    return {
        length: length,
        value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(valueBytes, {
            signed: signed
        }),
        bytes: valueBytes
    };
};
var normalizePath = function normalizePath(path) {
    if (typeof path === 'string') {
        return path.match(/.{1,2}/g).map(function(p) {
            return normalizePath(p);
        });
    }
    if (typeof path === 'number') {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberToBytes"])(path);
    }
    return path;
};
var normalizePaths = function normalizePaths(paths) {
    if (!Array.isArray(paths)) {
        return [
            normalizePath(paths)
        ];
    }
    return paths.map(function(p) {
        return normalizePath(p);
    });
};
var getInfinityDataSize = function getInfinityDataSize(id, bytes, offset) {
    if (offset >= bytes.length) {
        return bytes.length;
    }
    var innerid = getvint(bytes, offset, false);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(id.bytes, innerid.bytes)) {
        return offset;
    }
    var dataHeader = getvint(bytes, offset + innerid.length);
    return getInfinityDataSize(id, bytes, offset + dataHeader.length + dataHeader.value + innerid.length);
};
var findEbml = function findEbml(bytes, paths) {
    paths = normalizePaths(paths);
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    var results = [];
    if (!paths.length) {
        return results;
    }
    var i = 0;
    while(i < bytes.length){
        var id = getvint(bytes, i, false);
        var dataHeader = getvint(bytes, i + id.length);
        var dataStart = i + id.length + dataHeader.length; // dataSize is unknown or this is a live stream
        if (dataHeader.value === 0x7f) {
            dataHeader.value = getInfinityDataSize(id, bytes, dataStart);
            if (dataHeader.value !== bytes.length) {
                dataHeader.value -= dataStart;
            }
        }
        var dataEnd = dataStart + dataHeader.value > bytes.length ? bytes.length : dataStart + dataHeader.value;
        var data = bytes.subarray(dataStart, dataEnd);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(paths[0], id.bytes)) {
            if (paths.length === 1) {
                // this is the end of the paths and we've found the tag we were
                // looking for
                results.push(data);
            } else {
                // recursively search for the next tag inside of the data
                // of this one
                results = results.concat(findEbml(data, paths.slice(1)));
            }
        }
        var totalLength = id.length + dataHeader.length + data.length; // move past this tag entirely, we are not looking for it
        i += totalLength;
    }
    return results;
}; // see https://www.matroska.org/technical/basics.html#block-structure
var decodeBlock = function decodeBlock(block, type, timestampScale, clusterTimestamp) {
    var duration;
    if (type === 'group') {
        duration = findEbml(block, [
            EBML_TAGS.BlockDuration
        ])[0];
        if (duration) {
            duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(duration);
            duration = 1 / timestampScale * duration * timestampScale / 1000;
        }
        block = findEbml(block, [
            EBML_TAGS.Block
        ])[0];
        type = 'block'; // treat data as a block after this point
    }
    var dv = new DataView(block.buffer, block.byteOffset, block.byteLength);
    var trackNumber = getvint(block, 0);
    var timestamp = dv.getInt16(trackNumber.length, false);
    var flags = block[trackNumber.length + 2];
    var data = block.subarray(trackNumber.length + 3); // pts/dts in seconds
    var ptsdts = 1 / timestampScale * (clusterTimestamp + timestamp) * timestampScale / 1000; // return the frame
    var parsed = {
        duration: duration,
        trackNumber: trackNumber.value,
        keyframe: type === 'simple' && flags >> 7 === 1,
        invisible: (flags & 0x08) >> 3 === 1,
        lacing: (flags & 0x06) >> 1,
        discardable: type === 'simple' && (flags & 0x01) === 1,
        frames: [],
        pts: ptsdts,
        dts: ptsdts,
        timestamp: timestamp
    };
    if (!parsed.lacing) {
        parsed.frames.push(data);
        return parsed;
    }
    var numberOfFrames = data[0] + 1;
    var frameSizes = [];
    var offset = 1; // Fixed
    if (parsed.lacing === 2) {
        var sizeOfFrame = (data.length - offset) / numberOfFrames;
        for(var i = 0; i < numberOfFrames; i++){
            frameSizes.push(sizeOfFrame);
        }
    } // xiph
    if (parsed.lacing === 1) {
        for(var _i = 0; _i < numberOfFrames - 1; _i++){
            var size = 0;
            do {
                size += data[offset];
                offset++;
            }while (data[offset - 1] === 0xFF)
            frameSizes.push(size);
        }
    } // ebml
    if (parsed.lacing === 3) {
        // first vint is unsinged
        // after that vints are singed and
        // based on a compounding size
        var _size = 0;
        for(var _i2 = 0; _i2 < numberOfFrames - 1; _i2++){
            var vint = _i2 === 0 ? getvint(data, offset) : getvint(data, offset, true, true);
            _size += vint.value;
            frameSizes.push(_size);
            offset += vint.length;
        }
    }
    frameSizes.forEach(function(size) {
        parsed.frames.push(data.subarray(offset, offset + size));
        offset += size;
    });
    return parsed;
}; // VP9 Codec Feature Metadata (CodecPrivate)
// https://www.webmproject.org/docs/container/
var parseVp9Private = function parseVp9Private(bytes) {
    var i = 0;
    var params = {};
    while(i < bytes.length){
        var id = bytes[i] & 0x7f;
        var len = bytes[i + 1];
        var val = void 0;
        if (len === 1) {
            val = bytes[i + 2];
        } else {
            val = bytes.subarray(i + 2, i + 2 + len);
        }
        if (id === 1) {
            params.profile = val;
        } else if (id === 2) {
            params.level = val;
        } else if (id === 3) {
            params.bitDepth = val;
        } else if (id === 4) {
            params.chromaSubsampling = val;
        } else {
            params[id] = val;
        }
        i += 2 + len;
    }
    return params;
};
var parseTracks = function parseTracks(bytes) {
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    var decodedTracks = [];
    var tracks = findEbml(bytes, [
        EBML_TAGS.Segment,
        EBML_TAGS.Tracks,
        EBML_TAGS.Track
    ]);
    if (!tracks.length) {
        tracks = findEbml(bytes, [
            EBML_TAGS.Tracks,
            EBML_TAGS.Track
        ]);
    }
    if (!tracks.length) {
        tracks = findEbml(bytes, [
            EBML_TAGS.Track
        ]);
    }
    if (!tracks.length) {
        return decodedTracks;
    }
    tracks.forEach(function(track) {
        var trackType = findEbml(track, EBML_TAGS.TrackType)[0];
        if (!trackType || !trackType.length) {
            return;
        } // 1 is video, 2 is audio, 17 is subtitle
        // other values are unimportant in this context
        if (trackType[0] === 1) {
            trackType = 'video';
        } else if (trackType[0] === 2) {
            trackType = 'audio';
        } else if (trackType[0] === 17) {
            trackType = 'subtitle';
        } else {
            return;
        } // todo parse language
        var decodedTrack = {
            rawCodec: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToString"])(findEbml(track, [
                EBML_TAGS.CodecID
            ])[0]),
            type: trackType,
            codecPrivate: findEbml(track, [
                EBML_TAGS.CodecPrivate
            ])[0],
            number: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(findEbml(track, [
                EBML_TAGS.TrackNumber
            ])[0]),
            defaultDuration: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(findEbml(track, [
                EBML_TAGS.DefaultDuration
            ])[0]),
            default: findEbml(track, [
                EBML_TAGS.FlagDefault
            ])[0],
            rawData: track
        };
        var codec = '';
        if (/V_MPEG4\/ISO\/AVC/.test(decodedTrack.rawCodec)) {
            codec = "avc1." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAvcCodec"])(decodedTrack.codecPrivate);
        } else if (/V_MPEGH\/ISO\/HEVC/.test(decodedTrack.rawCodec)) {
            codec = "hev1." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHvcCodec"])(decodedTrack.codecPrivate);
        } else if (/V_MPEG4\/ISO\/ASP/.test(decodedTrack.rawCodec)) {
            if (decodedTrack.codecPrivate) {
                codec = 'mp4v.20.' + decodedTrack.codecPrivate[4].toString();
            } else {
                codec = 'mp4v.20.9';
            }
        } else if (/^V_THEORA/.test(decodedTrack.rawCodec)) {
            codec = 'theora';
        } else if (/^V_VP8/.test(decodedTrack.rawCodec)) {
            codec = 'vp8';
        } else if (/^V_VP9/.test(decodedTrack.rawCodec)) {
            if (decodedTrack.codecPrivate) {
                var _parseVp9Private = parseVp9Private(decodedTrack.codecPrivate), profile = _parseVp9Private.profile, level = _parseVp9Private.level, bitDepth = _parseVp9Private.bitDepth, chromaSubsampling = _parseVp9Private.chromaSubsampling;
                codec = 'vp09.';
                codec += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(profile, 2, '0') + ".";
                codec += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(level, 2, '0') + ".";
                codec += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(bitDepth, 2, '0') + ".";
                codec += "" + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(chromaSubsampling, 2, '0'); // Video -> Colour -> Ebml name
                var matrixCoefficients = findEbml(track, [
                    0xE0,
                    [
                        0x55,
                        0xB0
                    ],
                    [
                        0x55,
                        0xB1
                    ]
                ])[0] || [];
                var videoFullRangeFlag = findEbml(track, [
                    0xE0,
                    [
                        0x55,
                        0xB0
                    ],
                    [
                        0x55,
                        0xB9
                    ]
                ])[0] || [];
                var transferCharacteristics = findEbml(track, [
                    0xE0,
                    [
                        0x55,
                        0xB0
                    ],
                    [
                        0x55,
                        0xBA
                    ]
                ])[0] || [];
                var colourPrimaries = findEbml(track, [
                    0xE0,
                    [
                        0x55,
                        0xB0
                    ],
                    [
                        0x55,
                        0xBB
                    ]
                ])[0] || []; // if we find any optional codec parameter specify them all.
                if (matrixCoefficients.length || videoFullRangeFlag.length || transferCharacteristics.length || colourPrimaries.length) {
                    codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(colourPrimaries[0], 2, '0');
                    codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(transferCharacteristics[0], 2, '0');
                    codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(matrixCoefficients[0], 2, '0');
                    codec += "." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["padStart"])(videoFullRangeFlag[0], 2, '0');
                }
            } else {
                codec = 'vp9';
            }
        } else if (/^V_AV1/.test(decodedTrack.rawCodec)) {
            codec = "av01." + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$codec$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAv1Codec"])(decodedTrack.codecPrivate);
        } else if (/A_ALAC/.test(decodedTrack.rawCodec)) {
            codec = 'alac';
        } else if (/A_MPEG\/L2/.test(decodedTrack.rawCodec)) {
            codec = 'mp2';
        } else if (/A_MPEG\/L3/.test(decodedTrack.rawCodec)) {
            codec = 'mp3';
        } else if (/^A_AAC/.test(decodedTrack.rawCodec)) {
            if (decodedTrack.codecPrivate) {
                codec = 'mp4a.40.' + (decodedTrack.codecPrivate[0] >>> 3).toString();
            } else {
                codec = 'mp4a.40.2';
            }
        } else if (/^A_AC3/.test(decodedTrack.rawCodec)) {
            codec = 'ac-3';
        } else if (/^A_PCM/.test(decodedTrack.rawCodec)) {
            codec = 'pcm';
        } else if (/^A_MS\/ACM/.test(decodedTrack.rawCodec)) {
            codec = 'speex';
        } else if (/^A_EAC3/.test(decodedTrack.rawCodec)) {
            codec = 'ec-3';
        } else if (/^A_VORBIS/.test(decodedTrack.rawCodec)) {
            codec = 'vorbis';
        } else if (/^A_FLAC/.test(decodedTrack.rawCodec)) {
            codec = 'flac';
        } else if (/^A_OPUS/.test(decodedTrack.rawCodec)) {
            codec = 'opus';
        }
        decodedTrack.codec = codec;
        decodedTracks.push(decodedTrack);
    });
    return decodedTracks.sort(function(a, b) {
        return a.number - b.number;
    });
};
var parseData = function parseData(data, tracks) {
    var allBlocks = [];
    var segment = findEbml(data, [
        EBML_TAGS.Segment
    ])[0];
    var timestampScale = findEbml(segment, [
        EBML_TAGS.SegmentInfo,
        EBML_TAGS.TimestampScale
    ])[0]; // in nanoseconds, defaults to 1ms
    if (timestampScale && timestampScale.length) {
        timestampScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(timestampScale);
    } else {
        timestampScale = 1000000;
    }
    var clusters = findEbml(segment, [
        EBML_TAGS.Cluster
    ]);
    if (!tracks) {
        tracks = parseTracks(segment);
    }
    clusters.forEach(function(cluster, ci) {
        var simpleBlocks = findEbml(cluster, [
            EBML_TAGS.SimpleBlock
        ]).map(function(b) {
            return {
                type: 'simple',
                data: b
            };
        });
        var blockGroups = findEbml(cluster, [
            EBML_TAGS.BlockGroup
        ]).map(function(b) {
            return {
                type: 'group',
                data: b
            };
        });
        var timestamp = findEbml(cluster, [
            EBML_TAGS.Timestamp
        ])[0] || 0;
        if (timestamp && timestamp.length) {
            timestamp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesToNumber"])(timestamp);
        } // get all blocks then sort them into the correct order
        var blocks = simpleBlocks.concat(blockGroups).sort(function(a, b) {
            return a.data.byteOffset - b.data.byteOffset;
        });
        blocks.forEach(function(block, bi) {
            var decoded = decodeBlock(block.data, block.type, timestampScale, timestamp);
            allBlocks.push(decoded);
        });
    });
    return {
        tracks: tracks,
        blocks: allBlocks
    };
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/nal-helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "EMULATION_PREVENTION": (()=>EMULATION_PREVENTION),
    "NAL_TYPE_ONE": (()=>NAL_TYPE_ONE),
    "NAL_TYPE_TWO": (()=>NAL_TYPE_TWO),
    "discardEmulationPreventionBytes": (()=>discardEmulationPreventionBytes),
    "findH264Nal": (()=>findH264Nal),
    "findH265Nal": (()=>findH265Nal),
    "findNal": (()=>findNal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)");
;
var NAL_TYPE_ONE = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
    0x00,
    0x00,
    0x00,
    0x01
]);
var NAL_TYPE_TWO = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
    0x00,
    0x00,
    0x01
]);
var EMULATION_PREVENTION = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
    0x00,
    0x00,
    0x03
]);
var discardEmulationPreventionBytes = function discardEmulationPreventionBytes(bytes) {
    var positions = [];
    var i = 1; // Find all `Emulation Prevention Bytes`
    while(i < bytes.length - 2){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes.subarray(i, i + 3), EMULATION_PREVENTION)) {
            positions.push(i + 2);
            i++;
        }
        i++;
    } // If no Emulation Prevention Bytes were found just return the original
    // array
    if (positions.length === 0) {
        return bytes;
    } // Create a new array to hold the NAL unit data
    var newLength = bytes.length - positions.length;
    var newData = new Uint8Array(newLength);
    var sourceIndex = 0;
    for(i = 0; i < newLength; sourceIndex++, i++){
        if (sourceIndex === positions[0]) {
            // Skip this byte
            sourceIndex++; // Remove this position index
            positions.shift();
        }
        newData[i] = bytes[sourceIndex];
    }
    return newData;
};
var findNal = function findNal(bytes, dataType, types, nalLimit) {
    if (nalLimit === void 0) {
        nalLimit = Infinity;
    }
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    types = [].concat(types);
    var i = 0;
    var nalStart;
    var nalsFound = 0; // keep searching until:
    // we reach the end of bytes
    // we reach the maximum number of nals they want to seach
    // NOTE: that we disregard nalLimit when we have found the start
    // of the nal we want so that we can find the end of the nal we want.
    while(i < bytes.length && (nalsFound < nalLimit || nalStart)){
        var nalOffset = void 0;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes.subarray(i), NAL_TYPE_ONE)) {
            nalOffset = 4;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes.subarray(i), NAL_TYPE_TWO)) {
            nalOffset = 3;
        } // we are unsynced,
        // find the next nal unit
        if (!nalOffset) {
            i++;
            continue;
        }
        nalsFound++;
        if (nalStart) {
            return discardEmulationPreventionBytes(bytes.subarray(nalStart, i));
        }
        var nalType = void 0;
        if (dataType === 'h264') {
            nalType = bytes[i + nalOffset] & 0x1f;
        } else if (dataType === 'h265') {
            nalType = bytes[i + nalOffset] >> 1 & 0x3f;
        }
        if (types.indexOf(nalType) !== -1) {
            nalStart = i + nalOffset;
        } // nal header is 1 length for h264, and 2 for h265
        i += nalOffset + (dataType === 'h264' ? 1 : 2);
    }
    return bytes.subarray(0, 0);
};
var findH264Nal = function findH264Nal(bytes, type, nalLimit) {
    return findNal(bytes, 'h264', type, nalLimit);
};
var findH265Nal = function findH265Nal(bytes, type, nalLimit) {
    return findNal(bytes, 'h265', type, nalLimit);
};
}}),
"[project]/node_modules/@videojs/vhs-utils/es/containers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "detectContainerForBytes": (()=>detectContainerForBytes),
    "isLikely": (()=>isLikely),
    "isLikelyFmp4MediaSegment": (()=>isLikelyFmp4MediaSegment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/byte-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$mp4$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/mp4-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/ebml-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$id3$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/id3-helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$nal$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/nal-helpers.js [app-client] (ecmascript)");
;
;
;
;
;
var CONSTANTS = {
    // "webm" string literal in hex
    'webm': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x77,
        0x65,
        0x62,
        0x6d
    ]),
    // "matroska" string literal in hex
    'matroska': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x6d,
        0x61,
        0x74,
        0x72,
        0x6f,
        0x73,
        0x6b,
        0x61
    ]),
    // "fLaC" string literal in hex
    'flac': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x66,
        0x4c,
        0x61,
        0x43
    ]),
    // "OggS" string literal in hex
    'ogg': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x4f,
        0x67,
        0x67,
        0x53
    ]),
    // ac-3 sync byte, also works for ec-3 as that is simply a codec
    // of ac-3
    'ac3': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x0b,
        0x77
    ]),
    // "RIFF" string literal in hex used for wav and avi
    'riff': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x52,
        0x49,
        0x46,
        0x46
    ]),
    // "AVI" string literal in hex
    'avi': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x41,
        0x56,
        0x49
    ]),
    // "WAVE" string literal in hex
    'wav': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x57,
        0x41,
        0x56,
        0x45
    ]),
    // "ftyp3g" string literal in hex
    '3gp': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x66,
        0x74,
        0x79,
        0x70,
        0x33,
        0x67
    ]),
    // "ftyp" string literal in hex
    'mp4': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x66,
        0x74,
        0x79,
        0x70
    ]),
    // "styp" string literal in hex
    'fmp4': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x73,
        0x74,
        0x79,
        0x70
    ]),
    // "ftypqt" string literal in hex
    'mov': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x66,
        0x74,
        0x79,
        0x70,
        0x71,
        0x74
    ]),
    // moov string literal in hex
    'moov': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x6D,
        0x6F,
        0x6F,
        0x76
    ]),
    // moof string literal in hex
    'moof': (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])([
        0x6D,
        0x6F,
        0x6F,
        0x66
    ])
};
var _isLikely = {
    aac: function aac(bytes) {
        var offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$id3$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getId3Offset"])(bytes);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, [
            0xFF,
            0x10
        ], {
            offset: offset,
            mask: [
                0xFF,
                0x16
            ]
        });
    },
    mp3: function mp3(bytes) {
        var offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$id3$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getId3Offset"])(bytes);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, [
            0xFF,
            0x02
        ], {
            offset: offset,
            mask: [
                0xFF,
                0x06
            ]
        });
    },
    webm: function webm(bytes) {
        var docType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findEbml"])(bytes, [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EBML_TAGS"].EBML,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EBML_TAGS"].DocType
        ])[0]; // check if DocType EBML tag is webm
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(docType, CONSTANTS.webm);
    },
    mkv: function mkv(bytes) {
        var docType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findEbml"])(bytes, [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EBML_TAGS"].EBML,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$ebml$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EBML_TAGS"].DocType
        ])[0]; // check if DocType EBML tag is matroska
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(docType, CONSTANTS.matroska);
    },
    mp4: function mp4(bytes) {
        // if this file is another base media file format, it is not mp4
        if (_isLikely['3gp'](bytes) || _isLikely.mov(bytes)) {
            return false;
        } // if this file starts with a ftyp or styp box its mp4
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.mp4, {
            offset: 4
        }) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.fmp4, {
            offset: 4
        })) {
            return true;
        } // if this file starts with a moof/moov box its mp4
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.moof, {
            offset: 4
        }) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.moov, {
            offset: 4
        })) {
            return true;
        }
    },
    mov: function mov(bytes) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.mov, {
            offset: 4
        });
    },
    '3gp': function gp(bytes) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS['3gp'], {
            offset: 4
        });
    },
    ac3: function ac3(bytes) {
        var offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$id3$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getId3Offset"])(bytes);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.ac3, {
            offset: offset
        });
    },
    ts: function ts(bytes) {
        if (bytes.length < 189 && bytes.length >= 1) {
            return bytes[0] === 0x47;
        }
        var i = 0; // check the first 376 bytes for two matching sync bytes
        while(i + 188 < bytes.length && i < 188){
            if (bytes[i] === 0x47 && bytes[i + 188] === 0x47) {
                return true;
            }
            i += 1;
        }
        return false;
    },
    flac: function flac(bytes) {
        var offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$id3$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getId3Offset"])(bytes);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.flac, {
            offset: offset
        });
    },
    ogg: function ogg(bytes) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.ogg);
    },
    avi: function avi(bytes) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.riff) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.avi, {
            offset: 8
        });
    },
    wav: function wav(bytes) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.riff) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bytesMatch"])(bytes, CONSTANTS.wav, {
            offset: 8
        });
    },
    'h264': function h264(bytes) {
        // find seq_parameter_set_rbsp
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$nal$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findH264Nal"])(bytes, 7, 3).length;
    },
    'h265': function h265(bytes) {
        // find video_parameter_set_rbsp or seq_parameter_set_rbsp
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$nal$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findH265Nal"])(bytes, [
            32,
            33
        ], 3).length;
    }
}; // get all the isLikely functions
// but make sure 'ts' is above h264 and h265
// but below everything else as it is the least specific
var isLikelyTypes = Object.keys(_isLikely) // remove ts, h264, h265
.filter(function(t) {
    return t !== 'ts' && t !== 'h264' && t !== 'h265';
}) // add it back to the bottom
.concat([
    'ts',
    'h264',
    'h265'
]); // make sure we are dealing with uint8 data.
isLikelyTypes.forEach(function(type) {
    var isLikelyFn = _isLikely[type];
    _isLikely[type] = function(bytes) {
        return isLikelyFn((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes));
    };
}); // export after wrapping
var isLikely = _isLikely; // A useful list of file signatures can be found here
var detectContainerForBytes = function detectContainerForBytes(bytes) {
    bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$byte$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toUint8"])(bytes);
    for(var i = 0; i < isLikelyTypes.length; i++){
        var type = isLikelyTypes[i];
        if (isLikely[type](bytes)) {
            return type;
        }
    }
    return '';
}; // fmp4 is not a container
var isLikelyFmp4MediaSegment = function isLikelyFmp4MediaSegment(bytes) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$mp4$2d$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findBox"])(bytes, [
        'moof'
    ]).length > 0;
};
}}),
"[project]/node_modules/m3u8-parser/dist/m3u8-parser.es.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/*! @name m3u8-parser @version 7.2.0 @license Apache-2.0 */ __turbopack_esm__({
    "LineStream": (()=>LineStream),
    "ParseStream": (()=>ParseStream),
    "Parser": (()=>Parser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$stream$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/stream.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$decode$2d$b64$2d$to$2d$uint8$2d$array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/decode-b64-to-uint8-array.js [app-client] (ecmascript)");
;
;
;
/**
 * @file m3u8/line-stream.js
 */ /**
 * A stream that buffers string input and generates a `data` event for each
 * line.
 *
 * @class LineStream
 * @extends Stream
 */ class LineStream extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$stream$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super();
        this.buffer = '';
    }
    /**
   * Add new data to be parsed.
   *
   * @param {string} data the text to process
   */ push(data) {
        let nextNewline;
        this.buffer += data;
        nextNewline = this.buffer.indexOf('\n');
        for(; nextNewline > -1; nextNewline = this.buffer.indexOf('\n')){
            this.trigger('data', this.buffer.substring(0, nextNewline));
            this.buffer = this.buffer.substring(nextNewline + 1);
        }
    }
}
const TAB = String.fromCharCode(0x09);
const parseByterange = function(byterangeString) {
    // optionally match and capture 0+ digits before `@`
    // optionally match and capture 0+ digits after `@`
    const match = /([0-9.]*)?@?([0-9.]*)?/.exec(byterangeString || '');
    const result = {};
    if (match[1]) {
        result.length = parseInt(match[1], 10);
    }
    if (match[2]) {
        result.offset = parseInt(match[2], 10);
    }
    return result;
};
/**
 * "forgiving" attribute list psuedo-grammar:
 * attributes -> keyvalue (',' keyvalue)*
 * keyvalue   -> key '=' value
 * key        -> [^=]*
 * value      -> '"' [^"]* '"' | [^,]*
 */ const attributeSeparator = function() {
    const key = '[^=]*';
    const value = '"[^"]*"|[^,]*';
    const keyvalue = '(?:' + key + ')=(?:' + value + ')';
    return new RegExp('(?:^|,)(' + keyvalue + ')');
};
/**
 * Parse attributes from a line given the separator
 *
 * @param {string} attributes the attribute line to parse
 */ const parseAttributes = function(attributes) {
    const result = {};
    if (!attributes) {
        return result;
    } // split the string using attributes as the separator
    const attrs = attributes.split(attributeSeparator());
    let i = attrs.length;
    let attr;
    while(i--){
        // filter out unmatched portions of the string
        if (attrs[i] === '') {
            continue;
        } // split the key and value
        attr = /([^=]*)=(.*)/.exec(attrs[i]).slice(1); // trim whitespace and remove optional quotes around the value
        attr[0] = attr[0].replace(/^\s+|\s+$/g, '');
        attr[1] = attr[1].replace(/^\s+|\s+$/g, '');
        attr[1] = attr[1].replace(/^['"](.*)['"]$/g, '$1');
        result[attr[0]] = attr[1];
    }
    return result;
};
/**
 * Converts a string into a resolution object
 *
 * @param {string} resolution a string such as 3840x2160
 *
 * @return {Object} An object representing the resolution
 *
 */ const parseResolution = (resolution)=>{
    const split = resolution.split('x');
    const result = {};
    if (split[0]) {
        result.width = parseInt(split[0], 10);
    }
    if (split[1]) {
        result.height = parseInt(split[1], 10);
    }
    return result;
};
/**
 * A line-level M3U8 parser event stream. It expects to receive input one
 * line at a time and performs a context-free parse of its contents. A stream
 * interpretation of a manifest can be useful if the manifest is expected to
 * be too large to fit comfortably into memory or the entirety of the input
 * is not immediately available. Otherwise, it's probably much easier to work
 * with a regular `Parser` object.
 *
 * Produces `data` events with an object that captures the parser's
 * interpretation of the input. That object has a property `tag` that is one
 * of `uri`, `comment`, or `tag`. URIs only have a single additional
 * property, `line`, which captures the entirety of the input without
 * interpretation. Comments similarly have a single additional property
 * `text` which is the input without the leading `#`.
 *
 * Tags always have a property `tagType` which is the lower-cased version of
 * the M3U8 directive without the `#EXT` or `#EXT-X-` prefix. For instance,
 * `#EXT-X-MEDIA-SEQUENCE` becomes `media-sequence` when parsed. Unrecognized
 * tags are given the tag type `unknown` and a single additional property
 * `data` with the remainder of the input.
 *
 * @class ParseStream
 * @extends Stream
 */ class ParseStream extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$stream$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super();
        this.customParsers = [];
        this.tagMappers = [];
    }
    /**
   * Parses an additional line of input.
   *
   * @param {string} line a single line of an M3U8 file to parse
   */ push(line) {
        let match;
        let event; // strip whitespace
        line = line.trim();
        if (line.length === 0) {
            // ignore empty lines
            return;
        } // URIs
        if (line[0] !== '#') {
            this.trigger('data', {
                type: 'uri',
                uri: line
            });
            return;
        } // map tags
        const newLines = this.tagMappers.reduce((acc, mapper)=>{
            const mappedLine = mapper(line); // skip if unchanged
            if (mappedLine === line) {
                return acc;
            }
            return acc.concat([
                mappedLine
            ]);
        }, [
            line
        ]);
        newLines.forEach((newLine)=>{
            for(let i = 0; i < this.customParsers.length; i++){
                if (this.customParsers[i].call(this, newLine)) {
                    return;
                }
            } // Comments
            if (newLine.indexOf('#EXT') !== 0) {
                this.trigger('data', {
                    type: 'comment',
                    text: newLine.slice(1)
                });
                return;
            } // strip off any carriage returns here so the regex matching
            // doesn't have to account for them.
            newLine = newLine.replace('\r', ''); // Tags
            match = /^#EXTM3U/.exec(newLine);
            if (match) {
                this.trigger('data', {
                    type: 'tag',
                    tagType: 'm3u'
                });
                return;
            }
            match = /^#EXTINF:([0-9\.]*)?,?(.*)?$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'inf'
                };
                if (match[1]) {
                    event.duration = parseFloat(match[1]);
                }
                if (match[2]) {
                    event.title = match[2];
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-TARGETDURATION:([0-9.]*)?/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'targetduration'
                };
                if (match[1]) {
                    event.duration = parseInt(match[1], 10);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-VERSION:([0-9.]*)?/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'version'
                };
                if (match[1]) {
                    event.version = parseInt(match[1], 10);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-MEDIA-SEQUENCE:(\-?[0-9.]*)?/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'media-sequence'
                };
                if (match[1]) {
                    event.number = parseInt(match[1], 10);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-DISCONTINUITY-SEQUENCE:(\-?[0-9.]*)?/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'discontinuity-sequence'
                };
                if (match[1]) {
                    event.number = parseInt(match[1], 10);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-PLAYLIST-TYPE:(.*)?$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'playlist-type'
                };
                if (match[1]) {
                    event.playlistType = match[1];
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-BYTERANGE:(.*)?$/.exec(newLine);
            if (match) {
                event = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(parseByterange(match[1]), {
                    type: 'tag',
                    tagType: 'byterange'
                });
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-ALLOW-CACHE:(YES|NO)?/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'allow-cache'
                };
                if (match[1]) {
                    event.allowed = !/NO/.test(match[1]);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-MAP:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'map'
                };
                if (match[1]) {
                    const attributes = parseAttributes(match[1]);
                    if (attributes.URI) {
                        event.uri = attributes.URI;
                    }
                    if (attributes.BYTERANGE) {
                        event.byterange = parseByterange(attributes.BYTERANGE);
                    }
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-STREAM-INF:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'stream-inf'
                };
                if (match[1]) {
                    event.attributes = parseAttributes(match[1]);
                    if (event.attributes.RESOLUTION) {
                        event.attributes.RESOLUTION = parseResolution(event.attributes.RESOLUTION);
                    }
                    if (event.attributes.BANDWIDTH) {
                        event.attributes.BANDWIDTH = parseInt(event.attributes.BANDWIDTH, 10);
                    }
                    if (event.attributes['FRAME-RATE']) {
                        event.attributes['FRAME-RATE'] = parseFloat(event.attributes['FRAME-RATE']);
                    }
                    if (event.attributes['PROGRAM-ID']) {
                        event.attributes['PROGRAM-ID'] = parseInt(event.attributes['PROGRAM-ID'], 10);
                    }
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-MEDIA:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'media'
                };
                if (match[1]) {
                    event.attributes = parseAttributes(match[1]);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-ENDLIST/.exec(newLine);
            if (match) {
                this.trigger('data', {
                    type: 'tag',
                    tagType: 'endlist'
                });
                return;
            }
            match = /^#EXT-X-DISCONTINUITY/.exec(newLine);
            if (match) {
                this.trigger('data', {
                    type: 'tag',
                    tagType: 'discontinuity'
                });
                return;
            }
            match = /^#EXT-X-PROGRAM-DATE-TIME:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'program-date-time'
                };
                if (match[1]) {
                    event.dateTimeString = match[1];
                    event.dateTimeObject = new Date(match[1]);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-KEY:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'key'
                };
                if (match[1]) {
                    event.attributes = parseAttributes(match[1]); // parse the IV string into a Uint32Array
                    if (event.attributes.IV) {
                        if (event.attributes.IV.substring(0, 2).toLowerCase() === '0x') {
                            event.attributes.IV = event.attributes.IV.substring(2);
                        }
                        event.attributes.IV = event.attributes.IV.match(/.{8}/g);
                        event.attributes.IV[0] = parseInt(event.attributes.IV[0], 16);
                        event.attributes.IV[1] = parseInt(event.attributes.IV[1], 16);
                        event.attributes.IV[2] = parseInt(event.attributes.IV[2], 16);
                        event.attributes.IV[3] = parseInt(event.attributes.IV[3], 16);
                        event.attributes.IV = new Uint32Array(event.attributes.IV);
                    }
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-START:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'start'
                };
                if (match[1]) {
                    event.attributes = parseAttributes(match[1]);
                    event.attributes['TIME-OFFSET'] = parseFloat(event.attributes['TIME-OFFSET']);
                    event.attributes.PRECISE = /YES/.test(event.attributes.PRECISE);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-CUE-OUT-CONT:(.*)?$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'cue-out-cont'
                };
                if (match[1]) {
                    event.data = match[1];
                } else {
                    event.data = '';
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-CUE-OUT:(.*)?$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'cue-out'
                };
                if (match[1]) {
                    event.data = match[1];
                } else {
                    event.data = '';
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-CUE-IN:?(.*)?$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'cue-in'
                };
                if (match[1]) {
                    event.data = match[1];
                } else {
                    event.data = '';
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-SKIP:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'skip'
                };
                event.attributes = parseAttributes(match[1]);
                if (event.attributes.hasOwnProperty('SKIPPED-SEGMENTS')) {
                    event.attributes['SKIPPED-SEGMENTS'] = parseInt(event.attributes['SKIPPED-SEGMENTS'], 10);
                }
                if (event.attributes.hasOwnProperty('RECENTLY-REMOVED-DATERANGES')) {
                    event.attributes['RECENTLY-REMOVED-DATERANGES'] = event.attributes['RECENTLY-REMOVED-DATERANGES'].split(TAB);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-PART:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'part'
                };
                event.attributes = parseAttributes(match[1]);
                [
                    'DURATION'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = parseFloat(event.attributes[key]);
                    }
                });
                [
                    'INDEPENDENT',
                    'GAP'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = /YES/.test(event.attributes[key]);
                    }
                });
                if (event.attributes.hasOwnProperty('BYTERANGE')) {
                    event.attributes.byterange = parseByterange(event.attributes.BYTERANGE);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-SERVER-CONTROL:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'server-control'
                };
                event.attributes = parseAttributes(match[1]);
                [
                    'CAN-SKIP-UNTIL',
                    'PART-HOLD-BACK',
                    'HOLD-BACK'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = parseFloat(event.attributes[key]);
                    }
                });
                [
                    'CAN-SKIP-DATERANGES',
                    'CAN-BLOCK-RELOAD'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = /YES/.test(event.attributes[key]);
                    }
                });
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-PART-INF:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'part-inf'
                };
                event.attributes = parseAttributes(match[1]);
                [
                    'PART-TARGET'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = parseFloat(event.attributes[key]);
                    }
                });
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-PRELOAD-HINT:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'preload-hint'
                };
                event.attributes = parseAttributes(match[1]);
                [
                    'BYTERANGE-START',
                    'BYTERANGE-LENGTH'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = parseInt(event.attributes[key], 10);
                        const subkey = key === 'BYTERANGE-LENGTH' ? 'length' : 'offset';
                        event.attributes.byterange = event.attributes.byterange || {};
                        event.attributes.byterange[subkey] = event.attributes[key]; // only keep the parsed byterange object.
                        delete event.attributes[key];
                    }
                });
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-RENDITION-REPORT:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'rendition-report'
                };
                event.attributes = parseAttributes(match[1]);
                [
                    'LAST-MSN',
                    'LAST-PART'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = parseInt(event.attributes[key], 10);
                    }
                });
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-DATERANGE:(.*)$/.exec(newLine);
            if (match && match[1]) {
                event = {
                    type: 'tag',
                    tagType: 'daterange'
                };
                event.attributes = parseAttributes(match[1]);
                [
                    'ID',
                    'CLASS'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = String(event.attributes[key]);
                    }
                });
                [
                    'START-DATE',
                    'END-DATE'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = new Date(event.attributes[key]);
                    }
                });
                [
                    'DURATION',
                    'PLANNED-DURATION'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = parseFloat(event.attributes[key]);
                    }
                });
                [
                    'END-ON-NEXT'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = /YES/i.test(event.attributes[key]);
                    }
                });
                [
                    'SCTE35-CMD',
                    ' SCTE35-OUT',
                    'SCTE35-IN'
                ].forEach(function(key) {
                    if (event.attributes.hasOwnProperty(key)) {
                        event.attributes[key] = event.attributes[key].toString(16);
                    }
                });
                const clientAttributePattern = /^X-([A-Z]+-)+[A-Z]+$/;
                for(const key in event.attributes){
                    if (!clientAttributePattern.test(key)) {
                        continue;
                    }
                    const isHexaDecimal = /[0-9A-Fa-f]{6}/g.test(event.attributes[key]);
                    const isDecimalFloating = /^\d+(\.\d+)?$/.test(event.attributes[key]);
                    event.attributes[key] = isHexaDecimal ? event.attributes[key].toString(16) : isDecimalFloating ? parseFloat(event.attributes[key]) : String(event.attributes[key]);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-INDEPENDENT-SEGMENTS/.exec(newLine);
            if (match) {
                this.trigger('data', {
                    type: 'tag',
                    tagType: 'independent-segments'
                });
                return;
            }
            match = /^#EXT-X-I-FRAMES-ONLY/.exec(newLine);
            if (match) {
                this.trigger('data', {
                    type: 'tag',
                    tagType: 'i-frames-only'
                });
                return;
            }
            match = /^#EXT-X-CONTENT-STEERING:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'content-steering'
                };
                event.attributes = parseAttributes(match[1]);
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-I-FRAME-STREAM-INF:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'i-frame-playlist'
                };
                event.attributes = parseAttributes(match[1]);
                if (event.attributes.URI) {
                    event.uri = event.attributes.URI;
                }
                if (event.attributes.BANDWIDTH) {
                    event.attributes.BANDWIDTH = parseInt(event.attributes.BANDWIDTH, 10);
                }
                if (event.attributes.RESOLUTION) {
                    event.attributes.RESOLUTION = parseResolution(event.attributes.RESOLUTION);
                }
                if (event.attributes['AVERAGE-BANDWIDTH']) {
                    event.attributes['AVERAGE-BANDWIDTH'] = parseInt(event.attributes['AVERAGE-BANDWIDTH'], 10);
                }
                if (event.attributes['FRAME-RATE']) {
                    event.attributes['FRAME-RATE'] = parseFloat(event.attributes['FRAME-RATE']);
                }
                this.trigger('data', event);
                return;
            }
            match = /^#EXT-X-DEFINE:(.*)$/.exec(newLine);
            if (match) {
                event = {
                    type: 'tag',
                    tagType: 'define'
                };
                event.attributes = parseAttributes(match[1]);
                this.trigger('data', event);
                return;
            } // unknown tag type
            this.trigger('data', {
                type: 'tag',
                data: newLine.slice(4)
            });
        });
    }
    /**
   * Add a parser for custom headers
   *
   * @param {Object}   options              a map of options for the added parser
   * @param {RegExp}   options.expression   a regular expression to match the custom header
   * @param {string}   options.customType   the custom type to register to the output
   * @param {Function} [options.dataParser] function to parse the line into an object
   * @param {boolean}  [options.segment]    should tag data be attached to the segment object
   */ addParser({ expression, customType, dataParser, segment }) {
        if (typeof dataParser !== 'function') {
            dataParser = (line)=>line;
        }
        this.customParsers.push((line)=>{
            const match = expression.exec(line);
            if (match) {
                this.trigger('data', {
                    type: 'custom',
                    data: dataParser(line),
                    customType,
                    segment
                });
                return true;
            }
        });
    }
    /**
   * Add a custom header mapper
   *
   * @param {Object}   options
   * @param {RegExp}   options.expression   a regular expression to match the custom header
   * @param {Function} options.map          function to translate tag into a different tag
   */ addTagMapper({ expression, map }) {
        const mapFn = (line)=>{
            if (expression.test(line)) {
                return map(line);
            }
            return line;
        };
        this.tagMappers.push(mapFn);
    }
}
const camelCase = (str)=>str.toLowerCase().replace(/-(\w)/g, (a)=>a[1].toUpperCase());
const camelCaseKeys = function(attributes) {
    const result = {};
    Object.keys(attributes).forEach(function(key) {
        result[camelCase(key)] = attributes[key];
    });
    return result;
}; // set SERVER-CONTROL hold back based upon targetDuration and partTargetDuration
// we need this helper because defaults are based upon targetDuration and
// partTargetDuration being set, but they may not be if SERVER-CONTROL appears before
// target durations are set.
const setHoldBack = function(manifest) {
    const { serverControl, targetDuration, partTargetDuration } = manifest;
    if (!serverControl) {
        return;
    }
    const tag = '#EXT-X-SERVER-CONTROL';
    const hb = 'holdBack';
    const phb = 'partHoldBack';
    const minTargetDuration = targetDuration && targetDuration * 3;
    const minPartDuration = partTargetDuration && partTargetDuration * 2;
    if (targetDuration && !serverControl.hasOwnProperty(hb)) {
        serverControl[hb] = minTargetDuration;
        this.trigger('info', {
            message: `${tag} defaulting HOLD-BACK to targetDuration * 3 (${minTargetDuration}).`
        });
    }
    if (minTargetDuration && serverControl[hb] < minTargetDuration) {
        this.trigger('warn', {
            message: `${tag} clamping HOLD-BACK (${serverControl[hb]}) to targetDuration * 3 (${minTargetDuration})`
        });
        serverControl[hb] = minTargetDuration;
    } // default no part hold back to part target duration * 3
    if (partTargetDuration && !serverControl.hasOwnProperty(phb)) {
        serverControl[phb] = partTargetDuration * 3;
        this.trigger('info', {
            message: `${tag} defaulting PART-HOLD-BACK to partTargetDuration * 3 (${serverControl[phb]}).`
        });
    } // if part hold back is too small default it to part target duration * 2
    if (partTargetDuration && serverControl[phb] < minPartDuration) {
        this.trigger('warn', {
            message: `${tag} clamping PART-HOLD-BACK (${serverControl[phb]}) to partTargetDuration * 2 (${minPartDuration}).`
        });
        serverControl[phb] = minPartDuration;
    }
};
/**
 * A parser for M3U8 files. The current interpretation of the input is
 * exposed as a property `manifest` on parser objects. It's just two lines to
 * create and parse a manifest once you have the contents available as a string:
 *
 * ```js
 * var parser = new m3u8.Parser();
 * parser.push(xhr.responseText);
 * ```
 *
 * New input can later be applied to update the manifest object by calling
 * `push` again.
 *
 * The parser attempts to create a usable manifest object even if the
 * underlying input is somewhat nonsensical. It emits `info` and `warning`
 * events during the parse if it encounters input that seems invalid or
 * requires some property of the manifest object to be defaulted.
 *
 * @class Parser
 * @param {Object} [opts] Options for the constructor, needed for substitutions
 * @param {string} [opts.uri] URL to check for query params
 * @param {Object} [opts.mainDefinitions] Definitions on main playlist that can be imported
 * @extends Stream
 */ class Parser extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$stream$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    constructor(opts = {}){
        super();
        this.lineStream = new LineStream();
        this.parseStream = new ParseStream();
        this.lineStream.pipe(this.parseStream);
        this.mainDefinitions = opts.mainDefinitions || {};
        this.params = new URL(opts.uri, 'https://a.com').searchParams;
        this.lastProgramDateTime = null;
        /* eslint-disable consistent-this */ const self = this;
        /* eslint-enable consistent-this */ const uris = [];
        let currentUri = {}; // if specified, the active EXT-X-MAP definition
        let currentMap; // if specified, the active decryption key
        let key;
        let hasParts = false;
        const noop = function() {};
        const defaultMediaGroups = {
            'AUDIO': {},
            'VIDEO': {},
            'CLOSED-CAPTIONS': {},
            'SUBTITLES': {}
        }; // This is the Widevine UUID from DASH IF IOP. The same exact string is
        // used in MPDs with Widevine encrypted streams.
        const widevineUuid = 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'; // group segments into numbered timelines delineated by discontinuities
        let currentTimeline = 0; // the manifest is empty until the parse stream begins delivering data
        this.manifest = {
            allowCache: true,
            discontinuityStarts: [],
            dateRanges: [],
            iFramePlaylists: [],
            segments: []
        }; // keep track of the last seen segment's byte range end, as segments are not required
        // to provide the offset, in which case it defaults to the next byte after the
        // previous segment
        let lastByterangeEnd = 0; // keep track of the last seen part's byte range end.
        let lastPartByterangeEnd = 0;
        const dateRangeTags = {};
        this.on('end', ()=>{
            // only add preloadSegment if we don't yet have a uri for it.
            // and we actually have parts/preloadHints
            if (currentUri.uri || !currentUri.parts && !currentUri.preloadHints) {
                return;
            }
            if (!currentUri.map && currentMap) {
                currentUri.map = currentMap;
            }
            if (!currentUri.key && key) {
                currentUri.key = key;
            }
            if (!currentUri.timeline && typeof currentTimeline === 'number') {
                currentUri.timeline = currentTimeline;
            }
            this.manifest.preloadSegment = currentUri;
        }); // update the manifest with the m3u8 entry from the parse stream
        this.parseStream.on('data', function(entry) {
            let mediaGroup;
            let rendition; // Replace variables in uris and attributes as defined in #EXT-X-DEFINE tags
            if (self.manifest.definitions) {
                for(const def in self.manifest.definitions){
                    if (entry.uri) {
                        entry.uri = entry.uri.replace(`{$${def}}`, self.manifest.definitions[def]);
                    }
                    if (entry.attributes) {
                        for(const attr in entry.attributes){
                            if (typeof entry.attributes[attr] === 'string') {
                                entry.attributes[attr] = entry.attributes[attr].replace(`{$${def}}`, self.manifest.definitions[def]);
                            }
                        }
                    }
                }
            }
            ({
                tag () {
                    // switch based on the tag type
                    (({
                        version () {
                            if (entry.version) {
                                this.manifest.version = entry.version;
                            }
                        },
                        'allow-cache' () {
                            this.manifest.allowCache = entry.allowed;
                            if (!('allowed' in entry)) {
                                this.trigger('info', {
                                    message: 'defaulting allowCache to YES'
                                });
                                this.manifest.allowCache = true;
                            }
                        },
                        byterange () {
                            const byterange = {};
                            if ('length' in entry) {
                                currentUri.byterange = byterange;
                                byterange.length = entry.length;
                                if (!('offset' in entry)) {
                                    /*
                   * From the latest spec (as of this writing):
                   * https://tools.ietf.org/html/draft-pantos-http-live-streaming-23#section-4.3.2.2
                   *
                   * Same text since EXT-X-BYTERANGE's introduction in draft 7:
                   * https://tools.ietf.org/html/draft-pantos-http-live-streaming-07#section-3.3.1)
                   *
                   * "If o [offset] is not present, the sub-range begins at the next byte
                   * following the sub-range of the previous media segment."
                   */ entry.offset = lastByterangeEnd;
                                }
                            }
                            if ('offset' in entry) {
                                currentUri.byterange = byterange;
                                byterange.offset = entry.offset;
                            }
                            lastByterangeEnd = byterange.offset + byterange.length;
                        },
                        endlist () {
                            this.manifest.endList = true;
                        },
                        inf () {
                            if (!('mediaSequence' in this.manifest)) {
                                this.manifest.mediaSequence = 0;
                                this.trigger('info', {
                                    message: 'defaulting media sequence to zero'
                                });
                            }
                            if (!('discontinuitySequence' in this.manifest)) {
                                this.manifest.discontinuitySequence = 0;
                                this.trigger('info', {
                                    message: 'defaulting discontinuity sequence to zero'
                                });
                            }
                            if (entry.title) {
                                currentUri.title = entry.title;
                            }
                            if (entry.duration > 0) {
                                currentUri.duration = entry.duration;
                            }
                            if (entry.duration === 0) {
                                currentUri.duration = 0.01;
                                this.trigger('info', {
                                    message: 'updating zero segment duration to a small value'
                                });
                            }
                            this.manifest.segments = uris;
                        },
                        key () {
                            if (!entry.attributes) {
                                this.trigger('warn', {
                                    message: 'ignoring key declaration without attribute list'
                                });
                                return;
                            } // clear the active encryption key
                            if (entry.attributes.METHOD === 'NONE') {
                                key = null;
                                return;
                            }
                            if (!entry.attributes.URI) {
                                this.trigger('warn', {
                                    message: 'ignoring key declaration without URI'
                                });
                                return;
                            }
                            if (entry.attributes.KEYFORMAT === 'com.apple.streamingkeydelivery') {
                                this.manifest.contentProtection = this.manifest.contentProtection || {}; // TODO: add full support for this.
                                this.manifest.contentProtection['com.apple.fps.1_0'] = {
                                    attributes: entry.attributes
                                };
                                return;
                            }
                            if (entry.attributes.KEYFORMAT === 'com.microsoft.playready') {
                                this.manifest.contentProtection = this.manifest.contentProtection || {}; // TODO: add full support for this.
                                this.manifest.contentProtection['com.microsoft.playready'] = {
                                    uri: entry.attributes.URI
                                };
                                return;
                            } // check if the content is encrypted for Widevine
                            // Widevine/HLS spec: https://storage.googleapis.com/wvdocs/Widevine_DRM_HLS.pdf
                            if (entry.attributes.KEYFORMAT === widevineUuid) {
                                const VALID_METHODS = [
                                    'SAMPLE-AES',
                                    'SAMPLE-AES-CTR',
                                    'SAMPLE-AES-CENC'
                                ];
                                if (VALID_METHODS.indexOf(entry.attributes.METHOD) === -1) {
                                    this.trigger('warn', {
                                        message: 'invalid key method provided for Widevine'
                                    });
                                    return;
                                }
                                if (entry.attributes.METHOD === 'SAMPLE-AES-CENC') {
                                    this.trigger('warn', {
                                        message: 'SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead'
                                    });
                                }
                                if (entry.attributes.URI.substring(0, 23) !== 'data:text/plain;base64,') {
                                    this.trigger('warn', {
                                        message: 'invalid key URI provided for Widevine'
                                    });
                                    return;
                                }
                                if (!(entry.attributes.KEYID && entry.attributes.KEYID.substring(0, 2) === '0x')) {
                                    this.trigger('warn', {
                                        message: 'invalid key ID provided for Widevine'
                                    });
                                    return;
                                } // if Widevine key attributes are valid, store them as `contentProtection`
                                // on the manifest to emulate Widevine tag structure in a DASH mpd
                                this.manifest.contentProtection = this.manifest.contentProtection || {};
                                this.manifest.contentProtection['com.widevine.alpha'] = {
                                    attributes: {
                                        schemeIdUri: entry.attributes.KEYFORMAT,
                                        // remove '0x' from the key id string
                                        keyId: entry.attributes.KEYID.substring(2)
                                    },
                                    // decode the base64-encoded PSSH box
                                    pssh: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$decode$2d$b64$2d$to$2d$uint8$2d$array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(entry.attributes.URI.split(',')[1])
                                };
                                return;
                            }
                            if (!entry.attributes.METHOD) {
                                this.trigger('warn', {
                                    message: 'defaulting key method to AES-128'
                                });
                            } // setup an encryption key for upcoming segments
                            key = {
                                method: entry.attributes.METHOD || 'AES-128',
                                uri: entry.attributes.URI
                            };
                            if (typeof entry.attributes.IV !== 'undefined') {
                                key.iv = entry.attributes.IV;
                            }
                        },
                        'media-sequence' () {
                            if (!isFinite(entry.number)) {
                                this.trigger('warn', {
                                    message: 'ignoring invalid media sequence: ' + entry.number
                                });
                                return;
                            }
                            this.manifest.mediaSequence = entry.number;
                        },
                        'discontinuity-sequence' () {
                            if (!isFinite(entry.number)) {
                                this.trigger('warn', {
                                    message: 'ignoring invalid discontinuity sequence: ' + entry.number
                                });
                                return;
                            }
                            this.manifest.discontinuitySequence = entry.number;
                            currentTimeline = entry.number;
                        },
                        'playlist-type' () {
                            if (!/VOD|EVENT/.test(entry.playlistType)) {
                                this.trigger('warn', {
                                    message: 'ignoring unknown playlist type: ' + entry.playlist
                                });
                                return;
                            }
                            this.manifest.playlistType = entry.playlistType;
                        },
                        map () {
                            currentMap = {};
                            if (entry.uri) {
                                currentMap.uri = entry.uri;
                            }
                            if (entry.byterange) {
                                currentMap.byterange = entry.byterange;
                            }
                            if (key) {
                                currentMap.key = key;
                            }
                        },
                        'stream-inf' () {
                            this.manifest.playlists = uris;
                            this.manifest.mediaGroups = this.manifest.mediaGroups || defaultMediaGroups;
                            if (!entry.attributes) {
                                this.trigger('warn', {
                                    message: 'ignoring empty stream-inf attributes'
                                });
                                return;
                            }
                            if (!currentUri.attributes) {
                                currentUri.attributes = {};
                            }
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currentUri.attributes, entry.attributes);
                        },
                        media () {
                            this.manifest.mediaGroups = this.manifest.mediaGroups || defaultMediaGroups;
                            if (!(entry.attributes && entry.attributes.TYPE && entry.attributes['GROUP-ID'] && entry.attributes.NAME)) {
                                this.trigger('warn', {
                                    message: 'ignoring incomplete or missing media group'
                                });
                                return;
                            } // find the media group, creating defaults as necessary
                            const mediaGroupType = this.manifest.mediaGroups[entry.attributes.TYPE];
                            mediaGroupType[entry.attributes['GROUP-ID']] = mediaGroupType[entry.attributes['GROUP-ID']] || {};
                            mediaGroup = mediaGroupType[entry.attributes['GROUP-ID']]; // collect the rendition metadata
                            rendition = {
                                default: /yes/i.test(entry.attributes.DEFAULT)
                            };
                            if (rendition.default) {
                                rendition.autoselect = true;
                            } else {
                                rendition.autoselect = /yes/i.test(entry.attributes.AUTOSELECT);
                            }
                            if (entry.attributes.LANGUAGE) {
                                rendition.language = entry.attributes.LANGUAGE;
                            }
                            if (entry.attributes.URI) {
                                rendition.uri = entry.attributes.URI;
                            }
                            if (entry.attributes['INSTREAM-ID']) {
                                rendition.instreamId = entry.attributes['INSTREAM-ID'];
                            }
                            if (entry.attributes.CHARACTERISTICS) {
                                rendition.characteristics = entry.attributes.CHARACTERISTICS;
                            }
                            if (entry.attributes.FORCED) {
                                rendition.forced = /yes/i.test(entry.attributes.FORCED);
                            } // insert the new rendition
                            mediaGroup[entry.attributes.NAME] = rendition;
                        },
                        discontinuity () {
                            currentTimeline += 1;
                            currentUri.discontinuity = true;
                            this.manifest.discontinuityStarts.push(uris.length);
                        },
                        'program-date-time' () {
                            if (typeof this.manifest.dateTimeString === 'undefined') {
                                // PROGRAM-DATE-TIME is a media-segment tag, but for backwards
                                // compatibility, we add the first occurence of the PROGRAM-DATE-TIME tag
                                // to the manifest object
                                // TODO: Consider removing this in future major version
                                this.manifest.dateTimeString = entry.dateTimeString;
                                this.manifest.dateTimeObject = entry.dateTimeObject;
                            }
                            currentUri.dateTimeString = entry.dateTimeString;
                            currentUri.dateTimeObject = entry.dateTimeObject;
                            const { lastProgramDateTime } = this;
                            this.lastProgramDateTime = new Date(entry.dateTimeString).getTime(); // We should extrapolate Program Date Time backward only during first program date time occurrence.
                            // Once we have at least one program date time point, we can always extrapolate it forward using lastProgramDateTime reference.
                            if (lastProgramDateTime === null) {
                                // Extrapolate Program Date Time backward
                                // Since it is first program date time occurrence we're assuming that
                                // all this.manifest.segments have no program date time info
                                this.manifest.segments.reduceRight((programDateTime, segment)=>{
                                    segment.programDateTime = programDateTime - segment.duration * 1000;
                                    return segment.programDateTime;
                                }, this.lastProgramDateTime);
                            }
                        },
                        targetduration () {
                            if (!isFinite(entry.duration) || entry.duration < 0) {
                                this.trigger('warn', {
                                    message: 'ignoring invalid target duration: ' + entry.duration
                                });
                                return;
                            }
                            this.manifest.targetDuration = entry.duration;
                            setHoldBack.call(this, this.manifest);
                        },
                        start () {
                            if (!entry.attributes || isNaN(entry.attributes['TIME-OFFSET'])) {
                                this.trigger('warn', {
                                    message: 'ignoring start declaration without appropriate attribute list'
                                });
                                return;
                            }
                            this.manifest.start = {
                                timeOffset: entry.attributes['TIME-OFFSET'],
                                precise: entry.attributes.PRECISE
                            };
                        },
                        'cue-out' () {
                            currentUri.cueOut = entry.data;
                        },
                        'cue-out-cont' () {
                            currentUri.cueOutCont = entry.data;
                        },
                        'cue-in' () {
                            currentUri.cueIn = entry.data;
                        },
                        'skip' () {
                            this.manifest.skip = camelCaseKeys(entry.attributes);
                            this.warnOnMissingAttributes_('#EXT-X-SKIP', entry.attributes, [
                                'SKIPPED-SEGMENTS'
                            ]);
                        },
                        'part' () {
                            hasParts = true; // parts are always specifed before a segment
                            const segmentIndex = this.manifest.segments.length;
                            const part = camelCaseKeys(entry.attributes);
                            currentUri.parts = currentUri.parts || [];
                            currentUri.parts.push(part);
                            if (part.byterange) {
                                if (!part.byterange.hasOwnProperty('offset')) {
                                    part.byterange.offset = lastPartByterangeEnd;
                                }
                                lastPartByterangeEnd = part.byterange.offset + part.byterange.length;
                            }
                            const partIndex = currentUri.parts.length - 1;
                            this.warnOnMissingAttributes_(`#EXT-X-PART #${partIndex} for segment #${segmentIndex}`, entry.attributes, [
                                'URI',
                                'DURATION'
                            ]);
                            if (this.manifest.renditionReports) {
                                this.manifest.renditionReports.forEach((r, i)=>{
                                    if (!r.hasOwnProperty('lastPart')) {
                                        this.trigger('warn', {
                                            message: `#EXT-X-RENDITION-REPORT #${i} lacks required attribute(s): LAST-PART`
                                        });
                                    }
                                });
                            }
                        },
                        'server-control' () {
                            const attrs = this.manifest.serverControl = camelCaseKeys(entry.attributes);
                            if (!attrs.hasOwnProperty('canBlockReload')) {
                                attrs.canBlockReload = false;
                                this.trigger('info', {
                                    message: '#EXT-X-SERVER-CONTROL defaulting CAN-BLOCK-RELOAD to false'
                                });
                            }
                            setHoldBack.call(this, this.manifest);
                            if (attrs.canSkipDateranges && !attrs.hasOwnProperty('canSkipUntil')) {
                                this.trigger('warn', {
                                    message: '#EXT-X-SERVER-CONTROL lacks required attribute CAN-SKIP-UNTIL which is required when CAN-SKIP-DATERANGES is set'
                                });
                            }
                        },
                        'preload-hint' () {
                            // parts are always specifed before a segment
                            const segmentIndex = this.manifest.segments.length;
                            const hint = camelCaseKeys(entry.attributes);
                            const isPart = hint.type && hint.type === 'PART';
                            currentUri.preloadHints = currentUri.preloadHints || [];
                            currentUri.preloadHints.push(hint);
                            if (hint.byterange) {
                                if (!hint.byterange.hasOwnProperty('offset')) {
                                    // use last part byterange end or zero if not a part.
                                    hint.byterange.offset = isPart ? lastPartByterangeEnd : 0;
                                    if (isPart) {
                                        lastPartByterangeEnd = hint.byterange.offset + hint.byterange.length;
                                    }
                                }
                            }
                            const index = currentUri.preloadHints.length - 1;
                            this.warnOnMissingAttributes_(`#EXT-X-PRELOAD-HINT #${index} for segment #${segmentIndex}`, entry.attributes, [
                                'TYPE',
                                'URI'
                            ]);
                            if (!hint.type) {
                                return;
                            } // search through all preload hints except for the current one for
                            // a duplicate type.
                            for(let i = 0; i < currentUri.preloadHints.length - 1; i++){
                                const otherHint = currentUri.preloadHints[i];
                                if (!otherHint.type) {
                                    continue;
                                }
                                if (otherHint.type === hint.type) {
                                    this.trigger('warn', {
                                        message: `#EXT-X-PRELOAD-HINT #${index} for segment #${segmentIndex} has the same TYPE ${hint.type} as preload hint #${i}`
                                    });
                                }
                            }
                        },
                        'rendition-report' () {
                            const report = camelCaseKeys(entry.attributes);
                            this.manifest.renditionReports = this.manifest.renditionReports || [];
                            this.manifest.renditionReports.push(report);
                            const index = this.manifest.renditionReports.length - 1;
                            const required = [
                                'LAST-MSN',
                                'URI'
                            ];
                            if (hasParts) {
                                required.push('LAST-PART');
                            }
                            this.warnOnMissingAttributes_(`#EXT-X-RENDITION-REPORT #${index}`, entry.attributes, required);
                        },
                        'part-inf' () {
                            this.manifest.partInf = camelCaseKeys(entry.attributes);
                            this.warnOnMissingAttributes_('#EXT-X-PART-INF', entry.attributes, [
                                'PART-TARGET'
                            ]);
                            if (this.manifest.partInf.partTarget) {
                                this.manifest.partTargetDuration = this.manifest.partInf.partTarget;
                            }
                            setHoldBack.call(this, this.manifest);
                        },
                        'daterange' () {
                            this.manifest.dateRanges.push(camelCaseKeys(entry.attributes));
                            const index = this.manifest.dateRanges.length - 1;
                            this.warnOnMissingAttributes_(`#EXT-X-DATERANGE #${index}`, entry.attributes, [
                                'ID',
                                'START-DATE'
                            ]);
                            const dateRange = this.manifest.dateRanges[index];
                            if (dateRange.endDate && dateRange.startDate && new Date(dateRange.endDate) < new Date(dateRange.startDate)) {
                                this.trigger('warn', {
                                    message: 'EXT-X-DATERANGE END-DATE must be equal to or later than the value of the START-DATE'
                                });
                            }
                            if (dateRange.duration && dateRange.duration < 0) {
                                this.trigger('warn', {
                                    message: 'EXT-X-DATERANGE DURATION must not be negative'
                                });
                            }
                            if (dateRange.plannedDuration && dateRange.plannedDuration < 0) {
                                this.trigger('warn', {
                                    message: 'EXT-X-DATERANGE PLANNED-DURATION must not be negative'
                                });
                            }
                            const endOnNextYes = !!dateRange.endOnNext;
                            if (endOnNextYes && !dateRange.class) {
                                this.trigger('warn', {
                                    message: 'EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must have a CLASS attribute'
                                });
                            }
                            if (endOnNextYes && (dateRange.duration || dateRange.endDate)) {
                                this.trigger('warn', {
                                    message: 'EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must not contain DURATION or END-DATE attributes'
                                });
                            }
                            if (dateRange.duration && dateRange.endDate) {
                                const startDate = dateRange.startDate;
                                const newDateInSeconds = startDate.getTime() + dateRange.duration * 1000;
                                this.manifest.dateRanges[index].endDate = new Date(newDateInSeconds);
                            }
                            if (!dateRangeTags[dateRange.id]) {
                                dateRangeTags[dateRange.id] = dateRange;
                            } else {
                                for(const attribute in dateRangeTags[dateRange.id]){
                                    if (!!dateRange[attribute] && JSON.stringify(dateRangeTags[dateRange.id][attribute]) !== JSON.stringify(dateRange[attribute])) {
                                        this.trigger('warn', {
                                            message: 'EXT-X-DATERANGE tags with the same ID in a playlist must have the same attributes values'
                                        });
                                        break;
                                    }
                                } // if tags with the same ID do not have conflicting attributes, merge them
                                const dateRangeWithSameId = this.manifest.dateRanges.findIndex((dateRangeToFind)=>dateRangeToFind.id === dateRange.id);
                                this.manifest.dateRanges[dateRangeWithSameId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.manifest.dateRanges[dateRangeWithSameId], dateRange);
                                dateRangeTags[dateRange.id] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(dateRangeTags[dateRange.id], dateRange); // after merging, delete the duplicate dateRange that was added last
                                this.manifest.dateRanges.pop();
                            }
                        },
                        'independent-segments' () {
                            this.manifest.independentSegments = true;
                        },
                        'i-frames-only' () {
                            this.manifest.iFramesOnly = true;
                            this.requiredCompatibilityversion(this.manifest.version, 4);
                        },
                        'content-steering' () {
                            this.manifest.contentSteering = camelCaseKeys(entry.attributes);
                            this.warnOnMissingAttributes_('#EXT-X-CONTENT-STEERING', entry.attributes, [
                                'SERVER-URI'
                            ]);
                        },
                        /** @this {Parser} */ define () {
                            this.manifest.definitions = this.manifest.definitions || {};
                            const addDef = (n, v)=>{
                                if (n in this.manifest.definitions) {
                                    // An EXT-X-DEFINE tag MUST NOT specify the same Variable Name as any other
                                    // EXT-X-DEFINE tag in the same Playlist.  Parsers that encounter duplicate
                                    // Variable Name declarations MUST fail to parse the Playlist.
                                    this.trigger('error', {
                                        message: `EXT-X-DEFINE: Duplicate name ${n}`
                                    });
                                    return;
                                }
                                this.manifest.definitions[n] = v;
                            };
                            if ('QUERYPARAM' in entry.attributes) {
                                if ('NAME' in entry.attributes || 'IMPORT' in entry.attributes) {
                                    // An EXT-X-DEFINE tag MUST contain either a NAME, an IMPORT, or a
                                    // QUERYPARAM attribute, but only one of the three.  Otherwise, the
                                    // client MUST fail to parse the Playlist.
                                    this.trigger('error', {
                                        message: 'EXT-X-DEFINE: Invalid attributes'
                                    });
                                    return;
                                }
                                const val = this.params.get(entry.attributes.QUERYPARAM);
                                if (!val) {
                                    // If the QUERYPARAM attribute value does not match any query parameter in
                                    // the URI or the matching parameter has no associated value, the parser
                                    // MUST fail to parse the Playlist.  If more than one parameter matches,
                                    // any of the associated values MAY be used.
                                    this.trigger('error', {
                                        message: `EXT-X-DEFINE: No query param ${entry.attributes.QUERYPARAM}`
                                    });
                                    return;
                                }
                                addDef(entry.attributes.QUERYPARAM, decodeURIComponent(val));
                                return;
                            }
                            if ('NAME' in entry.attributes) {
                                if ('IMPORT' in entry.attributes) {
                                    // An EXT-X-DEFINE tag MUST contain either a NAME, an IMPORT, or a
                                    // QUERYPARAM attribute, but only one of the three.  Otherwise, the
                                    // client MUST fail to parse the Playlist.
                                    this.trigger('error', {
                                        message: 'EXT-X-DEFINE: Invalid attributes'
                                    });
                                    return;
                                }
                                if (!('VALUE' in entry.attributes) || typeof entry.attributes.VALUE !== 'string') {
                                    // This attribute is REQUIRED if the EXT-X-DEFINE tag has a NAME attribute.
                                    // The quoted-string MAY be empty.
                                    this.trigger('error', {
                                        message: `EXT-X-DEFINE: No value for ${entry.attributes.NAME}`
                                    });
                                    return;
                                }
                                addDef(entry.attributes.NAME, entry.attributes.VALUE);
                                return;
                            }
                            if ('IMPORT' in entry.attributes) {
                                if (!this.mainDefinitions[entry.attributes.IMPORT]) {
                                    // Covers two conditions, as mainDefinitions will always be empty on main
                                    //
                                    // EXT-X-DEFINE tags containing the IMPORT attribute MUST NOT occur in
                                    // Multivariant Playlists; they are only allowed in Media Playlists.
                                    //
                                    // If the IMPORT attribute value does not match any Variable Name in the
                                    // Multivariant Playlist, or if the Media Playlist loaded from a
                                    // Multivariant Playlist, the parser MUST fail the Playlist.
                                    this.trigger('error', {
                                        message: `EXT-X-DEFINE: No value ${entry.attributes.IMPORT} to import, or IMPORT used on main playlist`
                                    });
                                    return;
                                }
                                addDef(entry.attributes.IMPORT, this.mainDefinitions[entry.attributes.IMPORT]);
                                return;
                            } // An EXT-X-DEFINE tag MUST contain either a NAME, an IMPORT, or a QUERYPARAM
                            // attribute, but only one of the three.  Otherwise, the client MUST fail to
                            // parse the Playlist.
                            this.trigger('error', {
                                message: 'EXT-X-DEFINE: No attribute'
                            });
                        },
                        'i-frame-playlist' () {
                            this.manifest.iFramePlaylists.push({
                                attributes: entry.attributes,
                                uri: entry.uri,
                                timeline: currentTimeline
                            });
                            this.warnOnMissingAttributes_('#EXT-X-I-FRAME-STREAM-INF', entry.attributes, [
                                'BANDWIDTH',
                                'URI'
                            ]);
                        }
                    })[entry.tagType] || noop).call(self);
                },
                uri () {
                    currentUri.uri = entry.uri;
                    uris.push(currentUri); // if no explicit duration was declared, use the target duration
                    if (this.manifest.targetDuration && !('duration' in currentUri)) {
                        this.trigger('warn', {
                            message: 'defaulting segment duration to the target duration'
                        });
                        currentUri.duration = this.manifest.targetDuration;
                    } // annotate with encryption information, if necessary
                    if (key) {
                        currentUri.key = key;
                    }
                    currentUri.timeline = currentTimeline; // annotate with initialization segment information, if necessary
                    if (currentMap) {
                        currentUri.map = currentMap;
                    } // reset the last byterange end as it needs to be 0 between parts
                    lastPartByterangeEnd = 0; // Once we have at least one program date time we can always extrapolate it forward
                    if (this.lastProgramDateTime !== null) {
                        currentUri.programDateTime = this.lastProgramDateTime;
                        this.lastProgramDateTime += currentUri.duration * 1000;
                    } // prepare for the next URI
                    currentUri = {};
                },
                comment () {},
                custom () {
                    // if this is segment-level data attach the output to the segment
                    if (entry.segment) {
                        currentUri.custom = currentUri.custom || {};
                        currentUri.custom[entry.customType] = entry.data; // if this is manifest-level data attach to the top level manifest object
                    } else {
                        this.manifest.custom = this.manifest.custom || {};
                        this.manifest.custom[entry.customType] = entry.data;
                    }
                }
            })[entry.type].call(self);
        });
    }
    requiredCompatibilityversion(currentVersion, targetVersion) {
        if (currentVersion < targetVersion || !currentVersion) {
            this.trigger('warn', {
                message: `manifest must be at least version ${targetVersion}`
            });
        }
    }
    warnOnMissingAttributes_(identifier, attributes, required) {
        const missing = [];
        required.forEach(function(key) {
            if (!attributes.hasOwnProperty(key)) {
                missing.push(key);
            }
        });
        if (missing.length) {
            this.trigger('warn', {
                message: `${identifier} lacks required attribute(s): ${missing.join(', ')}`
            });
        }
    }
    /**
   * Parse the input string and update the manifest object.
   *
   * @param {string} chunk a potentially incomplete portion of the manifest
   */ push(chunk) {
        this.lineStream.push(chunk);
    }
    /**
   * Flush any remaining input. This can be handy if the last line of an M3U8
   * manifest did not contain a trailing newline but the file has been
   * completely received.
   */ end() {
        // flush any buffered input
        this.lineStream.push('\n');
        if (this.manifest.dateRanges.length && this.lastProgramDateTime === null) {
            this.trigger('warn', {
                message: 'A playlist with EXT-X-DATERANGE tag must contain atleast one EXT-X-PROGRAM-DATE-TIME tag'
            });
        }
        this.lastProgramDateTime = null;
        this.trigger('end');
    }
    /**
   * Add an additional parser for non-standard tags
   *
   * @param {Object}   options              a map of options for the added parser
   * @param {RegExp}   options.expression   a regular expression to match the custom header
   * @param {string}   options.customType   the custom type to register to the output
   * @param {Function} [options.dataParser] function to parse the line into an object
   * @param {boolean}  [options.segment]    should tag data be attached to the segment object
   */ addParser(options) {
        this.parseStream.addParser(options);
    }
    /**
   * Add a custom header mapper
   *
   * @param {Object}   options
   * @param {RegExp}   options.expression   a regular expression to match the custom header
   * @param {Function} options.map          function to translate tag into a different tag
   */ addTagMapper(options) {
        this.parseStream.addTagMapper(options);
    }
}
;
}}),
"[project]/node_modules/mpd-parser/dist/mpd-parser.es.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/*! @name mpd-parser @version 1.3.1 @license Apache-2.0 */ __turbopack_esm__({
    "VERSION": (()=>VERSION),
    "addSidxSegmentsToPlaylist": (()=>addSidxSegmentsToPlaylist$1),
    "generateSidxKey": (()=>generateSidxKey),
    "inheritAttributes": (()=>inheritAttributes),
    "parse": (()=>parse),
    "parseUTCTiming": (()=>parseUTCTiming),
    "stringToMpdXml": (()=>stringToMpdXml),
    "toM3u8": (()=>toM3u8),
    "toPlaylists": (()=>toPlaylists)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$resolve$2d$url$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/resolve-url.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/global/window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$media$2d$groups$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/media-groups.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$decode$2d$b64$2d$to$2d$uint8$2d$array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@videojs/vhs-utils/es/decode-b64-to-uint8-array.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$xmldom$2f$xmldom$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@xmldom/xmldom/lib/index.js [app-client] (ecmascript)");
;
;
;
;
;
var version = "1.3.1";
const isObject = (obj)=>{
    return !!obj && typeof obj === 'object';
};
const merge = (...objects)=>{
    return objects.reduce((result, source)=>{
        if (typeof source !== 'object') {
            return result;
        }
        Object.keys(source).forEach((key)=>{
            if (Array.isArray(result[key]) && Array.isArray(source[key])) {
                result[key] = result[key].concat(source[key]);
            } else if (isObject(result[key]) && isObject(source[key])) {
                result[key] = merge(result[key], source[key]);
            } else {
                result[key] = source[key];
            }
        });
        return result;
    }, {});
};
const values = (o)=>Object.keys(o).map((k)=>o[k]);
const range = (start, end)=>{
    const result = [];
    for(let i = start; i < end; i++){
        result.push(i);
    }
    return result;
};
const flatten = (lists)=>lists.reduce((x, y)=>x.concat(y), []);
const from = (list)=>{
    if (!list.length) {
        return [];
    }
    const result = [];
    for(let i = 0; i < list.length; i++){
        result.push(list[i]);
    }
    return result;
};
const findIndexes = (l, key)=>l.reduce((a, e, i)=>{
        if (e[key]) {
            a.push(i);
        }
        return a;
    }, []);
/**
 * Returns a union of the included lists provided each element can be identified by a key.
 *
 * @param {Array} list - list of lists to get the union of
 * @param {Function} keyFunction - the function to use as a key for each element
 *
 * @return {Array} the union of the arrays
 */ const union = (lists, keyFunction)=>{
    return values(lists.reduce((acc, list)=>{
        list.forEach((el)=>{
            acc[keyFunction(el)] = el;
        });
        return acc;
    }, {}));
};
var errors = {
    INVALID_NUMBER_OF_PERIOD: 'INVALID_NUMBER_OF_PERIOD',
    INVALID_NUMBER_OF_CONTENT_STEERING: 'INVALID_NUMBER_OF_CONTENT_STEERING',
    DASH_EMPTY_MANIFEST: 'DASH_EMPTY_MANIFEST',
    DASH_INVALID_XML: 'DASH_INVALID_XML',
    NO_BASE_URL: 'NO_BASE_URL',
    MISSING_SEGMENT_INFORMATION: 'MISSING_SEGMENT_INFORMATION',
    SEGMENT_TIME_UNSPECIFIED: 'SEGMENT_TIME_UNSPECIFIED',
    UNSUPPORTED_UTC_TIMING_SCHEME: 'UNSUPPORTED_UTC_TIMING_SCHEME'
};
/**
 * @typedef {Object} SingleUri
 * @property {string} uri - relative location of segment
 * @property {string} resolvedUri - resolved location of segment
 * @property {Object} byterange - Object containing information on how to make byte range
 *   requests following byte-range-spec per RFC2616.
 * @property {String} byterange.length - length of range request
 * @property {String} byterange.offset - byte offset of range request
 *
 * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.35.1
 */ /**
 * Converts a URLType node (*******.3 Table 13) to a segment object
 * that conforms to how m3u8-parser is structured
 *
 * @see https://github.com/videojs/m3u8-parser
 *
 * @param {string} baseUrl - baseUrl provided by <BaseUrl> nodes
 * @param {string} source - source url for segment
 * @param {string} range - optional range used for range calls,
 *   follows  RFC 2616, Clause 14.35.1
 * @return {SingleUri} full segment information transformed into a format similar
 *   to m3u8-parser
 */ const urlTypeToSegment = ({ baseUrl = '', source = '', range = '', indexRange = '' })=>{
    const segment = {
        uri: source,
        resolvedUri: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$resolve$2d$url$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(baseUrl || '', source)
    };
    if (range || indexRange) {
        const rangeStr = range ? range : indexRange;
        const ranges = rangeStr.split('-'); // default to parsing this as a BigInt if possible
        let startRange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(ranges[0]) : parseInt(ranges[0], 10);
        let endRange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(ranges[1]) : parseInt(ranges[1], 10); // convert back to a number if less than MAX_SAFE_INTEGER
        if (startRange < Number.MAX_SAFE_INTEGER && typeof startRange === 'bigint') {
            startRange = Number(startRange);
        }
        if (endRange < Number.MAX_SAFE_INTEGER && typeof endRange === 'bigint') {
            endRange = Number(endRange);
        }
        let length;
        if (typeof endRange === 'bigint' || typeof startRange === 'bigint') {
            length = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(endRange) - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(startRange) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(1);
        } else {
            length = endRange - startRange + 1;
        }
        if (typeof length === 'bigint' && length < Number.MAX_SAFE_INTEGER) {
            length = Number(length);
        } // byterange should be inclusive according to
        // RFC 2616, Clause 14.35.1
        segment.byterange = {
            length,
            offset: startRange
        };
    }
    return segment;
};
const byteRangeToString = (byterange)=>{
    // `endRange` is one less than `offset + length` because the HTTP range
    // header uses inclusive ranges
    let endRange;
    if (typeof byterange.offset === 'bigint' || typeof byterange.length === 'bigint') {
        endRange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(byterange.offset) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(byterange.length) - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(1);
    } else {
        endRange = byterange.offset + byterange.length - 1;
    }
    return `${byterange.offset}-${endRange}`;
};
/**
 * parse the end number attribue that can be a string
 * number, or undefined.
 *
 * @param {string|number|undefined} endNumber
 *        The end number attribute.
 *
 * @return {number|null}
 *          The result of parsing the end number.
 */ const parseEndNumber = (endNumber)=>{
    if (endNumber && typeof endNumber !== 'number') {
        endNumber = parseInt(endNumber, 10);
    }
    if (isNaN(endNumber)) {
        return null;
    }
    return endNumber;
};
/**
 * Functions for calculating the range of available segments in static and dynamic
 * manifests.
 */ const segmentRange = {
    /**
   * Returns the entire range of available segments for a static MPD
   *
   * @param {Object} attributes
   *        Inheritied MPD attributes
   * @return {{ start: number, end: number }}
   *         The start and end numbers for available segments
   */ static (attributes) {
        const { duration, timescale = 1, sourceDuration, periodDuration } = attributes;
        const endNumber = parseEndNumber(attributes.endNumber);
        const segmentDuration = duration / timescale;
        if (typeof endNumber === 'number') {
            return {
                start: 0,
                end: endNumber
            };
        }
        if (typeof periodDuration === 'number') {
            return {
                start: 0,
                end: periodDuration / segmentDuration
            };
        }
        return {
            start: 0,
            end: sourceDuration / segmentDuration
        };
    },
    /**
   * Returns the current live window range of available segments for a dynamic MPD
   *
   * @param {Object} attributes
   *        Inheritied MPD attributes
   * @return {{ start: number, end: number }}
   *         The start and end numbers for available segments
   */ dynamic (attributes) {
        const { NOW, clientOffset, availabilityStartTime, timescale = 1, duration, periodStart = 0, minimumUpdatePeriod = 0, timeShiftBufferDepth = Infinity } = attributes;
        const endNumber = parseEndNumber(attributes.endNumber); // clientOffset is passed in at the top level of mpd-parser and is an offset calculated
        // after retrieving UTC server time.
        const now = (NOW + clientOffset) / 1000; // WC stands for Wall Clock.
        // Convert the period start time to EPOCH.
        const periodStartWC = availabilityStartTime + periodStart; // Period end in EPOCH is manifest's retrieval time + time until next update.
        const periodEndWC = now + minimumUpdatePeriod;
        const periodDuration = periodEndWC - periodStartWC;
        const segmentCount = Math.ceil(periodDuration * timescale / duration);
        const availableStart = Math.floor((now - periodStartWC - timeShiftBufferDepth) * timescale / duration);
        const availableEnd = Math.floor((now - periodStartWC) * timescale / duration);
        return {
            start: Math.max(0, availableStart),
            end: typeof endNumber === 'number' ? endNumber : Math.min(segmentCount, availableEnd)
        };
    }
};
/**
 * Maps a range of numbers to objects with information needed to build the corresponding
 * segment list
 *
 * @name toSegmentsCallback
 * @function
 * @param {number} number
 *        Number of the segment
 * @param {number} index
 *        Index of the number in the range list
 * @return {{ number: Number, duration: Number, timeline: Number, time: Number }}
 *         Object with segment timing and duration info
 */ /**
 * Returns a callback for Array.prototype.map for mapping a range of numbers to
 * information needed to build the segment list.
 *
 * @param {Object} attributes
 *        Inherited MPD attributes
 * @return {toSegmentsCallback}
 *         Callback map function
 */ const toSegments = (attributes)=>(number)=>{
        const { duration, timescale = 1, periodStart, startNumber = 1 } = attributes;
        return {
            number: startNumber + number,
            duration: duration / timescale,
            timeline: periodStart,
            time: number * duration
        };
    };
/**
 * Returns a list of objects containing segment timing and duration info used for
 * building the list of segments. This uses the @duration attribute specified
 * in the MPD manifest to derive the range of segments.
 *
 * @param {Object} attributes
 *        Inherited MPD attributes
 * @return {{number: number, duration: number, time: number, timeline: number}[]}
 *         List of Objects with segment timing and duration info
 */ const parseByDuration = (attributes)=>{
    const { type, duration, timescale = 1, periodDuration, sourceDuration } = attributes;
    const { start, end } = segmentRange[type](attributes);
    const segments = range(start, end).map(toSegments(attributes));
    if (type === 'static') {
        const index = segments.length - 1; // section is either a period or the full source
        const sectionDuration = typeof periodDuration === 'number' ? periodDuration : sourceDuration; // final segment may be less than full segment duration
        segments[index].duration = sectionDuration - duration / timescale * index;
    }
    return segments;
};
/**
 * Translates SegmentBase into a set of segments.
 * (DASH SPEC Section 5.3.9.3.2) contains a set of <SegmentURL> nodes.  Each
 * node should be translated into segment.
 *
 * @param {Object} attributes
 *   Object containing all inherited attributes from parent elements with attribute
 *   names as keys
 * @return {Object.<Array>} list of segments
 */ const segmentsFromBase = (attributes)=>{
    const { baseUrl, initialization = {}, sourceDuration, indexRange = '', periodStart, presentationTime, number = 0, duration } = attributes; // base url is required for SegmentBase to work, per spec (Section *******.1)
    if (!baseUrl) {
        throw new Error(errors.NO_BASE_URL);
    }
    const initSegment = urlTypeToSegment({
        baseUrl,
        source: initialization.sourceURL,
        range: initialization.range
    });
    const segment = urlTypeToSegment({
        baseUrl,
        source: baseUrl,
        indexRange
    });
    segment.map = initSegment; // If there is a duration, use it, otherwise use the given duration of the source
    // (since SegmentBase is only for one total segment)
    if (duration) {
        const segmentTimeInfo = parseByDuration(attributes);
        if (segmentTimeInfo.length) {
            segment.duration = segmentTimeInfo[0].duration;
            segment.timeline = segmentTimeInfo[0].timeline;
        }
    } else if (sourceDuration) {
        segment.duration = sourceDuration;
        segment.timeline = periodStart;
    } // If presentation time is provided, these segments are being generated by SIDX
    // references, and should use the time provided. For the general case of SegmentBase,
    // there should only be one segment in the period, so its presentation time is the same
    // as its period start.
    segment.presentationTime = presentationTime || periodStart;
    segment.number = number;
    return [
        segment
    ];
};
/**
 * Given a playlist, a sidx box, and a baseUrl, update the segment list of the playlist
 * according to the sidx information given.
 *
 * playlist.sidx has metadadata about the sidx where-as the sidx param
 * is the parsed sidx box itself.
 *
 * @param {Object} playlist the playlist to update the sidx information for
 * @param {Object} sidx the parsed sidx box
 * @return {Object} the playlist object with the updated sidx information
 */ const addSidxSegmentsToPlaylist$1 = (playlist, sidx, baseUrl)=>{
    // Retain init segment information
    const initSegment = playlist.sidx.map ? playlist.sidx.map : null; // Retain source duration from initial main manifest parsing
    const sourceDuration = playlist.sidx.duration; // Retain source timeline
    const timeline = playlist.timeline || 0;
    const sidxByteRange = playlist.sidx.byterange;
    const sidxEnd = sidxByteRange.offset + sidxByteRange.length; // Retain timescale of the parsed sidx
    const timescale = sidx.timescale; // referenceType 1 refers to other sidx boxes
    const mediaReferences = sidx.references.filter((r)=>r.referenceType !== 1);
    const segments = [];
    const type = playlist.endList ? 'static' : 'dynamic';
    const periodStart = playlist.sidx.timeline;
    let presentationTime = periodStart;
    let number = playlist.mediaSequence || 0; // firstOffset is the offset from the end of the sidx box
    let startIndex; // eslint-disable-next-line
    if (typeof sidx.firstOffset === 'bigint') {
        startIndex = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(sidxEnd) + sidx.firstOffset;
    } else {
        startIndex = sidxEnd + sidx.firstOffset;
    }
    for(let i = 0; i < mediaReferences.length; i++){
        const reference = sidx.references[i]; // size of the referenced (sub)segment
        const size = reference.referencedSize; // duration of the referenced (sub)segment, in  the  timescale
        // this will be converted to seconds when generating segments
        const duration = reference.subsegmentDuration; // should be an inclusive range
        let endIndex; // eslint-disable-next-line
        if (typeof startIndex === 'bigint') {
            endIndex = startIndex + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(size) - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(1);
        } else {
            endIndex = startIndex + size - 1;
        }
        const indexRange = `${startIndex}-${endIndex}`;
        const attributes = {
            baseUrl,
            timescale,
            timeline,
            periodStart,
            presentationTime,
            number,
            duration,
            sourceDuration,
            indexRange,
            type
        };
        const segment = segmentsFromBase(attributes)[0];
        if (initSegment) {
            segment.map = initSegment;
        }
        segments.push(segment);
        if (typeof startIndex === 'bigint') {
            startIndex += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$global$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BigInt(size);
        } else {
            startIndex += size;
        }
        presentationTime += duration / timescale;
        number++;
    }
    playlist.segments = segments;
    return playlist;
};
const SUPPORTED_MEDIA_TYPES = [
    'AUDIO',
    'SUBTITLES'
]; // allow one 60fps frame as leniency (arbitrarily chosen)
const TIME_FUDGE = 1 / 60;
/**
 * Given a list of timelineStarts, combines, dedupes, and sorts them.
 *
 * @param {TimelineStart[]} timelineStarts - list of timeline starts
 *
 * @return {TimelineStart[]} the combined and deduped timeline starts
 */ const getUniqueTimelineStarts = (timelineStarts)=>{
    return union(timelineStarts, ({ timeline })=>timeline).sort((a, b)=>a.timeline > b.timeline ? 1 : -1);
};
/**
 * Finds the playlist with the matching NAME attribute.
 *
 * @param {Array} playlists - playlists to search through
 * @param {string} name - the NAME attribute to search for
 *
 * @return {Object|null} the matching playlist object, or null
 */ const findPlaylistWithName = (playlists, name)=>{
    for(let i = 0; i < playlists.length; i++){
        if (playlists[i].attributes.NAME === name) {
            return playlists[i];
        }
    }
    return null;
};
/**
 * Gets a flattened array of media group playlists.
 *
 * @param {Object} manifest - the main manifest object
 *
 * @return {Array} the media group playlists
 */ const getMediaGroupPlaylists = (manifest)=>{
    let mediaGroupPlaylists = [];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$media$2d$groups$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forEachMediaGroup"])(manifest, SUPPORTED_MEDIA_TYPES, (properties, type, group, label)=>{
        mediaGroupPlaylists = mediaGroupPlaylists.concat(properties.playlists || []);
    });
    return mediaGroupPlaylists;
};
/**
 * Updates the playlist's media sequence numbers.
 *
 * @param {Object} config - options object
 * @param {Object} config.playlist - the playlist to update
 * @param {number} config.mediaSequence - the mediaSequence number to start with
 */ const updateMediaSequenceForPlaylist = ({ playlist, mediaSequence })=>{
    playlist.mediaSequence = mediaSequence;
    playlist.segments.forEach((segment, index)=>{
        segment.number = playlist.mediaSequence + index;
    });
};
/**
 * Updates the media and discontinuity sequence numbers of newPlaylists given oldPlaylists
 * and a complete list of timeline starts.
 *
 * If no matching playlist is found, only the discontinuity sequence number of the playlist
 * will be updated.
 *
 * Since early available timelines are not supported, at least one segment must be present.
 *
 * @param {Object} config - options object
 * @param {Object[]} oldPlaylists - the old playlists to use as a reference
 * @param {Object[]} newPlaylists - the new playlists to update
 * @param {Object} timelineStarts - all timelineStarts seen in the stream to this point
 */ const updateSequenceNumbers = ({ oldPlaylists, newPlaylists, timelineStarts })=>{
    newPlaylists.forEach((playlist)=>{
        playlist.discontinuitySequence = timelineStarts.findIndex(function({ timeline }) {
            return timeline === playlist.timeline;
        }); // Playlists NAMEs come from DASH Representation IDs, which are mandatory
        // (see ISO_23009-1-2012 *******).
        //
        // If the same Representation existed in a prior Period, it will retain the same NAME.
        const oldPlaylist = findPlaylistWithName(oldPlaylists, playlist.attributes.NAME);
        if (!oldPlaylist) {
            // Since this is a new playlist, the media sequence values can start from 0 without
            // consequence.
            return;
        } // TODO better support for live SIDX
        //
        // As of this writing, mpd-parser does not support multiperiod SIDX (in live or VOD).
        // This is evident by a playlist only having a single SIDX reference. In a multiperiod
        // playlist there would need to be multiple SIDX references. In addition, live SIDX is
        // not supported when the SIDX properties change on refreshes.
        //
        // In the future, if support needs to be added, the merging logic here can be called
        // after SIDX references are resolved. For now, exit early to prevent exceptions being
        // thrown due to undefined references.
        if (playlist.sidx) {
            return;
        } // Since we don't yet support early available timelines, we don't need to support
        // playlists with no segments.
        const firstNewSegment = playlist.segments[0];
        const oldMatchingSegmentIndex = oldPlaylist.segments.findIndex(function(oldSegment) {
            return Math.abs(oldSegment.presentationTime - firstNewSegment.presentationTime) < TIME_FUDGE;
        }); // No matching segment from the old playlist means the entire playlist was refreshed.
        // In this case the media sequence should account for this update, and the new segments
        // should be marked as discontinuous from the prior content, since the last prior
        // timeline was removed.
        if (oldMatchingSegmentIndex === -1) {
            updateMediaSequenceForPlaylist({
                playlist,
                mediaSequence: oldPlaylist.mediaSequence + oldPlaylist.segments.length
            });
            playlist.segments[0].discontinuity = true;
            playlist.discontinuityStarts.unshift(0); // No matching segment does not necessarily mean there's missing content.
            //
            // If the new playlist's timeline is the same as the last seen segment's timeline,
            // then a discontinuity can be added to identify that there's potentially missing
            // content. If there's no missing content, the discontinuity should still be rather
            // harmless. It's possible that if segment durations are accurate enough, that the
            // existence of a gap can be determined using the presentation times and durations,
            // but if the segment timing info is off, it may introduce more problems than simply
            // adding the discontinuity.
            //
            // If the new playlist's timeline is different from the last seen segment's timeline,
            // then a discontinuity can be added to identify that this is the first seen segment
            // of a new timeline. However, the logic at the start of this function that
            // determined the disconinuity sequence by timeline index is now off by one (the
            // discontinuity of the newest timeline hasn't yet fallen off the manifest...since
            // we added it), so the disconinuity sequence must be decremented.
            //
            // A period may also have a duration of zero, so the case of no segments is handled
            // here even though we don't yet support early available periods.
            if (!oldPlaylist.segments.length && playlist.timeline > oldPlaylist.timeline || oldPlaylist.segments.length && playlist.timeline > oldPlaylist.segments[oldPlaylist.segments.length - 1].timeline) {
                playlist.discontinuitySequence--;
            }
            return;
        } // If the first segment matched with a prior segment on a discontinuity (it's matching
        // on the first segment of a period), then the discontinuitySequence shouldn't be the
        // timeline's matching one, but instead should be the one prior, and the first segment
        // of the new manifest should be marked with a discontinuity.
        //
        // The reason for this special case is that discontinuity sequence shows how many
        // discontinuities have fallen off of the playlist, and discontinuities are marked on
        // the first segment of a new "timeline." Because of this, while DASH will retain that
        // Period while the "timeline" exists, HLS keeps track of it via the discontinuity
        // sequence, and that first segment is an indicator, but can be removed before that
        // timeline is gone.
        const oldMatchingSegment = oldPlaylist.segments[oldMatchingSegmentIndex];
        if (oldMatchingSegment.discontinuity && !firstNewSegment.discontinuity) {
            firstNewSegment.discontinuity = true;
            playlist.discontinuityStarts.unshift(0);
            playlist.discontinuitySequence--;
        }
        updateMediaSequenceForPlaylist({
            playlist,
            mediaSequence: oldPlaylist.segments[oldMatchingSegmentIndex].number
        });
    });
};
/**
 * Given an old parsed manifest object and a new parsed manifest object, updates the
 * sequence and timing values within the new manifest to ensure that it lines up with the
 * old.
 *
 * @param {Array} oldManifest - the old main manifest object
 * @param {Array} newManifest - the new main manifest object
 *
 * @return {Object} the updated new manifest object
 */ const positionManifestOnTimeline = ({ oldManifest, newManifest })=>{
    // Starting from v4.1.2 of the IOP, section 4.4.3.3 states:
    //
    // "MPD@availabilityStartTime and Period@start shall not be changed over MPD updates."
    //
    // This was added from https://github.com/Dash-Industry-Forum/DASH-IF-IOP/issues/160
    //
    // Because of this change, and the difficulty of supporting periods with changing start
    // times, periods with changing start times are not supported. This makes the logic much
    // simpler, since periods with the same start time can be considerred the same period
    // across refreshes.
    //
    // To give an example as to the difficulty of handling periods where the start time may
    // change, if a single period manifest is refreshed with another manifest with a single
    // period, and both the start and end times are increased, then the only way to determine
    // if it's a new period or an old one that has changed is to look through the segments of
    // each playlist and determine the presentation time bounds to find a match. In addition,
    // if the period start changed to exceed the old period end, then there would be no
    // match, and it would not be possible to determine whether the refreshed period is a new
    // one or the old one.
    const oldPlaylists = oldManifest.playlists.concat(getMediaGroupPlaylists(oldManifest));
    const newPlaylists = newManifest.playlists.concat(getMediaGroupPlaylists(newManifest)); // Save all seen timelineStarts to the new manifest. Although this potentially means that
    // there's a "memory leak" in that it will never stop growing, in reality, only a couple
    // of properties are saved for each seen Period. Even long running live streams won't
    // generate too many Periods, unless the stream is watched for decades. In the future,
    // this can be optimized by mapping to discontinuity sequence numbers for each timeline,
    // but it may not become an issue, and the additional info can be useful for debugging.
    newManifest.timelineStarts = getUniqueTimelineStarts([
        oldManifest.timelineStarts,
        newManifest.timelineStarts
    ]);
    updateSequenceNumbers({
        oldPlaylists,
        newPlaylists,
        timelineStarts: newManifest.timelineStarts
    });
    return newManifest;
};
const generateSidxKey = (sidx)=>sidx && sidx.uri + '-' + byteRangeToString(sidx.byterange);
const mergeDiscontiguousPlaylists = (playlists)=>{
    // Break out playlists into groups based on their baseUrl
    const playlistsByBaseUrl = playlists.reduce(function(acc, cur) {
        if (!acc[cur.attributes.baseUrl]) {
            acc[cur.attributes.baseUrl] = [];
        }
        acc[cur.attributes.baseUrl].push(cur);
        return acc;
    }, {});
    let allPlaylists = [];
    Object.values(playlistsByBaseUrl).forEach((playlistGroup)=>{
        const mergedPlaylists = values(playlistGroup.reduce((acc, playlist)=>{
            // assuming playlist IDs are the same across periods
            // TODO: handle multiperiod where representation sets are not the same
            // across periods
            const name = playlist.attributes.id + (playlist.attributes.lang || '');
            if (!acc[name]) {
                // First Period
                acc[name] = playlist;
                acc[name].attributes.timelineStarts = [];
            } else {
                // Subsequent Periods
                if (playlist.segments) {
                    // first segment of subsequent periods signal a discontinuity
                    if (playlist.segments[0]) {
                        playlist.segments[0].discontinuity = true;
                    }
                    acc[name].segments.push(...playlist.segments);
                } // bubble up contentProtection, this assumes all DRM content
                // has the same contentProtection
                if (playlist.attributes.contentProtection) {
                    acc[name].attributes.contentProtection = playlist.attributes.contentProtection;
                }
            }
            acc[name].attributes.timelineStarts.push({
                // Although they represent the same number, it's important to have both to make it
                // compatible with HLS potentially having a similar attribute.
                start: playlist.attributes.periodStart,
                timeline: playlist.attributes.periodStart
            });
            return acc;
        }, {}));
        allPlaylists = allPlaylists.concat(mergedPlaylists);
    });
    return allPlaylists.map((playlist)=>{
        playlist.discontinuityStarts = findIndexes(playlist.segments || [], 'discontinuity');
        return playlist;
    });
};
const addSidxSegmentsToPlaylist = (playlist, sidxMapping)=>{
    const sidxKey = generateSidxKey(playlist.sidx);
    const sidxMatch = sidxKey && sidxMapping[sidxKey] && sidxMapping[sidxKey].sidx;
    if (sidxMatch) {
        addSidxSegmentsToPlaylist$1(playlist, sidxMatch, playlist.sidx.resolvedUri);
    }
    return playlist;
};
const addSidxSegmentsToPlaylists = (playlists, sidxMapping = {})=>{
    if (!Object.keys(sidxMapping).length) {
        return playlists;
    }
    for(const i in playlists){
        playlists[i] = addSidxSegmentsToPlaylist(playlists[i], sidxMapping);
    }
    return playlists;
};
const formatAudioPlaylist = ({ attributes, segments, sidx, mediaSequence, discontinuitySequence, discontinuityStarts }, isAudioOnly)=>{
    const playlist = {
        attributes: {
            NAME: attributes.id,
            BANDWIDTH: attributes.bandwidth,
            CODECS: attributes.codecs,
            ['PROGRAM-ID']: 1
        },
        uri: '',
        endList: attributes.type === 'static',
        timeline: attributes.periodStart,
        resolvedUri: attributes.baseUrl || '',
        targetDuration: attributes.duration,
        discontinuitySequence,
        discontinuityStarts,
        timelineStarts: attributes.timelineStarts,
        mediaSequence,
        segments
    };
    if (attributes.contentProtection) {
        playlist.contentProtection = attributes.contentProtection;
    }
    if (attributes.serviceLocation) {
        playlist.attributes.serviceLocation = attributes.serviceLocation;
    }
    if (sidx) {
        playlist.sidx = sidx;
    }
    if (isAudioOnly) {
        playlist.attributes.AUDIO = 'audio';
        playlist.attributes.SUBTITLES = 'subs';
    }
    return playlist;
};
const formatVttPlaylist = ({ attributes, segments, mediaSequence, discontinuityStarts, discontinuitySequence })=>{
    if (typeof segments === 'undefined') {
        // vtt tracks may use single file in BaseURL
        segments = [
            {
                uri: attributes.baseUrl,
                timeline: attributes.periodStart,
                resolvedUri: attributes.baseUrl || '',
                duration: attributes.sourceDuration,
                number: 0
            }
        ]; // targetDuration should be the same duration as the only segment
        attributes.duration = attributes.sourceDuration;
    }
    const m3u8Attributes = {
        NAME: attributes.id,
        BANDWIDTH: attributes.bandwidth,
        ['PROGRAM-ID']: 1
    };
    if (attributes.codecs) {
        m3u8Attributes.CODECS = attributes.codecs;
    }
    const vttPlaylist = {
        attributes: m3u8Attributes,
        uri: '',
        endList: attributes.type === 'static',
        timeline: attributes.periodStart,
        resolvedUri: attributes.baseUrl || '',
        targetDuration: attributes.duration,
        timelineStarts: attributes.timelineStarts,
        discontinuityStarts,
        discontinuitySequence,
        mediaSequence,
        segments
    };
    if (attributes.serviceLocation) {
        vttPlaylist.attributes.serviceLocation = attributes.serviceLocation;
    }
    return vttPlaylist;
};
const organizeAudioPlaylists = (playlists, sidxMapping = {}, isAudioOnly = false)=>{
    let mainPlaylist;
    const formattedPlaylists = playlists.reduce((a, playlist)=>{
        const role = playlist.attributes.role && playlist.attributes.role.value || '';
        const language = playlist.attributes.lang || '';
        let label = playlist.attributes.label || 'main';
        if (language && !playlist.attributes.label) {
            const roleLabel = role ? ` (${role})` : '';
            label = `${playlist.attributes.lang}${roleLabel}`;
        }
        if (!a[label]) {
            a[label] = {
                language,
                autoselect: true,
                default: role === 'main',
                playlists: [],
                uri: ''
            };
        }
        const formatted = addSidxSegmentsToPlaylist(formatAudioPlaylist(playlist, isAudioOnly), sidxMapping);
        a[label].playlists.push(formatted);
        if (typeof mainPlaylist === 'undefined' && role === 'main') {
            mainPlaylist = playlist;
            mainPlaylist.default = true;
        }
        return a;
    }, {}); // if no playlists have role "main", mark the first as main
    if (!mainPlaylist) {
        const firstLabel = Object.keys(formattedPlaylists)[0];
        formattedPlaylists[firstLabel].default = true;
    }
    return formattedPlaylists;
};
const organizeVttPlaylists = (playlists, sidxMapping = {})=>{
    return playlists.reduce((a, playlist)=>{
        const label = playlist.attributes.label || playlist.attributes.lang || 'text';
        const language = playlist.attributes.lang || 'und';
        if (!a[label]) {
            a[label] = {
                language,
                default: false,
                autoselect: false,
                playlists: [],
                uri: ''
            };
        }
        a[label].playlists.push(addSidxSegmentsToPlaylist(formatVttPlaylist(playlist), sidxMapping));
        return a;
    }, {});
};
const organizeCaptionServices = (captionServices)=>captionServices.reduce((svcObj, svc)=>{
        if (!svc) {
            return svcObj;
        }
        svc.forEach((service)=>{
            const { channel, language } = service;
            svcObj[language] = {
                autoselect: false,
                default: false,
                instreamId: channel,
                language
            };
            if (service.hasOwnProperty('aspectRatio')) {
                svcObj[language].aspectRatio = service.aspectRatio;
            }
            if (service.hasOwnProperty('easyReader')) {
                svcObj[language].easyReader = service.easyReader;
            }
            if (service.hasOwnProperty('3D')) {
                svcObj[language]['3D'] = service['3D'];
            }
        });
        return svcObj;
    }, {});
const formatVideoPlaylist = ({ attributes, segments, sidx, discontinuityStarts })=>{
    const playlist = {
        attributes: {
            NAME: attributes.id,
            AUDIO: 'audio',
            SUBTITLES: 'subs',
            RESOLUTION: {
                width: attributes.width,
                height: attributes.height
            },
            CODECS: attributes.codecs,
            BANDWIDTH: attributes.bandwidth,
            ['PROGRAM-ID']: 1
        },
        uri: '',
        endList: attributes.type === 'static',
        timeline: attributes.periodStart,
        resolvedUri: attributes.baseUrl || '',
        targetDuration: attributes.duration,
        discontinuityStarts,
        timelineStarts: attributes.timelineStarts,
        segments
    };
    if (attributes.frameRate) {
        playlist.attributes['FRAME-RATE'] = attributes.frameRate;
    }
    if (attributes.contentProtection) {
        playlist.contentProtection = attributes.contentProtection;
    }
    if (attributes.serviceLocation) {
        playlist.attributes.serviceLocation = attributes.serviceLocation;
    }
    if (sidx) {
        playlist.sidx = sidx;
    }
    return playlist;
};
const videoOnly = ({ attributes })=>attributes.mimeType === 'video/mp4' || attributes.mimeType === 'video/webm' || attributes.contentType === 'video';
const audioOnly = ({ attributes })=>attributes.mimeType === 'audio/mp4' || attributes.mimeType === 'audio/webm' || attributes.contentType === 'audio';
const vttOnly = ({ attributes })=>attributes.mimeType === 'text/vtt' || attributes.contentType === 'text';
/**
 * Contains start and timeline properties denoting a timeline start. For DASH, these will
 * be the same number.
 *
 * @typedef {Object} TimelineStart
 * @property {number} start - the start time of the timeline
 * @property {number} timeline - the timeline number
 */ /**
 * Adds appropriate media and discontinuity sequence values to the segments and playlists.
 *
 * Throughout mpd-parser, the `number` attribute is used in relation to `startNumber`, a
 * DASH specific attribute used in constructing segment URI's from templates. However, from
 * an HLS perspective, the `number` attribute on a segment would be its `mediaSequence`
 * value, which should start at the original media sequence value (or 0) and increment by 1
 * for each segment thereafter. Since DASH's `startNumber` values are independent per
 * period, it doesn't make sense to use it for `number`. Instead, assume everything starts
 * from a 0 mediaSequence value and increment from there.
 *
 * Note that VHS currently doesn't use the `number` property, but it can be helpful for
 * debugging and making sense of the manifest.
 *
 * For live playlists, to account for values increasing in manifests when periods are
 * removed on refreshes, merging logic should be used to update the numbers to their
 * appropriate values (to ensure they're sequential and increasing).
 *
 * @param {Object[]} playlists - the playlists to update
 * @param {TimelineStart[]} timelineStarts - the timeline starts for the manifest
 */ const addMediaSequenceValues = (playlists, timelineStarts)=>{
    // increment all segments sequentially
    playlists.forEach((playlist)=>{
        playlist.mediaSequence = 0;
        playlist.discontinuitySequence = timelineStarts.findIndex(function({ timeline }) {
            return timeline === playlist.timeline;
        });
        if (!playlist.segments) {
            return;
        }
        playlist.segments.forEach((segment, index)=>{
            segment.number = index;
        });
    });
};
/**
 * Given a media group object, flattens all playlists within the media group into a single
 * array.
 *
 * @param {Object} mediaGroupObject - the media group object
 *
 * @return {Object[]}
 *         The media group playlists
 */ const flattenMediaGroupPlaylists = (mediaGroupObject)=>{
    if (!mediaGroupObject) {
        return [];
    }
    return Object.keys(mediaGroupObject).reduce((acc, label)=>{
        const labelContents = mediaGroupObject[label];
        return acc.concat(labelContents.playlists);
    }, []);
};
const toM3u8 = ({ dashPlaylists, locations, contentSteering, sidxMapping = {}, previousManifest, eventStream })=>{
    if (!dashPlaylists.length) {
        return {};
    } // grab all main manifest attributes
    const { sourceDuration: duration, type, suggestedPresentationDelay, minimumUpdatePeriod } = dashPlaylists[0].attributes;
    const videoPlaylists = mergeDiscontiguousPlaylists(dashPlaylists.filter(videoOnly)).map(formatVideoPlaylist);
    const audioPlaylists = mergeDiscontiguousPlaylists(dashPlaylists.filter(audioOnly));
    const vttPlaylists = mergeDiscontiguousPlaylists(dashPlaylists.filter(vttOnly));
    const captions = dashPlaylists.map((playlist)=>playlist.attributes.captionServices).filter(Boolean);
    const manifest = {
        allowCache: true,
        discontinuityStarts: [],
        segments: [],
        endList: true,
        mediaGroups: {
            AUDIO: {},
            VIDEO: {},
            ['CLOSED-CAPTIONS']: {},
            SUBTITLES: {}
        },
        uri: '',
        duration,
        playlists: addSidxSegmentsToPlaylists(videoPlaylists, sidxMapping)
    };
    if (minimumUpdatePeriod >= 0) {
        manifest.minimumUpdatePeriod = minimumUpdatePeriod * 1000;
    }
    if (locations) {
        manifest.locations = locations;
    }
    if (contentSteering) {
        manifest.contentSteering = contentSteering;
    }
    if (type === 'dynamic') {
        manifest.suggestedPresentationDelay = suggestedPresentationDelay;
    }
    if (eventStream && eventStream.length > 0) {
        manifest.eventStream = eventStream;
    }
    const isAudioOnly = manifest.playlists.length === 0;
    const organizedAudioGroup = audioPlaylists.length ? organizeAudioPlaylists(audioPlaylists, sidxMapping, isAudioOnly) : null;
    const organizedVttGroup = vttPlaylists.length ? organizeVttPlaylists(vttPlaylists, sidxMapping) : null;
    const formattedPlaylists = videoPlaylists.concat(flattenMediaGroupPlaylists(organizedAudioGroup), flattenMediaGroupPlaylists(organizedVttGroup));
    const playlistTimelineStarts = formattedPlaylists.map(({ timelineStarts })=>timelineStarts);
    manifest.timelineStarts = getUniqueTimelineStarts(playlistTimelineStarts);
    addMediaSequenceValues(formattedPlaylists, manifest.timelineStarts);
    if (organizedAudioGroup) {
        manifest.mediaGroups.AUDIO.audio = organizedAudioGroup;
    }
    if (organizedVttGroup) {
        manifest.mediaGroups.SUBTITLES.subs = organizedVttGroup;
    }
    if (captions.length) {
        manifest.mediaGroups['CLOSED-CAPTIONS'].cc = organizeCaptionServices(captions);
    }
    if (previousManifest) {
        return positionManifestOnTimeline({
            oldManifest: previousManifest,
            newManifest: manifest
        });
    }
    return manifest;
};
/**
 * Calculates the R (repetition) value for a live stream (for the final segment
 * in a manifest where the r value is negative 1)
 *
 * @param {Object} attributes
 *        Object containing all inherited attributes from parent elements with attribute
 *        names as keys
 * @param {number} time
 *        current time (typically the total time up until the final segment)
 * @param {number} duration
 *        duration property for the given <S />
 *
 * @return {number}
 *        R value to reach the end of the given period
 */ const getLiveRValue = (attributes, time, duration)=>{
    const { NOW, clientOffset, availabilityStartTime, timescale = 1, periodStart = 0, minimumUpdatePeriod = 0 } = attributes;
    const now = (NOW + clientOffset) / 1000;
    const periodStartWC = availabilityStartTime + periodStart;
    const periodEndWC = now + minimumUpdatePeriod;
    const periodDuration = periodEndWC - periodStartWC;
    return Math.ceil((periodDuration * timescale - time) / duration);
};
/**
 * Uses information provided by SegmentTemplate.SegmentTimeline to determine segment
 * timing and duration
 *
 * @param {Object} attributes
 *        Object containing all inherited attributes from parent elements with attribute
 *        names as keys
 * @param {Object[]} segmentTimeline
 *        List of objects representing the attributes of each S element contained within
 *
 * @return {{number: number, duration: number, time: number, timeline: number}[]}
 *         List of Objects with segment timing and duration info
 */ const parseByTimeline = (attributes, segmentTimeline)=>{
    const { type, minimumUpdatePeriod = 0, media = '', sourceDuration, timescale = 1, startNumber = 1, periodStart: timeline } = attributes;
    const segments = [];
    let time = -1;
    for(let sIndex = 0; sIndex < segmentTimeline.length; sIndex++){
        const S = segmentTimeline[sIndex];
        const duration = S.d;
        const repeat = S.r || 0;
        const segmentTime = S.t || 0;
        if (time < 0) {
            // first segment
            time = segmentTime;
        }
        if (segmentTime && segmentTime > time) {
            // discontinuity
            // TODO: How to handle this type of discontinuity
            // timeline++ here would treat it like HLS discontuity and content would
            // get appended without gap
            // E.G.
            //  <S t="0" d="1" />
            //  <S d="1" />
            //  <S d="1" />
            //  <S t="5" d="1" />
            // would have $Time$ values of [0, 1, 2, 5]
            // should this be appened at time positions [0, 1, 2, 3],(#EXT-X-DISCONTINUITY)
            // or [0, 1, 2, gap, gap, 5]? (#EXT-X-GAP)
            // does the value of sourceDuration consider this when calculating arbitrary
            // negative @r repeat value?
            // E.G. Same elements as above with this added at the end
            //  <S d="1" r="-1" />
            //  with a sourceDuration of 10
            // Would the 2 gaps be included in the time duration calculations resulting in
            // 8 segments with $Time$ values of [0, 1, 2, 5, 6, 7, 8, 9] or 10 segments
            // with $Time$ values of [0, 1, 2, 5, 6, 7, 8, 9, 10, 11] ?
            time = segmentTime;
        }
        let count;
        if (repeat < 0) {
            const nextS = sIndex + 1;
            if (nextS === segmentTimeline.length) {
                // last segment
                if (type === 'dynamic' && minimumUpdatePeriod > 0 && media.indexOf('$Number$') > 0) {
                    count = getLiveRValue(attributes, time, duration);
                } else {
                    // TODO: This may be incorrect depending on conclusion of TODO above
                    count = (sourceDuration * timescale - time) / duration;
                }
            } else {
                count = (segmentTimeline[nextS].t - time) / duration;
            }
        } else {
            count = repeat + 1;
        }
        const end = startNumber + segments.length + count;
        let number = startNumber + segments.length;
        while(number < end){
            segments.push({
                number,
                duration: duration / timescale,
                time,
                timeline
            });
            time += duration;
            number++;
        }
    }
    return segments;
};
const identifierPattern = /\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g;
/**
 * Replaces template identifiers with corresponding values. To be used as the callback
 * for String.prototype.replace
 *
 * @name replaceCallback
 * @function
 * @param {string} match
 *        Entire match of identifier
 * @param {string} identifier
 *        Name of matched identifier
 * @param {string} format
 *        Format tag string. Its presence indicates that padding is expected
 * @param {string} width
 *        Desired length of the replaced value. Values less than this width shall be left
 *        zero padded
 * @return {string}
 *         Replacement for the matched identifier
 */ /**
 * Returns a function to be used as a callback for String.prototype.replace to replace
 * template identifiers
 *
 * @param {Obect} values
 *        Object containing values that shall be used to replace known identifiers
 * @param {number} values.RepresentationID
 *        Value of the Representation@id attribute
 * @param {number} values.Number
 *        Number of the corresponding segment
 * @param {number} values.Bandwidth
 *        Value of the Representation@bandwidth attribute.
 * @param {number} values.Time
 *        Timestamp value of the corresponding segment
 * @return {replaceCallback}
 *         Callback to be used with String.prototype.replace to replace identifiers
 */ const identifierReplacement = (values)=>(match, identifier, format, width)=>{
        if (match === '$$') {
            // escape sequence
            return '$';
        }
        if (typeof values[identifier] === 'undefined') {
            return match;
        }
        const value = '' + values[identifier];
        if (identifier === 'RepresentationID') {
            // Format tag shall not be present with RepresentationID
            return value;
        }
        if (!format) {
            width = 1;
        } else {
            width = parseInt(width, 10);
        }
        if (value.length >= width) {
            return value;
        }
        return `${new Array(width - value.length + 1).join('0')}${value}`;
    };
/**
 * Constructs a segment url from a template string
 *
 * @param {string} url
 *        Template string to construct url from
 * @param {Obect} values
 *        Object containing values that shall be used to replace known identifiers
 * @param {number} values.RepresentationID
 *        Value of the Representation@id attribute
 * @param {number} values.Number
 *        Number of the corresponding segment
 * @param {number} values.Bandwidth
 *        Value of the Representation@bandwidth attribute.
 * @param {number} values.Time
 *        Timestamp value of the corresponding segment
 * @return {string}
 *         Segment url with identifiers replaced
 */ const constructTemplateUrl = (url, values)=>url.replace(identifierPattern, identifierReplacement(values));
/**
 * Generates a list of objects containing timing and duration information about each
 * segment needed to generate segment uris and the complete segment object
 *
 * @param {Object} attributes
 *        Object containing all inherited attributes from parent elements with attribute
 *        names as keys
 * @param {Object[]|undefined} segmentTimeline
 *        List of objects representing the attributes of each S element contained within
 *        the SegmentTimeline element
 * @return {{number: number, duration: number, time: number, timeline: number}[]}
 *         List of Objects with segment timing and duration info
 */ const parseTemplateInfo = (attributes, segmentTimeline)=>{
    if (!attributes.duration && !segmentTimeline) {
        // if neither @duration or SegmentTimeline are present, then there shall be exactly
        // one media segment
        return [
            {
                number: attributes.startNumber || 1,
                duration: attributes.sourceDuration,
                time: 0,
                timeline: attributes.periodStart
            }
        ];
    }
    if (attributes.duration) {
        return parseByDuration(attributes);
    }
    return parseByTimeline(attributes, segmentTimeline);
};
/**
 * Generates a list of segments using information provided by the SegmentTemplate element
 *
 * @param {Object} attributes
 *        Object containing all inherited attributes from parent elements with attribute
 *        names as keys
 * @param {Object[]|undefined} segmentTimeline
 *        List of objects representing the attributes of each S element contained within
 *        the SegmentTimeline element
 * @return {Object[]}
 *         List of segment objects
 */ const segmentsFromTemplate = (attributes, segmentTimeline)=>{
    const templateValues = {
        RepresentationID: attributes.id,
        Bandwidth: attributes.bandwidth || 0
    };
    const { initialization = {
        sourceURL: '',
        range: ''
    } } = attributes;
    const mapSegment = urlTypeToSegment({
        baseUrl: attributes.baseUrl,
        source: constructTemplateUrl(initialization.sourceURL, templateValues),
        range: initialization.range
    });
    const segments = parseTemplateInfo(attributes, segmentTimeline);
    return segments.map((segment)=>{
        templateValues.Number = segment.number;
        templateValues.Time = segment.time;
        const uri = constructTemplateUrl(attributes.media || '', templateValues); // See DASH spec section *******.2
        // - if timescale isn't present on any level, default to 1.
        const timescale = attributes.timescale || 1; // - if presentationTimeOffset isn't present on any level, default to 0
        const presentationTimeOffset = attributes.presentationTimeOffset || 0;
        const presentationTime = // calculated in mpd-parser prior to this, so it's assumed to be available.
        attributes.periodStart + (segment.time - presentationTimeOffset) / timescale;
        const map = {
            uri,
            timeline: segment.timeline,
            duration: segment.duration,
            resolvedUri: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$resolve$2d$url$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(attributes.baseUrl || '', uri),
            map: mapSegment,
            number: segment.number,
            presentationTime
        };
        return map;
    });
};
/**
 * Converts a <SegmentUrl> (of type URLType from the DASH spec ******* Table 14)
 * to an object that matches the output of a segment in videojs/mpd-parser
 *
 * @param {Object} attributes
 *   Object containing all inherited attributes from parent elements with attribute
 *   names as keys
 * @param {Object} segmentUrl
 *   <SegmentURL> node to translate into a segment object
 * @return {Object} translated segment object
 */ const SegmentURLToSegmentObject = (attributes, segmentUrl)=>{
    const { baseUrl, initialization = {} } = attributes;
    const initSegment = urlTypeToSegment({
        baseUrl,
        source: initialization.sourceURL,
        range: initialization.range
    });
    const segment = urlTypeToSegment({
        baseUrl,
        source: segmentUrl.media,
        range: segmentUrl.mediaRange
    });
    segment.map = initSegment;
    return segment;
};
/**
 * Generates a list of segments using information provided by the SegmentList element
 * SegmentList (DASH SPEC Section 5.3.9.3.2) contains a set of <SegmentURL> nodes.  Each
 * node should be translated into segment.
 *
 * @param {Object} attributes
 *   Object containing all inherited attributes from parent elements with attribute
 *   names as keys
 * @param {Object[]|undefined} segmentTimeline
 *        List of objects representing the attributes of each S element contained within
 *        the SegmentTimeline element
 * @return {Object.<Array>} list of segments
 */ const segmentsFromList = (attributes, segmentTimeline)=>{
    const { duration, segmentUrls = [], periodStart } = attributes; // Per spec (*******.1) no way to determine segment duration OR
    // if both SegmentTimeline and @duration are defined, it is outside of spec.
    if (!duration && !segmentTimeline || duration && segmentTimeline) {
        throw new Error(errors.SEGMENT_TIME_UNSPECIFIED);
    }
    const segmentUrlMap = segmentUrls.map((segmentUrlObject)=>SegmentURLToSegmentObject(attributes, segmentUrlObject));
    let segmentTimeInfo;
    if (duration) {
        segmentTimeInfo = parseByDuration(attributes);
    }
    if (segmentTimeline) {
        segmentTimeInfo = parseByTimeline(attributes, segmentTimeline);
    }
    const segments = segmentTimeInfo.map((segmentTime, index)=>{
        if (segmentUrlMap[index]) {
            const segment = segmentUrlMap[index]; // See DASH spec section *******.2
            // - if timescale isn't present on any level, default to 1.
            const timescale = attributes.timescale || 1; // - if presentationTimeOffset isn't present on any level, default to 0
            const presentationTimeOffset = attributes.presentationTimeOffset || 0;
            segment.timeline = segmentTime.timeline;
            segment.duration = segmentTime.duration;
            segment.number = segmentTime.number;
            segment.presentationTime = periodStart + (segmentTime.time - presentationTimeOffset) / timescale;
            return segment;
        } // Since we're mapping we should get rid of any blank segments (in case
    // the given SegmentTimeline is handling for more elements than we have
    // SegmentURLs for).
    }).filter((segment)=>segment);
    return segments;
};
const generateSegments = ({ attributes, segmentInfo })=>{
    let segmentAttributes;
    let segmentsFn;
    if (segmentInfo.template) {
        segmentsFn = segmentsFromTemplate;
        segmentAttributes = merge(attributes, segmentInfo.template);
    } else if (segmentInfo.base) {
        segmentsFn = segmentsFromBase;
        segmentAttributes = merge(attributes, segmentInfo.base);
    } else if (segmentInfo.list) {
        segmentsFn = segmentsFromList;
        segmentAttributes = merge(attributes, segmentInfo.list);
    }
    const segmentsInfo = {
        attributes
    };
    if (!segmentsFn) {
        return segmentsInfo;
    }
    const segments = segmentsFn(segmentAttributes, segmentInfo.segmentTimeline); // The @duration attribute will be used to determin the playlist's targetDuration which
    // must be in seconds. Since we've generated the segment list, we no longer need
    // @duration to be in @timescale units, so we can convert it here.
    if (segmentAttributes.duration) {
        const { duration, timescale = 1 } = segmentAttributes;
        segmentAttributes.duration = duration / timescale;
    } else if (segments.length) {
        // if there is no @duration attribute, use the largest segment duration as
        // as target duration
        segmentAttributes.duration = segments.reduce((max, segment)=>{
            return Math.max(max, Math.ceil(segment.duration));
        }, 0);
    } else {
        segmentAttributes.duration = 0;
    }
    segmentsInfo.attributes = segmentAttributes;
    segmentsInfo.segments = segments; // This is a sidx box without actual segment information
    if (segmentInfo.base && segmentAttributes.indexRange) {
        segmentsInfo.sidx = segments[0];
        segmentsInfo.segments = [];
    }
    return segmentsInfo;
};
const toPlaylists = (representations)=>representations.map(generateSegments);
const findChildren = (element, name)=>from(element.childNodes).filter(({ tagName })=>tagName === name);
const getContent = (element)=>element.textContent.trim();
/**
 * Converts the provided string that may contain a division operation to a number.
 *
 * @param {string} value - the provided string value
 *
 * @return {number} the parsed string value
 */ const parseDivisionValue = (value)=>{
    return parseFloat(value.split('/').reduce((prev, current)=>prev / current));
};
const parseDuration = (str)=>{
    const SECONDS_IN_YEAR = 365 * 24 * 60 * 60;
    const SECONDS_IN_MONTH = 30 * 24 * 60 * 60;
    const SECONDS_IN_DAY = 24 * 60 * 60;
    const SECONDS_IN_HOUR = 60 * 60;
    const SECONDS_IN_MIN = 60; // P10Y10M10DT10H10M10.1S
    const durationRegex = /P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/;
    const match = durationRegex.exec(str);
    if (!match) {
        return 0;
    }
    const [year, month, day, hour, minute, second] = match.slice(1);
    return parseFloat(year || 0) * SECONDS_IN_YEAR + parseFloat(month || 0) * SECONDS_IN_MONTH + parseFloat(day || 0) * SECONDS_IN_DAY + parseFloat(hour || 0) * SECONDS_IN_HOUR + parseFloat(minute || 0) * SECONDS_IN_MIN + parseFloat(second || 0);
};
const parseDate = (str)=>{
    // Date format without timezone according to ISO 8601
    // YYY-MM-DDThh:mm:ss.ssssss
    const dateRegex = /^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/; // If the date string does not specifiy a timezone, we must specifiy UTC. This is
    // expressed by ending with 'Z'
    if (dateRegex.test(str)) {
        str += 'Z';
    }
    return Date.parse(str);
};
const parsers = {
    /**
   * Specifies the duration of the entire Media Presentation. Format is a duration string
   * as specified in ISO 8601
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The duration in seconds
   */ mediaPresentationDuration (value) {
        return parseDuration(value);
    },
    /**
   * Specifies the Segment availability start time for all Segments referred to in this
   * MPD. For a dynamic manifest, it specifies the anchor for the earliest availability
   * time. Format is a date string as specified in ISO 8601
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The date as seconds from unix epoch
   */ availabilityStartTime (value) {
        return parseDate(value) / 1000;
    },
    /**
   * Specifies the smallest period between potential changes to the MPD. Format is a
   * duration string as specified in ISO 8601
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The duration in seconds
   */ minimumUpdatePeriod (value) {
        return parseDuration(value);
    },
    /**
   * Specifies the suggested presentation delay. Format is a
   * duration string as specified in ISO 8601
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The duration in seconds
   */ suggestedPresentationDelay (value) {
        return parseDuration(value);
    },
    /**
   * specifices the type of mpd. Can be either "static" or "dynamic"
   *
   * @param {string} value
   *        value of attribute as a string
   *
   * @return {string}
   *         The type as a string
   */ type (value) {
        return value;
    },
    /**
   * Specifies the duration of the smallest time shifting buffer for any Representation
   * in the MPD. Format is a duration string as specified in ISO 8601
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The duration in seconds
   */ timeShiftBufferDepth (value) {
        return parseDuration(value);
    },
    /**
   * Specifies the PeriodStart time of the Period relative to the availabilityStarttime.
   * Format is a duration string as specified in ISO 8601
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The duration in seconds
   */ start (value) {
        return parseDuration(value);
    },
    /**
   * Specifies the width of the visual presentation
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed width
   */ width (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the height of the visual presentation
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed height
   */ height (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the bitrate of the representation
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed bandwidth
   */ bandwidth (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the frame rate of the representation
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed frame rate
   */ frameRate (value) {
        return parseDivisionValue(value);
    },
    /**
   * Specifies the number of the first Media Segment in this Representation in the Period
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed number
   */ startNumber (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the timescale in units per seconds
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed timescale
   */ timescale (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the presentationTimeOffset.
   *
   * @param {string} value
   *        value of the attribute as a string
   *
   * @return {number}
   *         The parsed presentationTimeOffset
   */ presentationTimeOffset (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the constant approximate Segment duration
   * NOTE: The <Period> element also contains an @duration attribute. This duration
   *       specifies the duration of the Period. This attribute is currently not
   *       supported by the rest of the parser, however we still check for it to prevent
   *       errors.
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed duration
   */ duration (value) {
        const parsedValue = parseInt(value, 10);
        if (isNaN(parsedValue)) {
            return parseDuration(value);
        }
        return parsedValue;
    },
    /**
   * Specifies the Segment duration, in units of the value of the @timescale.
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed duration
   */ d (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the MPD start time, in @timescale units, the first Segment in the series
   * starts relative to the beginning of the Period
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed time
   */ t (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the repeat count of the number of following contiguous Segments with the
   * same duration expressed by the value of @d
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {number}
   *         The parsed number
   */ r (value) {
        return parseInt(value, 10);
    },
    /**
   * Specifies the presentationTime.
   *
   * @param {string} value
   *        value of the attribute as a string
   *
   * @return {number}
   *         The parsed presentationTime
   */ presentationTime (value) {
        return parseInt(value, 10);
    },
    /**
   * Default parser for all other attributes. Acts as a no-op and just returns the value
   * as a string
   *
   * @param {string} value
   *        value of attribute as a string
   * @return {string}
   *         Unparsed value
   */ DEFAULT (value) {
        return value;
    }
};
/**
 * Gets all the attributes and values of the provided node, parses attributes with known
 * types, and returns an object with attribute names mapped to values.
 *
 * @param {Node} el
 *        The node to parse attributes from
 * @return {Object}
 *         Object with all attributes of el parsed
 */ const parseAttributes = (el)=>{
    if (!(el && el.attributes)) {
        return {};
    }
    return from(el.attributes).reduce((a, e)=>{
        const parseFn = parsers[e.name] || parsers.DEFAULT;
        a[e.name] = parseFn(e.value);
        return a;
    }, {});
};
const keySystemsMap = {
    'urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b': 'org.w3.clearkey',
    'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed': 'com.widevine.alpha',
    'urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95': 'com.microsoft.playready',
    'urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb': 'com.adobe.primetime',
    // ISO_IEC 23009-1_2022 5.8.5.2.2 The mp4 Protection Scheme
    'urn:mpeg:dash:mp4protection:2011': 'mp4protection'
};
/**
 * Builds a list of urls that is the product of the reference urls and BaseURL values
 *
 * @param {Object[]} references
 *        List of objects containing the reference URL as well as its attributes
 * @param {Node[]} baseUrlElements
 *        List of BaseURL nodes from the mpd
 * @return {Object[]}
 *         List of objects with resolved urls and attributes
 */ const buildBaseUrls = (references, baseUrlElements)=>{
    if (!baseUrlElements.length) {
        return references;
    }
    return flatten(references.map(function(reference) {
        return baseUrlElements.map(function(baseUrlElement) {
            const initialBaseUrl = getContent(baseUrlElement);
            const resolvedBaseUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$resolve$2d$url$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(reference.baseUrl, initialBaseUrl);
            const finalBaseUrl = merge(parseAttributes(baseUrlElement), {
                baseUrl: resolvedBaseUrl
            }); // If the URL is resolved, we want to get the serviceLocation from the reference
            // assuming there is no serviceLocation on the initialBaseUrl
            if (resolvedBaseUrl !== initialBaseUrl && !finalBaseUrl.serviceLocation && reference.serviceLocation) {
                finalBaseUrl.serviceLocation = reference.serviceLocation;
            }
            return finalBaseUrl;
        });
    }));
};
/**
 * Contains all Segment information for its containing AdaptationSet
 *
 * @typedef {Object} SegmentInformation
 * @property {Object|undefined} template
 *           Contains the attributes for the SegmentTemplate node
 * @property {Object[]|undefined} segmentTimeline
 *           Contains a list of atrributes for each S node within the SegmentTimeline node
 * @property {Object|undefined} list
 *           Contains the attributes for the SegmentList node
 * @property {Object|undefined} base
 *           Contains the attributes for the SegmentBase node
 */ /**
 * Returns all available Segment information contained within the AdaptationSet node
 *
 * @param {Node} adaptationSet
 *        The AdaptationSet node to get Segment information from
 * @return {SegmentInformation}
 *         The Segment information contained within the provided AdaptationSet
 */ const getSegmentInformation = (adaptationSet)=>{
    const segmentTemplate = findChildren(adaptationSet, 'SegmentTemplate')[0];
    const segmentList = findChildren(adaptationSet, 'SegmentList')[0];
    const segmentUrls = segmentList && findChildren(segmentList, 'SegmentURL').map((s)=>merge({
            tag: 'SegmentURL'
        }, parseAttributes(s)));
    const segmentBase = findChildren(adaptationSet, 'SegmentBase')[0];
    const segmentTimelineParentNode = segmentList || segmentTemplate;
    const segmentTimeline = segmentTimelineParentNode && findChildren(segmentTimelineParentNode, 'SegmentTimeline')[0];
    const segmentInitializationParentNode = segmentList || segmentBase || segmentTemplate;
    const segmentInitialization = segmentInitializationParentNode && findChildren(segmentInitializationParentNode, 'Initialization')[0]; // SegmentTemplate is handled slightly differently, since it can have both
    // @initialization and an <Initialization> node.  @initialization can be templated,
    // while the node can have a url and range specified.  If the <SegmentTemplate> has
    // both @initialization and an <Initialization> subelement we opt to override with
    // the node, as this interaction is not defined in the spec.
    const template = segmentTemplate && parseAttributes(segmentTemplate);
    if (template && segmentInitialization) {
        template.initialization = segmentInitialization && parseAttributes(segmentInitialization);
    } else if (template && template.initialization) {
        // If it is @initialization we convert it to an object since this is the format that
        // later functions will rely on for the initialization segment.  This is only valid
        // for <SegmentTemplate>
        template.initialization = {
            sourceURL: template.initialization
        };
    }
    const segmentInfo = {
        template,
        segmentTimeline: segmentTimeline && findChildren(segmentTimeline, 'S').map((s)=>parseAttributes(s)),
        list: segmentList && merge(parseAttributes(segmentList), {
            segmentUrls,
            initialization: parseAttributes(segmentInitialization)
        }),
        base: segmentBase && merge(parseAttributes(segmentBase), {
            initialization: parseAttributes(segmentInitialization)
        })
    };
    Object.keys(segmentInfo).forEach((key)=>{
        if (!segmentInfo[key]) {
            delete segmentInfo[key];
        }
    });
    return segmentInfo;
};
/**
 * Contains Segment information and attributes needed to construct a Playlist object
 * from a Representation
 *
 * @typedef {Object} RepresentationInformation
 * @property {SegmentInformation} segmentInfo
 *           Segment information for this Representation
 * @property {Object} attributes
 *           Inherited attributes for this Representation
 */ /**
 * Maps a Representation node to an object containing Segment information and attributes
 *
 * @name inheritBaseUrlsCallback
 * @function
 * @param {Node} representation
 *        Representation node from the mpd
 * @return {RepresentationInformation}
 *         Representation information needed to construct a Playlist object
 */ /**
 * Returns a callback for Array.prototype.map for mapping Representation nodes to
 * Segment information and attributes using inherited BaseURL nodes.
 *
 * @param {Object} adaptationSetAttributes
 *        Contains attributes inherited by the AdaptationSet
 * @param {Object[]} adaptationSetBaseUrls
 *        List of objects containing resolved base URLs and attributes
 *        inherited by the AdaptationSet
 * @param {SegmentInformation} adaptationSetSegmentInfo
 *        Contains Segment information for the AdaptationSet
 * @return {inheritBaseUrlsCallback}
 *         Callback map function
 */ const inheritBaseUrls = (adaptationSetAttributes, adaptationSetBaseUrls, adaptationSetSegmentInfo)=>(representation)=>{
        const repBaseUrlElements = findChildren(representation, 'BaseURL');
        const repBaseUrls = buildBaseUrls(adaptationSetBaseUrls, repBaseUrlElements);
        const attributes = merge(adaptationSetAttributes, parseAttributes(representation));
        const representationSegmentInfo = getSegmentInformation(representation);
        return repBaseUrls.map((baseUrl)=>{
            return {
                segmentInfo: merge(adaptationSetSegmentInfo, representationSegmentInfo),
                attributes: merge(attributes, baseUrl)
            };
        });
    };
/**
 * Tranforms a series of content protection nodes to
 * an object containing pssh data by key system
 *
 * @param {Node[]} contentProtectionNodes
 *        Content protection nodes
 * @return {Object}
 *        Object containing pssh data by key system
 */ const generateKeySystemInformation = (contentProtectionNodes)=>{
    return contentProtectionNodes.reduce((acc, node)=>{
        const attributes = parseAttributes(node); // Although it could be argued that according to the UUID RFC spec the UUID string (a-f chars) should be generated
        // as a lowercase string it also mentions it should be treated as case-insensitive on input. Since the key system
        // UUIDs in the keySystemsMap are hardcoded as lowercase in the codebase there isn't any reason not to do
        // .toLowerCase() on the input UUID string from the manifest (at least I could not think of one).
        if (attributes.schemeIdUri) {
            attributes.schemeIdUri = attributes.schemeIdUri.toLowerCase();
        }
        const keySystem = keySystemsMap[attributes.schemeIdUri];
        if (keySystem) {
            acc[keySystem] = {
                attributes
            };
            const psshNode = findChildren(node, 'cenc:pssh')[0];
            if (psshNode) {
                const pssh = getContent(psshNode);
                acc[keySystem].pssh = pssh && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$videojs$2f$vhs$2d$utils$2f$es$2f$decode$2d$b64$2d$to$2d$uint8$2d$array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pssh);
            }
        }
        return acc;
    }, {});
}; // defined in ANSI_SCTE 214-1 2016
const parseCaptionServiceMetadata = (service)=>{
    // 608 captions
    if (service.schemeIdUri === 'urn:scte:dash:cc:cea-608:2015') {
        const values = typeof service.value !== 'string' ? [] : service.value.split(';');
        return values.map((value)=>{
            let channel;
            let language; // default language to value
            language = value;
            if (/^CC\d=/.test(value)) {
                [channel, language] = value.split('=');
            } else if (/^CC\d$/.test(value)) {
                channel = value;
            }
            return {
                channel,
                language
            };
        });
    } else if (service.schemeIdUri === 'urn:scte:dash:cc:cea-708:2015') {
        const values = typeof service.value !== 'string' ? [] : service.value.split(';');
        return values.map((value)=>{
            const flags = {
                // service or channel number 1-63
                'channel': undefined,
                // language is a 3ALPHA per ISO 639.2/B
                // field is required
                'language': undefined,
                // BIT 1/0 or ?
                // default value is 1, meaning 16:9 aspect ratio, 0 is 4:3, ? is unknown
                'aspectRatio': 1,
                // BIT 1/0
                // easy reader flag indicated the text is tailed to the needs of beginning readers
                // default 0, or off
                'easyReader': 0,
                // BIT 1/0
                // If 3d metadata is present (CEA-708.1) then 1
                // default 0
                '3D': 0
            };
            if (/=/.test(value)) {
                const [channel, opts = ''] = value.split('=');
                flags.channel = channel;
                flags.language = value;
                opts.split(',').forEach((opt)=>{
                    const [name, val] = opt.split(':');
                    if (name === 'lang') {
                        flags.language = val; // er for easyReadery
                    } else if (name === 'er') {
                        flags.easyReader = Number(val); // war for wide aspect ratio
                    } else if (name === 'war') {
                        flags.aspectRatio = Number(val);
                    } else if (name === '3D') {
                        flags['3D'] = Number(val);
                    }
                });
            } else {
                flags.language = value;
            }
            if (flags.channel) {
                flags.channel = 'SERVICE' + flags.channel;
            }
            return flags;
        });
    }
};
/**
 * A map callback that will parse all event stream data for a collection of periods
 * DASH ISO_IEC_23009 5.10.2.2
 * https://dashif-documents.azurewebsites.net/Events/master/event.html#mpd-event-timing
 *
 * @param {PeriodInformation} period object containing necessary period information
 * @return a collection of parsed eventstream event objects
 */ const toEventStream = (period)=>{
    // get and flatten all EventStreams tags and parse attributes and children
    return flatten(findChildren(period.node, 'EventStream').map((eventStream)=>{
        const eventStreamAttributes = parseAttributes(eventStream);
        const schemeIdUri = eventStreamAttributes.schemeIdUri; // find all Events per EventStream tag and map to return objects
        return findChildren(eventStream, 'Event').map((event)=>{
            const eventAttributes = parseAttributes(event);
            const presentationTime = eventAttributes.presentationTime || 0;
            const timescale = eventStreamAttributes.timescale || 1;
            const duration = eventAttributes.duration || 0;
            const start = presentationTime / timescale + period.attributes.start;
            return {
                schemeIdUri,
                value: eventStreamAttributes.value,
                id: eventAttributes.id,
                start,
                end: start + duration / timescale,
                messageData: getContent(event) || eventAttributes.messageData,
                contentEncoding: eventStreamAttributes.contentEncoding,
                presentationTimeOffset: eventStreamAttributes.presentationTimeOffset || 0
            };
        });
    }));
};
/**
 * Maps an AdaptationSet node to a list of Representation information objects
 *
 * @name toRepresentationsCallback
 * @function
 * @param {Node} adaptationSet
 *        AdaptationSet node from the mpd
 * @return {RepresentationInformation[]}
 *         List of objects containing Representaion information
 */ /**
 * Returns a callback for Array.prototype.map for mapping AdaptationSet nodes to a list of
 * Representation information objects
 *
 * @param {Object} periodAttributes
 *        Contains attributes inherited by the Period
 * @param {Object[]} periodBaseUrls
 *        Contains list of objects with resolved base urls and attributes
 *        inherited by the Period
 * @param {string[]} periodSegmentInfo
 *        Contains Segment Information at the period level
 * @return {toRepresentationsCallback}
 *         Callback map function
 */ const toRepresentations = (periodAttributes, periodBaseUrls, periodSegmentInfo)=>(adaptationSet)=>{
        const adaptationSetAttributes = parseAttributes(adaptationSet);
        const adaptationSetBaseUrls = buildBaseUrls(periodBaseUrls, findChildren(adaptationSet, 'BaseURL'));
        const role = findChildren(adaptationSet, 'Role')[0];
        const roleAttributes = {
            role: parseAttributes(role)
        };
        let attrs = merge(periodAttributes, adaptationSetAttributes, roleAttributes);
        const accessibility = findChildren(adaptationSet, 'Accessibility')[0];
        const captionServices = parseCaptionServiceMetadata(parseAttributes(accessibility));
        if (captionServices) {
            attrs = merge(attrs, {
                captionServices
            });
        }
        const label = findChildren(adaptationSet, 'Label')[0];
        if (label && label.childNodes.length) {
            const labelVal = label.childNodes[0].nodeValue.trim();
            attrs = merge(attrs, {
                label: labelVal
            });
        }
        const contentProtection = generateKeySystemInformation(findChildren(adaptationSet, 'ContentProtection'));
        if (Object.keys(contentProtection).length) {
            attrs = merge(attrs, {
                contentProtection
            });
        }
        const segmentInfo = getSegmentInformation(adaptationSet);
        const representations = findChildren(adaptationSet, 'Representation');
        const adaptationSetSegmentInfo = merge(periodSegmentInfo, segmentInfo);
        return flatten(representations.map(inheritBaseUrls(attrs, adaptationSetBaseUrls, adaptationSetSegmentInfo)));
    };
/**
 * Contains all period information for mapping nodes onto adaptation sets.
 *
 * @typedef {Object} PeriodInformation
 * @property {Node} period.node
 *           Period node from the mpd
 * @property {Object} period.attributes
 *           Parsed period attributes from node plus any added
 */ /**
 * Maps a PeriodInformation object to a list of Representation information objects for all
 * AdaptationSet nodes contained within the Period.
 *
 * @name toAdaptationSetsCallback
 * @function
 * @param {PeriodInformation} period
 *        Period object containing necessary period information
 * @param {number} periodStart
 *        Start time of the Period within the mpd
 * @return {RepresentationInformation[]}
 *         List of objects containing Representaion information
 */ /**
 * Returns a callback for Array.prototype.map for mapping Period nodes to a list of
 * Representation information objects
 *
 * @param {Object} mpdAttributes
 *        Contains attributes inherited by the mpd
  * @param {Object[]} mpdBaseUrls
 *        Contains list of objects with resolved base urls and attributes
 *        inherited by the mpd
 * @return {toAdaptationSetsCallback}
 *         Callback map function
 */ const toAdaptationSets = (mpdAttributes, mpdBaseUrls)=>(period, index)=>{
        const periodBaseUrls = buildBaseUrls(mpdBaseUrls, findChildren(period.node, 'BaseURL'));
        const periodAttributes = merge(mpdAttributes, {
            periodStart: period.attributes.start
        });
        if (typeof period.attributes.duration === 'number') {
            periodAttributes.periodDuration = period.attributes.duration;
        }
        const adaptationSets = findChildren(period.node, 'AdaptationSet');
        const periodSegmentInfo = getSegmentInformation(period.node);
        return flatten(adaptationSets.map(toRepresentations(periodAttributes, periodBaseUrls, periodSegmentInfo)));
    };
/**
 * Tranforms an array of content steering nodes into an object
 * containing CDN content steering information from the MPD manifest.
 *
 * For more information on the DASH spec for Content Steering parsing, see:
 * https://dashif.org/docs/DASH-IF-CTS-00XX-Content-Steering-Community-Review.pdf
 *
 * @param {Node[]} contentSteeringNodes
 *        Content steering nodes
 * @param {Function} eventHandler
 *        The event handler passed into the parser options to handle warnings
 * @return {Object}
 *        Object containing content steering data
 */ const generateContentSteeringInformation = (contentSteeringNodes, eventHandler)=>{
    // If there are more than one ContentSteering tags, throw an error
    if (contentSteeringNodes.length > 1) {
        eventHandler({
            type: 'warn',
            message: 'The MPD manifest should contain no more than one ContentSteering tag'
        });
    } // Return a null value if there are no ContentSteering tags
    if (!contentSteeringNodes.length) {
        return null;
    }
    const infoFromContentSteeringTag = merge({
        serverURL: getContent(contentSteeringNodes[0])
    }, parseAttributes(contentSteeringNodes[0])); // Converts `queryBeforeStart` to a boolean, as well as setting the default value
    // to `false` if it doesn't exist
    infoFromContentSteeringTag.queryBeforeStart = infoFromContentSteeringTag.queryBeforeStart === 'true';
    return infoFromContentSteeringTag;
};
/**
 * Gets Period@start property for a given period.
 *
 * @param {Object} options
 *        Options object
 * @param {Object} options.attributes
 *        Period attributes
 * @param {Object} [options.priorPeriodAttributes]
 *        Prior period attributes (if prior period is available)
 * @param {string} options.mpdType
 *        The MPD@type these periods came from
 * @return {number|null}
 *         The period start, or null if it's an early available period or error
 */ const getPeriodStart = ({ attributes, priorPeriodAttributes, mpdType })=>{
    // Summary of period start time calculation from DASH spec section 5.3.2.1
    //
    // A period's start is the first period's start + time elapsed after playing all
    // prior periods to this one. Periods continue one after the other in time (without
    // gaps) until the end of the presentation.
    //
    // The value of Period@start should be:
    // 1. if Period@start is present: value of Period@start
    // 2. if previous period exists and it has @duration: previous Period@start +
    //    previous Period@duration
    // 3. if this is first period and MPD@type is 'static': 0
    // 4. in all other cases, consider the period an "early available period" (note: not
    //    currently supported)
    // (1)
    if (typeof attributes.start === 'number') {
        return attributes.start;
    } // (2)
    if (priorPeriodAttributes && typeof priorPeriodAttributes.start === 'number' && typeof priorPeriodAttributes.duration === 'number') {
        return priorPeriodAttributes.start + priorPeriodAttributes.duration;
    } // (3)
    if (!priorPeriodAttributes && mpdType === 'static') {
        return 0;
    } // (4)
    // There is currently no logic for calculating the Period@start value if there is
    // no Period@start or prior Period@start and Period@duration available. This is not made
    // explicit by the DASH interop guidelines or the DASH spec, however, since there's
    // nothing about any other resolution strategies, it's implied. Thus, this case should
    // be considered an early available period, or error, and null should suffice for both
    // of those cases.
    return null;
};
/**
 * Traverses the mpd xml tree to generate a list of Representation information objects
 * that have inherited attributes from parent nodes
 *
 * @param {Node} mpd
 *        The root node of the mpd
 * @param {Object} options
 *        Available options for inheritAttributes
 * @param {string} options.manifestUri
 *        The uri source of the mpd
 * @param {number} options.NOW
 *        Current time per DASH IOP.  Default is current time in ms since epoch
 * @param {number} options.clientOffset
 *        Client time difference from NOW (in milliseconds)
 * @return {RepresentationInformation[]}
 *         List of objects containing Representation information
 */ const inheritAttributes = (mpd, options = {})=>{
    const { manifestUri = '', NOW = Date.now(), clientOffset = 0, // TODO: For now, we are expecting an eventHandler callback function
    // to be passed into the mpd parser as an option.
    // In the future, we should enable stream parsing by using the Stream class from vhs-utils.
    // This will support new features including a standardized event handler.
    // See the m3u8 parser for examples of how stream parsing is currently used for HLS parsing.
    // https://github.com/videojs/vhs-utils/blob/88d6e10c631e57a5af02c5a62bc7376cd456b4f5/src/stream.js#L9
    eventHandler = function() {} } = options;
    const periodNodes = findChildren(mpd, 'Period');
    if (!periodNodes.length) {
        throw new Error(errors.INVALID_NUMBER_OF_PERIOD);
    }
    const locations = findChildren(mpd, 'Location');
    const mpdAttributes = parseAttributes(mpd);
    const mpdBaseUrls = buildBaseUrls([
        {
            baseUrl: manifestUri
        }
    ], findChildren(mpd, 'BaseURL'));
    const contentSteeringNodes = findChildren(mpd, 'ContentSteering'); // See DASH spec section 5.3.1.2, Semantics of MPD element. Default type to 'static'.
    mpdAttributes.type = mpdAttributes.type || 'static';
    mpdAttributes.sourceDuration = mpdAttributes.mediaPresentationDuration || 0;
    mpdAttributes.NOW = NOW;
    mpdAttributes.clientOffset = clientOffset;
    if (locations.length) {
        mpdAttributes.locations = locations.map(getContent);
    }
    const periods = []; // Since toAdaptationSets acts on individual periods right now, the simplest approach to
    // adding properties that require looking at prior periods is to parse attributes and add
    // missing ones before toAdaptationSets is called. If more such properties are added, it
    // may be better to refactor toAdaptationSets.
    periodNodes.forEach((node, index)=>{
        const attributes = parseAttributes(node); // Use the last modified prior period, as it may contain added information necessary
        // for this period.
        const priorPeriod = periods[index - 1];
        attributes.start = getPeriodStart({
            attributes,
            priorPeriodAttributes: priorPeriod ? priorPeriod.attributes : null,
            mpdType: mpdAttributes.type
        });
        periods.push({
            node,
            attributes
        });
    });
    return {
        locations: mpdAttributes.locations,
        contentSteeringInfo: generateContentSteeringInformation(contentSteeringNodes, eventHandler),
        // TODO: There are occurences where this `representationInfo` array contains undesired
        // duplicates. This generally occurs when there are multiple BaseURL nodes that are
        // direct children of the MPD node. When we attempt to resolve URLs from a combination of the
        // parent BaseURL and a child BaseURL, and the value does not resolve,
        // we end up returning the child BaseURL multiple times.
        // We need to determine a way to remove these duplicates in a safe way.
        // See: https://github.com/videojs/mpd-parser/pull/17#discussion_r162750527
        representationInfo: flatten(periods.map(toAdaptationSets(mpdAttributes, mpdBaseUrls))),
        eventStream: flatten(periods.map(toEventStream))
    };
};
const stringToMpdXml = (manifestString)=>{
    if (manifestString === '') {
        throw new Error(errors.DASH_EMPTY_MANIFEST);
    }
    const parser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$xmldom$2f$xmldom$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMParser"]();
    let xml;
    let mpd;
    try {
        xml = parser.parseFromString(manifestString, 'application/xml');
        mpd = xml && xml.documentElement.tagName === 'MPD' ? xml.documentElement : null;
    } catch (e) {}
    if (!mpd || mpd && mpd.getElementsByTagName('parsererror').length > 0) {
        throw new Error(errors.DASH_INVALID_XML);
    }
    return mpd;
};
/**
 * Parses the manifest for a UTCTiming node, returning the nodes attributes if found
 *
 * @param {string} mpd
 *        XML string of the MPD manifest
 * @return {Object|null}
 *         Attributes of UTCTiming node specified in the manifest. Null if none found
 */ const parseUTCTimingScheme = (mpd)=>{
    const UTCTimingNode = findChildren(mpd, 'UTCTiming')[0];
    if (!UTCTimingNode) {
        return null;
    }
    const attributes = parseAttributes(UTCTimingNode);
    switch(attributes.schemeIdUri){
        case 'urn:mpeg:dash:utc:http-head:2014':
        case 'urn:mpeg:dash:utc:http-head:2012':
            attributes.method = 'HEAD';
            break;
        case 'urn:mpeg:dash:utc:http-xsdate:2014':
        case 'urn:mpeg:dash:utc:http-iso:2014':
        case 'urn:mpeg:dash:utc:http-xsdate:2012':
        case 'urn:mpeg:dash:utc:http-iso:2012':
            attributes.method = 'GET';
            break;
        case 'urn:mpeg:dash:utc:direct:2014':
        case 'urn:mpeg:dash:utc:direct:2012':
            attributes.method = 'DIRECT';
            attributes.value = Date.parse(attributes.value);
            break;
        case 'urn:mpeg:dash:utc:http-ntp:2014':
        case 'urn:mpeg:dash:utc:ntp:2014':
        case 'urn:mpeg:dash:utc:sntp:2014':
        default:
            throw new Error(errors.UNSUPPORTED_UTC_TIMING_SCHEME);
    }
    return attributes;
};
const VERSION = version;
/*
 * Given a DASH manifest string and options, parses the DASH manifest into an object in the
 * form outputed by m3u8-parser and accepted by videojs/http-streaming.
 *
 * For live DASH manifests, if `previousManifest` is provided in options, then the newly
 * parsed DASH manifest will have its media sequence and discontinuity sequence values
 * updated to reflect its position relative to the prior manifest.
 *
 * @param {string} manifestString - the DASH manifest as a string
 * @param {options} [options] - any options
 *
 * @return {Object} the manifest object
 */ const parse = (manifestString, options = {})=>{
    const parsedManifestInfo = inheritAttributes(stringToMpdXml(manifestString), options);
    const playlists = toPlaylists(parsedManifestInfo.representationInfo);
    return toM3u8({
        dashPlaylists: playlists,
        locations: parsedManifestInfo.locations,
        contentSteering: parsedManifestInfo.contentSteeringInfo,
        sidxMapping: options.sidxMapping,
        previousManifest: options.previousManifest,
        eventStream: parsedManifestInfo.eventStream
    });
};
/**
 * Parses the manifest for a UTCTiming node, returning the nodes attributes if found
 *
 * @param {string} manifestString
 *        XML string of the MPD manifest
 * @return {Object|null}
 *         Attributes of UTCTiming node specified in the manifest. Null if none found
 */ const parseUTCTiming = (manifestString)=>parseUTCTimingScheme(stringToMpdXml(manifestString));
;
}}),
"[project]/node_modules/mux.js/lib/utils/numbers.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
var MAX_UINT32 = Math.pow(2, 32);
var getUint64 = function(uint8) {
    var dv = new DataView(uint8.buffer, uint8.byteOffset, uint8.byteLength);
    var value;
    if (dv.getBigUint64) {
        value = dv.getBigUint64(0);
        if (value < Number.MAX_SAFE_INTEGER) {
            return Number(value);
        }
        return value;
    }
    return dv.getUint32(0) * MAX_UINT32 + dv.getUint32(4);
};
module.exports = {
    getUint64: getUint64,
    MAX_UINT32: MAX_UINT32
};
}}),
"[project]/node_modules/mux.js/lib/tools/parse-sidx.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
var getUint64 = __turbopack_require__("[project]/node_modules/mux.js/lib/utils/numbers.js [app-client] (ecmascript)").getUint64;
var parseSidx = function(data) {
    var view = new DataView(data.buffer, data.byteOffset, data.byteLength), result = {
        version: data[0],
        flags: new Uint8Array(data.subarray(1, 4)),
        references: [],
        referenceId: view.getUint32(4),
        timescale: view.getUint32(8)
    }, i = 12;
    if (result.version === 0) {
        result.earliestPresentationTime = view.getUint32(i);
        result.firstOffset = view.getUint32(i + 4);
        i += 8;
    } else {
        // read 64 bits
        result.earliestPresentationTime = getUint64(data.subarray(i));
        result.firstOffset = getUint64(data.subarray(i + 8));
        i += 16;
    }
    i += 2; // reserved
    var referenceCount = view.getUint16(i);
    i += 2; // start of references
    for(; referenceCount > 0; i += 12, referenceCount--){
        result.references.push({
            referenceType: (data[i] & 0x80) >>> 7,
            referencedSize: view.getUint32(i) & 0x7FFFFFFF,
            subsegmentDuration: view.getUint32(i + 4),
            startsWithSap: !!(data[i + 8] & 0x80),
            sapType: (data[i + 8] & 0x70) >>> 4,
            sapDeltaTime: view.getUint32(i + 8) & 0x0FFFFFFF
        });
    }
    return result;
};
module.exports = parseSidx;
}}),
"[project]/node_modules/mux.js/lib/utils/clock.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
/**
 * mux.js
 *
 * Copyright (c) Brightcove
 * Licensed Apache-2.0 https://github.com/videojs/mux.js/blob/master/LICENSE
 */ var ONE_SECOND_IN_TS = 90000, secondsToVideoTs, secondsToAudioTs, videoTsToSeconds, audioTsToSeconds, audioTsToVideoTs, videoTsToAudioTs, metadataTsToSeconds;
secondsToVideoTs = function(seconds) {
    return seconds * ONE_SECOND_IN_TS;
};
secondsToAudioTs = function(seconds, sampleRate) {
    return seconds * sampleRate;
};
videoTsToSeconds = function(timestamp) {
    return timestamp / ONE_SECOND_IN_TS;
};
audioTsToSeconds = function(timestamp, sampleRate) {
    return timestamp / sampleRate;
};
audioTsToVideoTs = function(timestamp, sampleRate) {
    return secondsToVideoTs(audioTsToSeconds(timestamp, sampleRate));
};
videoTsToAudioTs = function(timestamp, sampleRate) {
    return secondsToAudioTs(videoTsToSeconds(timestamp), sampleRate);
};
/**
 * Adjust ID3 tag or caption timing information by the timeline pts values
 * (if keepOriginalTimestamps is false) and convert to seconds
 */ metadataTsToSeconds = function(timestamp, timelineStartPts, keepOriginalTimestamps) {
    return videoTsToSeconds(keepOriginalTimestamps ? timestamp : timestamp - timelineStartPts);
};
module.exports = {
    ONE_SECOND_IN_TS: ONE_SECOND_IN_TS,
    secondsToVideoTs: secondsToVideoTs,
    secondsToAudioTs: secondsToAudioTs,
    videoTsToSeconds: videoTsToSeconds,
    audioTsToSeconds: audioTsToSeconds,
    audioTsToVideoTs: audioTsToVideoTs,
    videoTsToAudioTs: videoTsToAudioTs,
    metadataTsToSeconds: metadataTsToSeconds
};
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>RotateCcw)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",
            key: "1357e3"
        }
    ],
    [
        "path",
        {
            d: "M3 3v5h5",
            key: "1xhq8a"
        }
    ]
];
const RotateCcw = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("rotate-ccw", __iconNode);
;
 //# sourceMappingURL=rotate-ccw.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-client] (ecmascript) <export default as RotateCcw>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "RotateCcw": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Play)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "polygon",
        {
            points: "6 3 20 12 6 21 6 3",
            key: "1oa8hb"
        }
    ]
];
const Play = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("play", __iconNode);
;
 //# sourceMappingURL=play.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-client] (ecmascript) <export default as Play>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Play": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Pause)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "rect",
        {
            x: "14",
            y: "4",
            width: "4",
            height: "16",
            rx: "1",
            key: "zuxfzm"
        }
    ],
    [
        "rect",
        {
            x: "6",
            y: "4",
            width: "4",
            height: "16",
            rx: "1",
            key: "1okwgv"
        }
    ]
];
const Pause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("pause", __iconNode);
;
 //# sourceMappingURL=pause.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript) <export default as Pause>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Pause": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/volume-2.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Volume2)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",
            key: "uqj9uw"
        }
    ],
    [
        "path",
        {
            d: "M16 9a5 5 0 0 1 0 6",
            key: "1q6k2b"
        }
    ],
    [
        "path",
        {
            d: "M19.364 18.364a9 9 0 0 0 0-12.728",
            key: "ijwkga"
        }
    ]
];
const Volume2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("volume-2", __iconNode);
;
 //# sourceMappingURL=volume-2.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/volume-2.js [app-client] (ecmascript) <export default as Volume2>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Volume2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/volume-2.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/volume-x.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>VolumeX)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",
            key: "uqj9uw"
        }
    ],
    [
        "line",
        {
            x1: "22",
            x2: "16",
            y1: "9",
            y2: "15",
            key: "1ewh16"
        }
    ],
    [
        "line",
        {
            x1: "16",
            x2: "22",
            y1: "9",
            y2: "15",
            key: "5ykzw1"
        }
    ]
];
const VolumeX = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("volume-x", __iconNode);
;
 //# sourceMappingURL=volume-x.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/volume-x.js [app-client] (ecmascript) <export default as VolumeX>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "VolumeX": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/volume-x.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Maximize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M8 3H5a2 2 0 0 0-2 2v3",
            key: "1dcmit"
        }
    ],
    [
        "path",
        {
            d: "M21 8V5a2 2 0 0 0-2-2h-3",
            key: "1e4gt3"
        }
    ],
    [
        "path",
        {
            d: "M3 16v3a2 2 0 0 0 2 2h3",
            key: "wsl5sc"
        }
    ],
    [
        "path",
        {
            d: "M16 21h3a2 2 0 0 0 2-2v-3",
            key: "18trek"
        }
    ]
];
const Maximize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("maximize", __iconNode);
;
 //# sourceMappingURL=maximize.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-client] (ecmascript) <export default as Maximize>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Maximize": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$maximize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$maximize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Minimize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M8 3v3a2 2 0 0 1-2 2H3",
            key: "hohbtr"
        }
    ],
    [
        "path",
        {
            d: "M21 8h-3a2 2 0 0 1-2-2V3",
            key: "5jw1f3"
        }
    ],
    [
        "path",
        {
            d: "M3 16h3a2 2 0 0 1 2 2v3",
            key: "198tvr"
        }
    ],
    [
        "path",
        {
            d: "M16 21v-3a2 2 0 0 1 2-2h3",
            key: "ph8mxp"
        }
    ]
];
const Minimize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("minimize", __iconNode);
;
 //# sourceMappingURL=minimize.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-client] (ecmascript) <export default as Minimize>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Minimize": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minimize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minimize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Funnel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",
            key: "sc7q7i"
        }
    ]
];
const Funnel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("funnel", __iconNode);
;
 //# sourceMappingURL=funnel.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as Filter>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Filter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Users)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",
            key: "1yyitq"
        }
    ],
    [
        "path",
        {
            d: "M16 3.128a4 4 0 0 1 0 7.744",
            key: "16gr8j"
        }
    ],
    [
        "path",
        {
            d: "M22 21v-2a4 4 0 0 0-3-3.87",
            key: "kshegd"
        }
    ],
    [
        "circle",
        {
            cx: "9",
            cy: "7",
            r: "4",
            key: "nufk8"
        }
    ]
];
const Users = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("users", __iconNode);
;
 //# sourceMappingURL=users.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Users": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/list.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>List)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M3 12h.01",
            key: "nlz23k"
        }
    ],
    [
        "path",
        {
            d: "M3 18h.01",
            key: "1tta3j"
        }
    ],
    [
        "path",
        {
            d: "M3 6h.01",
            key: "1rqtza"
        }
    ],
    [
        "path",
        {
            d: "M8 12h13",
            key: "1za7za"
        }
    ],
    [
        "path",
        {
            d: "M8 18h13",
            key: "1lx6n3"
        }
    ],
    [
        "path",
        {
            d: "M8 6h13",
            key: "ik3vkj"
        }
    ]
];
const List = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("list", __iconNode);
;
 //# sourceMappingURL=list.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/list.js [app-client] (ecmascript) <export default as List>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "List": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/list.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Plus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M5 12h14",
            key: "1ays0h"
        }
    ],
    [
        "path",
        {
            d: "M12 5v14",
            key: "s699le"
        }
    ]
];
const Plus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("plus", __iconNode);
;
 //# sourceMappingURL=plus.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Plus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Monitor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "rect",
        {
            width: "20",
            height: "14",
            x: "2",
            y: "3",
            rx: "2",
            key: "48i651"
        }
    ],
    [
        "line",
        {
            x1: "8",
            x2: "16",
            y1: "21",
            y2: "21",
            key: "1svkeh"
        }
    ],
    [
        "line",
        {
            x1: "12",
            x2: "12",
            y1: "17",
            y2: "21",
            key: "vw1qmm"
        }
    ]
];
const Monitor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("monitor", __iconNode);
;
 //# sourceMappingURL=monitor.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript) <export default as Monitor>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Monitor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$monitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$monitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>RefreshCw)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",
            key: "v9h5vc"
        }
    ],
    [
        "path",
        {
            d: "M21 3v5h-5",
            key: "1q7to0"
        }
    ],
    [
        "path",
        {
            d: "M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",
            key: "3uifl3"
        }
    ],
    [
        "path",
        {
            d: "M8 16H3v5",
            key: "1cv678"
        }
    ]
];
const RefreshCw = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("refresh-cw", __iconNode);
;
 //# sourceMappingURL=refresh-cw.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "RefreshCw": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_esm__({
    "__iconNode": (()=>__iconNode),
    "default": (()=>CircleAlert)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "circle",
        {
            cx: "12",
            cy: "12",
            r: "10",
            key: "1mglay"
        }
    ],
    [
        "line",
        {
            x1: "12",
            x2: "12",
            y1: "8",
            y2: "12",
            key: "1pkeuh"
        }
    ],
    [
        "line",
        {
            x1: "12",
            x2: "12.01",
            y1: "16",
            y2: "16",
            key: "4dfq90"
        }
    ]
];
const CircleAlert = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("circle-alert", __iconNode);
;
 //# sourceMappingURL=circle-alert.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AlertCircle": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=node_modules_abf0f2._.js.map