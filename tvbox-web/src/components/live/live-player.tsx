'use client';

import React, { useState, useEffect } from 'react';
import VideoPlayer from '@/components/player/video-player';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  WifiOff,
  Clock,
  Signal
} from 'lucide-react';
import type { LiveChannel } from '@/types';

interface LivePlayerProps {
  channel: LiveChannel;
  onError?: (error: string) => void;
  onSuccess?: () => void;
}

const LivePlayer: React.FC<LivePlayerProps> = ({
  channel,
  onError,
  onSuccess,
}) => {
  const [currentUrlIndex, setCurrentUrlIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'failed'>('connecting');

  const urls = channel.urls || [channel.url];
  const currentUrl = urls[currentUrlIndex];

  // 重置狀態
  const resetState = () => {
    setError(null);
    setRetryCount(0);
    setConnectionStatus('connecting');
  };

  // 切換到下一個 URL
  const switchToNextUrl = () => {
    if (currentUrlIndex < urls.length - 1) {
      setCurrentUrlIndex(currentUrlIndex + 1);
      resetState();
      return true;
    }
    return false;
  };

  // 重試當前 URL
  const retryCurrentUrl = () => {
    if (retryCount < 3) {
      setRetryCount(retryCount + 1);
      resetState();
      return true;
    }
    return false;
  };

  // 處理播放錯誤
  const handlePlayError = (errorMessage: string) => {
    console.error(`播放錯誤 (URL ${currentUrlIndex + 1}/${urls.length}):`, errorMessage);
    setError(errorMessage);
    setConnectionStatus('failed');

    // 嘗試重試或切換 URL
    setTimeout(() => {
      if (retryCurrentUrl()) {
        console.log(`重試 URL ${currentUrlIndex + 1} (第 ${retryCount + 1} 次)`);
      } else if (switchToNextUrl()) {
        console.log(`切換到備用 URL ${currentUrlIndex + 2}`);
      } else {
        console.log('所有 URL 都已嘗試，播放失敗');
        onError?.(errorMessage);
      }
    }, 2000);
  };

  // 處理播放成功
  const handlePlaySuccess = () => {
    setConnectionStatus('connected');
    setError(null);
    onSuccess?.();
  };

  // 手動重試
  const manualRetry = () => {
    resetState();
    setCurrentUrlIndex(0);
  };

  // 檢測 URL 類型
  const getUrlType = (url: string) => {
    if (url.includes('.m3u8')) return 'HLS';
    if (url.includes('.mpd')) return 'DASH';
    if (url.includes('rtmp://')) return 'RTMP';
    if (url.includes('rtsp://')) return 'RTSP';
    return 'HTTP';
  };

  // 檢測 URL 質量
  const getUrlQuality = (url: string) => {
    if (url.includes('1080') || url.includes('HD')) return '高清';
    if (url.includes('720')) return '標清';
    if (url.includes('480')) return '流暢';
    return '自動';
  };

  return (
    <div className="space-y-4">
      {/* 頻道信息 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {channel.logo && (
                <img 
                  src={channel.logo} 
                  alt={channel.name}
                  className="w-8 h-8 rounded"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              )}
              <span>{channel.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={connectionStatus === 'connected' ? 'default' : 'secondary'}>
                {connectionStatus === 'connecting' && <Clock className="w-3 h-3 mr-1" />}
                {connectionStatus === 'connected' && <Signal className="w-3 h-3 mr-1" />}
                {connectionStatus === 'failed' && <WifiOff className="w-3 h-3 mr-1" />}
                {connectionStatus === 'connecting' && '連接中'}
                {connectionStatus === 'connected' && '已連接'}
                {connectionStatus === 'failed' && '連接失敗'}
              </Badge>
              {urls.length > 1 && (
                <Badge variant="outline">
                  {currentUrlIndex + 1}/{urls.length}
                </Badge>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* URL 信息 */}
          <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
            <div className="flex items-center space-x-4">
              <span>類型: {getUrlType(currentUrl)}</span>
              <span>質量: {getUrlQuality(currentUrl)}</span>
              {retryCount > 0 && <span>重試: {retryCount}/3</span>}
            </div>
            {urls.length > 1 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (switchToNextUrl()) {
                    console.log(`手動切換到 URL ${currentUrlIndex + 2}`);
                  }
                }}
                disabled={currentUrlIndex >= urls.length - 1}
              >
                切換源
              </Button>
            )}
          </div>

          {/* 錯誤提示 */}
          {error && (
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={manualRetry}
                  className="ml-2"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  重試
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* 視頻播放器 */}
          <VideoPlayer
            src={currentUrl}
            poster={channel.logo}
            autoplay={true}
            className="aspect-video"
            onError={handlePlayError}
            onTimeUpdate={() => {
              if (connectionStatus !== 'connected') {
                handlePlaySuccess();
              }
            }}
          />

          {/* 頻道詳情 */}
          {channel.group && (
            <div className="mt-3 text-sm text-muted-foreground">
              分組: {channel.group}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 調試信息 (開發環境) */}
      {process.env.NODE_ENV === 'development' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">調試信息</CardTitle>
          </CardHeader>
          <CardContent className="text-xs space-y-2">
            <div>當前 URL: {currentUrl}</div>
            <div>重試次數: {retryCount}</div>
            <div>連接狀態: {connectionStatus}</div>
            {urls.length > 1 && (
              <div>
                所有源:
                <ul className="list-disc list-inside ml-2 mt-1">
                  {urls.map((url, index) => (
                    <li key={index} className={index === currentUrlIndex ? 'font-bold' : ''}>
                      {index + 1}. {getUrlType(url)} - {url.substring(0, 50)}...
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LivePlayer;
