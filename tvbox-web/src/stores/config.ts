import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { 
  ApiConfig, 
  SourceBean, 
  ParseBean, 
  AppSettings,
  LiveChannelGroup,
  IJKCode 
} from '@/types';

interface ConfigState {
  // 配置數據
  apiConfig: ApiConfig | null;
  sources: SourceBean[];
  parses: ParseBean[];
  liveChannels: LiveChannelGroup[];
  ijkCodes: IJKCode[];
  
  // 當前選中的配置
  homeSource: SourceBean | null;
  defaultParse: ParseBean | null;
  
  // 應用設置
  settings: AppSettings;
  
  // 加載狀態
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setApiConfig: (config: ApiConfig) => void;
  setHomeSource: (source: SourceBean) => void;
  setDefaultParse: (parse: ParseBean) => void;
  updateSettings: (settings: Partial<AppSettings>) => void;
  loadConfig: (apiUrl: string) => Promise<void>;
  clearConfig: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const defaultSettings: AppSettings = {
  apiUrl: '',
  homeSourceKey: '',
  defaultParseKey: '',
  playerType: 1, // 1: IJK
  livePlayerType: 1,
  searchView: 0, // 0: 列表
  homeRec: 0, // 0: 豆瓣熱播
  fastSearchMode: false,
  showPreview: true,
  debugMode: false,
};

export const useConfigStore = create<ConfigState>()(
  persist(
    (set, get) => ({
      // 初始狀態
      apiConfig: null,
      sources: [],
      parses: [],
      liveChannels: [],
      ijkCodes: [],
      homeSource: null,
      defaultParse: null,
      settings: defaultSettings,
      isLoading: false,
      error: null,

      // Actions
      setApiConfig: (config: ApiConfig) => {
        set({
          apiConfig: config,
          sources: config.sites || [],
          parses: config.parses || [],
          liveChannels: config.lives || [],
          ijkCodes: config.ijkCodes || [],
        });
        
        // 設置默認首頁源
        const { settings } = get();
        if (config.sites && config.sites.length > 0) {
          const homeSource = settings.homeSourceKey 
            ? config.sites.find(s => s.key === settings.homeSourceKey)
            : config.sites.find(s => s.filterable === 1) || config.sites[0];
          
          if (homeSource) {
            get().setHomeSource(homeSource);
          }
        }
        
        // 設置默認解析器
        if (config.parses && config.parses.length > 0) {
          const defaultParse = settings.defaultParseKey
            ? config.parses.find(p => p.name === settings.defaultParseKey)
            : config.parses[0];
          
          if (defaultParse) {
            get().setDefaultParse(defaultParse);
          }
        }
      },

      setHomeSource: (source: SourceBean) => {
        set({ homeSource: source });
        get().updateSettings({ homeSourceKey: source.key });
      },

      setDefaultParse: (parse: ParseBean) => {
        // 清除之前的默認標記
        const { parses } = get();
        const updatedParses = parses.map(p => ({ ...p, isDefault: false }));
        
        // 設置新的默認解析器
        parse.isDefault = true;
        const newParses = updatedParses.map(p => 
          p.name === parse.name ? parse : p
        );
        
        set({ 
          defaultParse: parse,
          parses: newParses 
        });
        get().updateSettings({ defaultParseKey: parse.name });
      },

      updateSettings: (newSettings: Partial<AppSettings>) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings }
        }));
      },

      loadConfig: async (apiUrl: string) => {
        set({ isLoading: true, error: null });
        
        try {
          // 這裡實現配置加載邏輯
          const response = await fetch(apiUrl, {
            headers: {
              'User-Agent': 'TVBox/1.0.0',
              'Accept': 'application/json, text/plain, */*'
            }
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const configText = await response.text();
          let config: ApiConfig;
          
          try {
            config = JSON.parse(configText);
          } catch (parseError) {
            throw new Error('配置文件格式錯誤，請檢查 JSON 格式');
          }
          
          // 驗證配置格式
          if (!config.sites || !Array.isArray(config.sites)) {
            throw new Error('配置文件缺少有效的 sites 數據');
          }
          
          get().setApiConfig(config);
          get().updateSettings({ apiUrl });
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '加載配置失敗';
          set({ error: errorMessage });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      clearConfig: () => {
        set({
          apiConfig: null,
          sources: [],
          parses: [],
          liveChannels: [],
          ijkCodes: [],
          homeSource: null,
          defaultParse: null,
          error: null,
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },
    }),
    {
      name: 'tvbox-config',
      partialize: (state) => ({
        settings: state.settings,
        // 只持久化設置，配置數據每次重新加載
      }),
    }
  )
);
