'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import MovieCard from './movie-card';
import type { Movie } from '@/types';
import { cn } from '@/lib/utils';

interface MovieGridProps {
  movies: Movie[];
  sourceKey: string;
  loading?: boolean;
  className?: string;
  onMoviePlay?: (movie: Movie) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

const MovieGrid: React.FC<MovieGridProps> = ({
  movies,
  sourceKey,
  loading = false,
  className,
  onMoviePlay,
  onLoadMore,
  hasMore = false,
}) => {
  // 無限滾動檢測
  React.useEffect(() => {
    if (!hasMore || loading) return;

    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      if (scrollTop + clientHeight >= scrollHeight - 1000) {
        onLoadMore?.();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, loading, onLoadMore]);

  if (movies.length === 0 && !loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground mb-2">暫無內容</div>
        <div className="text-sm text-muted-foreground">
          請嘗試切換其他分類或檢查網絡連接
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 影片網格 */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {movies.map((movie, index) => (
          <MovieCard
            key={`${movie.vodId}-${index}`}
            movie={movie}
            sourceKey={sourceKey}
            onPlay={onMoviePlay}
          />
        ))}
      </div>

      {/* 加載狀態 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span className="text-muted-foreground">加載中...</span>
        </div>
      )}

      {/* 加載更多按鈕 */}
      {hasMore && !loading && (
        <div className="flex justify-center py-4">
          <button
            onClick={onLoadMore}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            加載更多
          </button>
        </div>
      )}

      {/* 沒有更多內容 */}
      {!hasMore && movies.length > 0 && (
        <div className="text-center py-4 text-muted-foreground text-sm">
          已顯示全部內容
        </div>
      )}
    </div>
  );
};

export default MovieGrid;
