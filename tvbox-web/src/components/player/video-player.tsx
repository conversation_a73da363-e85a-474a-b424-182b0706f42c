'use client';

import React, { useRef, useEffect, useState } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import { Play, Pause, Volume2, VolumeX, Maximize, Minimize, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePlayerStore } from '@/stores/player';
import type { PlayInfo } from '@/types';
import { cn } from '@/lib/utils';

interface VideoPlayerProps {
  src?: string;
  poster?: string;
  className?: string;
  autoplay?: boolean;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onEnded?: () => void;
  onError?: (error: string) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  className,
  autoplay = false,
  onTimeUpdate,
  onEnded,
  onError,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { playerState, updatePlayerState } = usePlayerStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current || isInitialized) return;

    const player = videojs(videoRef.current, {
      controls: true,
      responsive: true,
      fluid: true,
      playbackRates: [0.5, 1, 1.25, 1.5, 2],
      poster: poster,
      preload: 'metadata',
      html5: {
        vhs: {
          overrideNative: true,
        },
        nativeVideoTracks: false,
        nativeAudioTracks: false,
        nativeTextTracks: false,
      },
    });

    playerRef.current = player;
    setIsInitialized(true);

    // 事件監聽
    player.on('loadstart', () => {
      updatePlayerState({ isLoading: true, error: undefined });
    });

    player.on('canplay', () => {
      updatePlayerState({ isLoading: false });
    });

    player.on('play', () => {
      updatePlayerState({ isPlaying: true });
    });

    player.on('pause', () => {
      updatePlayerState({ isPlaying: false });
    });

    player.on('timeupdate', () => {
      const currentTime = player.currentTime();
      const duration = player.duration();
      updatePlayerState({ currentTime, duration });
      onTimeUpdate?.(currentTime, duration);
    });

    player.on('volumechange', () => {
      updatePlayerState({ volume: player.volume() });
    });

    player.on('ratechange', () => {
      updatePlayerState({ playbackRate: player.playbackRate() });
    });

    player.on('fullscreenchange', () => {
      updatePlayerState({ isFullscreen: player.isFullscreen() });
    });

    player.on('ended', () => {
      updatePlayerState({ isPlaying: false });
      onEnded?.();
    });

    player.on('error', (e: any) => {
      const error = player.error();
      const errorMessage = error ? `播放錯誤: ${error.message}` : '播放錯誤';
      updatePlayerState({ error: errorMessage, isLoading: false });
      onError?.(errorMessage);
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
        setIsInitialized(false);
      }
    };
  }, []);

  // 更新播放源
  useEffect(() => {
    if (!playerRef.current || !src) return;

    const player = playerRef.current;
    
    try {
      player.src({
        src: src,
        type: getVideoType(src),
      });

      if (autoplay) {
        player.ready(() => {
          player.play().catch((error: any) => {
            console.warn('自動播放失敗:', error);
          });
        });
      }
    } catch (error) {
      console.error('設置播放源失敗:', error);
      onError?.('設置播放源失敗');
    }
  }, [src, autoplay]);

  // 獲取視頻類型
  const getVideoType = (url: string): string => {
    if (url.includes('.m3u8')) return 'application/x-mpegURL';
    if (url.includes('.mpd')) return 'application/dash+xml';
    if (url.includes('.mp4')) return 'video/mp4';
    if (url.includes('.webm')) return 'video/webm';
    if (url.includes('.ogg')) return 'video/ogg';
    return 'video/mp4'; // 默認
  };

  // 播放/暫停
  const togglePlay = () => {
    if (!playerRef.current) return;
    
    if (playerState.isPlaying) {
      playerRef.current.pause();
    } else {
      playerRef.current.play();
    }
  };

  // 靜音/取消靜音
  const toggleMute = () => {
    if (!playerRef.current) return;
    playerRef.current.muted(!playerRef.current.muted());
  };

  // 全屏/退出全屏
  const toggleFullscreen = () => {
    if (!playerRef.current) return;
    
    if (playerState.isFullscreen) {
      playerRef.current.exitFullscreen();
    } else {
      playerRef.current.requestFullscreen();
    }
  };

  // 重新加載
  const reload = () => {
    if (!playerRef.current || !src) return;
    
    const currentTime = playerRef.current.currentTime();
    playerRef.current.load();
    playerRef.current.ready(() => {
      playerRef.current.currentTime(currentTime);
    });
  };

  return (
    <div 
      ref={containerRef}
      className={cn("relative bg-black rounded-lg overflow-hidden", className)}
    >
      {/* Video.js 播放器 */}
      <div data-vjs-player>
        <video
          ref={videoRef}
          className="video-js vjs-default-skin w-full h-full"
          playsInline
          data-setup="{}"
        />
      </div>

      {/* 錯誤提示 */}
      {playerState.error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80">
          <div className="text-center text-white">
            <div className="text-lg mb-4">{playerState.error}</div>
            <Button onClick={reload} variant="outline">
              <RotateCcw className="h-4 w-4 mr-2" />
              重新加載
            </Button>
          </div>
        </div>
      )}

      {/* 加載指示器 */}
      {playerState.isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}

      {/* 自定義控制欄 (可選) */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 hover:opacity-100 transition-opacity">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-2">
            <Button
              size="icon"
              variant="ghost"
              onClick={togglePlay}
              className="text-white hover:bg-white/20"
            >
              {playerState.isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>

            <Button
              size="icon"
              variant="ghost"
              onClick={toggleMute}
              className="text-white hover:bg-white/20"
            >
              {playerState.volume === 0 ? (
                <VolumeX className="h-5 w-5" />
              ) : (
                <Volume2 className="h-5 w-5" />
              )}
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              size="icon"
              variant="ghost"
              onClick={reload}
              className="text-white hover:bg-white/20"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>

            <Button
              size="icon"
              variant="ghost"
              onClick={toggleFullscreen}
              className="text-white hover:bg-white/20"
            >
              {playerState.isFullscreen ? (
                <Minimize className="h-5 w-5" />
              ) : (
                <Maximize className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
