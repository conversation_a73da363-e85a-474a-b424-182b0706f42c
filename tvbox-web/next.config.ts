import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'epg.iill.top',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'raw.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'github.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'epg.iill.top',
        port: '',
        pathname: '/**',
      },
      // 允許常見的圖片域名
      {
        protocol: 'https',
        hostname: '*.iill.top',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
      // 允許任何 HTTPS 圖片（開發環境）
      {
        protocol: 'https',
        hostname: '**',
        port: '',
        pathname: '/**',
      },
      // 允許任何 HTTP 圖片（開發環境）
      {
        protocol: 'http',
        hostname: '**',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
