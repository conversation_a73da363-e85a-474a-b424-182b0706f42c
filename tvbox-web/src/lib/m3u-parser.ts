import type { LiveChannelGroup, LiveChannelItem } from '@/types';

export interface M3UChannel {
  name: string;
  url: string;
  logo?: string;
  group?: string;
  tvgId?: string;
  tvgName?: string;
  catchup?: string;
  catchupSource?: string;
}

export interface M3UPlaylist {
  channels: M3UChannel[];
  groups: string[];
  epgUrl?: string;
  catchup?: string;
  catchupSource?: string;
}

/**
 * 解析 M3U 播放列表
 */
export class M3UParser {
  /**
   * 解析 M3U 文本內容
   */
  static parse(content: string): M3UPlaylist {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const channels: M3UChannel[] = [];
    const groups = new Set<string>();
    let epgUrl: string | undefined;
    let globalCatchup: string | undefined;
    let globalCatchupSource: string | undefined;

    let currentChannel: Partial<M3UChannel> = {};

    console.log(`開始解析 M3U 文件，共 ${lines.length} 行`);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 解析 M3U 頭部信息
      if (line.startsWith('#EXTM3U')) {
        const headerInfo = this.parseM3UHeader(line);
        epgUrl = headerInfo.epgUrl;
        globalCatchup = headerInfo.catchup;
        globalCatchupSource = headerInfo.catchupSource;
        console.log('解析到 M3U 頭部信息:', headerInfo);
        continue;
      }

      // 解析頻道信息
      if (line.startsWith('#EXTINF:')) {
        currentChannel = this.parseExtInf(line);
        continue;
      }

      // 解析 URL - 更寬鬆的 URL 檢查
      if (line && !line.startsWith('#')) {
        // 如果沒有頻道名稱，嘗試從 URL 推斷
        if (!currentChannel.name) {
          // 可能是沒有 #EXTINF 的簡單格式，使用 URL 作為名稱
          const urlParts = line.split('/');
          const fileName = urlParts[urlParts.length - 1];
          currentChannel.name = fileName || `頻道 ${channels.length + 1}`;
          console.warn(`頻道缺少名稱，使用推斷名稱: ${currentChannel.name}`);
        }

        // 驗證 URL 格式
        if (line.startsWith('http') || line.startsWith('rtmp') || line.startsWith('rtsp') || line.includes('://')) {
          const channel: M3UChannel = {
            name: currentChannel.name || `頻道 ${channels.length + 1}`,
            url: line,
            logo: currentChannel.logo,
            group: currentChannel.group || '未分類',
            tvgId: currentChannel.tvgId,
            tvgName: currentChannel.tvgName,
            catchup: currentChannel.catchup || globalCatchup,
            catchupSource: currentChannel.catchupSource || globalCatchupSource,
          };

          channels.push(channel);

          if (channel.group) {
            groups.add(channel.group);
          }

          console.log(`解析到頻道: ${channel.name} (${channel.group})`);
        } else {
          console.warn(`跳過無效 URL: ${line}`);
        }

        currentChannel = {};
      }
    }

    console.log(`M3U 解析完成，共解析到 ${channels.length} 個頻道，${groups.size} 個分組`);

    return {
      channels,
      groups: Array.from(groups).sort(),
      epgUrl,
      catchup: globalCatchup,
      catchupSource: globalCatchupSource,
    };
  }

  /**
   * 解析 M3U 頭部信息
   */
  private static parseM3UHeader(line: string): {
    epgUrl?: string;
    catchup?: string;
    catchupSource?: string;
  } {
    const result: any = {};

    // 解析 x-tvg-url
    const epgMatch = line.match(/x-tvg-url="([^"]+)"/);
    if (epgMatch) {
      result.epgUrl = epgMatch[1];
    }

    // 解析 catchup
    const catchupMatch = line.match(/catchup="([^"]+)"/);
    if (catchupMatch) {
      result.catchup = catchupMatch[1];
    }

    // 解析 catchup-source
    const catchupSourceMatch = line.match(/catchup-source="([^"]+)"/);
    if (catchupSourceMatch) {
      result.catchupSource = catchupSourceMatch[1];
    }

    return result;
  }

  /**
   * 解析 EXTINF 行
   */
  private static parseExtInf(line: string): Partial<M3UChannel> {
    const channel: Partial<M3UChannel> = {};

    // 提取頻道名稱（最後的部分）
    const nameMatch = line.match(/,(.+)$/);
    if (nameMatch) {
      channel.name = nameMatch[1].trim();
    }

    // 解析各種屬性
    const attributes = [
      { key: 'tvgId', pattern: /tvg-id="([^"]+)"/ },
      { key: 'tvgName', pattern: /tvg-name="([^"]+)"/ },
      { key: 'logo', pattern: /tvg-logo="([^"]+)"/ },
      { key: 'group', pattern: /group-title="([^"]+)"/ },
      { key: 'catchup', pattern: /catchup="([^"]+)"/ },
      { key: 'catchupSource', pattern: /catchup-source="([^"]+)"/ },
    ];

    attributes.forEach(({ key, pattern }) => {
      const match = line.match(pattern);
      if (match) {
        (channel as any)[key] = match[1];
      }
    });

    return channel;
  }

  /**
   * 將 M3U 播放列表轉換為 TVBox 格式的直播頻道組
   */
  static toTVBoxFormat(playlist: M3UPlaylist): LiveChannelGroup[] {
    const groupMap = new Map<string, LiveChannelItem[]>();

    // 按組分類頻道
    playlist.channels.forEach((channel, index) => {
      const groupName = channel.group || '未分類';

      if (!groupMap.has(groupName)) {
        groupMap.set(groupName, []);
      }

      const liveChannel: LiveChannelItem = {
        channelName: channel.name,
        channelUrls: [channel.url],
        channelIndex: index,
        channelLogo: channel.logo,
        channelEpgUrl: playlist.epgUrl,
      };

      groupMap.get(groupName)!.push(liveChannel);
    });

    // 轉換為 LiveChannelGroup 數組
    const groups: LiveChannelGroup[] = [];
    let groupIndex = 0;

    groupMap.forEach((channels, groupName) => {
      groups.push({
        groupName,
        groupIndex: groupIndex++,
        liveChannels: channels.map((channel, channelIndex) => ({
          ...channel,
          channelIndex,
          groupIndex: groupIndex - 1,
        })),
      });
    });

    return groups;
  }

  /**
   * 轉換 GitHub URL 為 raw 格式
   */
  static convertGitHubUrl(url: string): string {
    // 如果是 GitHub blob URL，轉換為 raw URL
    if (url.includes('github.com') && url.includes('/blob/')) {
      return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/');
    }
    return url;
  }

  /**
   * 從 URL 加載並解析 M3U 播放列表
   */
  static async loadFromUrl(url: string): Promise<M3UPlaylist> {
    try {
      // 自動轉換 GitHub URL
      const processedUrl = this.convertGitHubUrl(url);

      console.log('正在加載 M3U 播放列表:', processedUrl);

      // 使用代理來避免 CORS 問題
      const proxyUrl = `/api/proxy?url=${encodeURIComponent(processedUrl)}`;
      const response = await fetch(proxyUrl, {
        headers: {
          'Accept': 'text/plain, application/x-mpegurl, */*',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();

      // 驗證內容是否為有效的 M3U 格式
      const validationResult = this.validate(content);
      if (!validationResult.isValid) {
        console.error('M3U 驗證失敗:', validationResult.error);
        console.log('文件內容 (前 500 字符):', content.substring(0, 500));
        throw new Error(`文件格式不正確: ${validationResult.error}`);
      }

      const playlist = this.parse(content);
      console.log('M3U 播放列表解析成功:', {
        頻道數量: playlist.channels.length,
        分組數量: playlist.groups.length,
        EPG地址: playlist.epgUrl
      });

      return playlist;
    } catch (error) {
      console.error('加載 M3U 播放列表失敗:', error);
      throw new Error(`加載 M3U 播放列表失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  }

  /**
   * 驗證 M3U 內容格式
   */
  static validate(content: string): { isValid: boolean; error?: string } {
    if (!content || content.trim().length === 0) {
      return { isValid: false, error: '文件內容為空' };
    }

    const lines = content.split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length === 0) {
      return { isValid: false, error: '文件沒有有效內容' };
    }

    // 檢查是否以 #EXTM3U 開頭（更寬鬆的檢查）
    const firstLine = lines[0];
    if (!firstLine.startsWith('#EXTM3U')) {
      // 如果第一行不是 #EXTM3U，檢查是否包含 #EXTINF
      const hasExtInf = lines.some(line => line.startsWith('#EXTINF:'));
      if (!hasExtInf) {
        return {
          isValid: false,
          error: `文件不是有效的 M3U 格式。第一行應該是 #EXTM3U，實際是: "${firstLine}"`
        };
      }
      // 如果有 #EXTINF 但沒有 #EXTM3U 頭，我們仍然嘗試解析
      console.warn('M3U 文件缺少 #EXTM3U 頭，但包含頻道信息，嘗試解析...');
    }

    // 檢查是否包含有效的頻道信息
    let hasValidChannel = false;
    let channelCount = 0;

    for (let i = 0; i < lines.length - 1; i++) {
      if (lines[i].startsWith('#EXTINF:')) {
        channelCount++;
        // 檢查下一行是否是 URL
        const nextLine = lines[i + 1];
        if (nextLine && !nextLine.startsWith('#') && (nextLine.startsWith('http') || nextLine.startsWith('rtmp') || nextLine.startsWith('rtsp'))) {
          hasValidChannel = true;
        }
      }
    }

    if (channelCount === 0) {
      return { isValid: false, error: '文件中沒有找到任何頻道信息 (#EXTINF)' };
    }

    if (!hasValidChannel) {
      return { isValid: false, error: `找到 ${channelCount} 個頻道定義，但沒有有效的播放 URL` };
    }

    return { isValid: true };
  }

  /**
   * 過濾頻道
   */
  static filterChannels(
    playlist: M3UPlaylist,
    filters: {
      group?: string;
      keyword?: string;
      excludeGroups?: string[];
    }
  ): M3UPlaylist {
    let filteredChannels = playlist.channels;

    // 按組過濾
    if (filters.group) {
      filteredChannels = filteredChannels.filter(channel => channel.group === filters.group);
    }

    // 排除特定組
    if (filters.excludeGroups && filters.excludeGroups.length > 0) {
      filteredChannels = filteredChannels.filter(
        channel => !filters.excludeGroups!.includes(channel.group || '')
      );
    }

    // 按關鍵詞過濾
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      filteredChannels = filteredChannels.filter(channel =>
        channel.name.toLowerCase().includes(keyword)
      );
    }

    // 重新計算組列表
    const groups = new Set<string>();
    filteredChannels.forEach(channel => {
      if (channel.group) {
        groups.add(channel.group);
      }
    });

    return {
      ...playlist,
      channels: filteredChannels,
      groups: Array.from(groups).sort(),
    };
  }
}
