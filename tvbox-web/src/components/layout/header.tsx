'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Search, Settings, Home, Tv, History, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useConfigStore } from '@/stores/config';
import { cn } from '@/lib/utils';

const Header: React.FC = () => {
  const pathname = usePathname();
  const { homeSource } = useConfigStore();

  const navItems = [
    { href: '/', label: '首頁', icon: Home },
    { href: '/search', label: '搜索', icon: Search },
    { href: '/live', label: '直播', icon: Tv },
    { href: '/history', label: '歷史', icon: History },
    { href: '/favorites', label: '收藏', icon: Heart },
    { href: '/settings', label: '設置', icon: Settings },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">TV</span>
            </div>
            <span className="font-bold text-xl">TVBox</span>
          </Link>
          
          {homeSource && (
            <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
              <span>當前源:</span>
              <span className="font-medium">{homeSource.name}</span>
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-1">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            
            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  className={cn(
                    "flex items-center space-x-2",
                    isActive && "bg-primary text-primary-foreground"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Button>
              </Link>
            );
          })}
        </nav>

        {/* Quick Search */}
        <div className="flex items-center space-x-2">
          <div className="hidden lg:flex items-center space-x-2">
            <Input
              placeholder="快速搜索..."
              className="w-64"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const keyword = (e.target as HTMLInputElement).value.trim();
                  if (keyword) {
                    window.location.href = `/search?q=${encodeURIComponent(keyword)}`;
                  }
                }
              }}
            />
          </div>
          
          <Link href="/search" className="lg:hidden">
            <Button variant="ghost" size="icon">
              <Search className="h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden border-t">
        <div className="container">
          <nav className="flex items-center justify-around py-2">
            {navItems.slice(0, 5).map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              
              return (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "flex flex-col items-center space-y-1 h-auto py-2 px-3",
                      isActive && "text-primary"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="text-xs">{item.label}</span>
                  </Button>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
