import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get('url');

  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
  }

  try {
    // 驗證 URL 格式
    new URL(url);

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'TVBox/1.0.0 (Web)',
        'Accept': 'application/json, text/plain, */*',
      },
      // 設置超時
      signal: AbortSignal.timeout(30000),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`代理請求失敗 ${response.status}:`, errorText.substring(0, 200));

      return NextResponse.json(
        {
          error: `HTTP ${response.status}: ${response.statusText}`,
          details: errorText.substring(0, 500),
          url: url
        },
        { status: response.status }
      );
    }

    const contentType = response.headers.get('content-type') || '';
    const data = await response.text();

    // 檢查返回的內容類型
    if (data.trim().startsWith('<!DOCTYPE') || data.trim().startsWith('<html')) {
      console.warn('代理返回了 HTML 內容:', data.substring(0, 200));

      return NextResponse.json(
        {
          error: 'URL 返回了網頁而不是預期的數據格式',
          contentType: 'text/html',
          preview: data.substring(0, 200),
          url: url
        },
        { status: 400 }
      );
    }

    // 返回響應，設置適當的 CORS 頭
    return new NextResponse(data, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Proxy error:', error);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return NextResponse.json({ error: '請求超時' }, { status: 408 });
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ error: '未知錯誤' }, { status: 500 });
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
