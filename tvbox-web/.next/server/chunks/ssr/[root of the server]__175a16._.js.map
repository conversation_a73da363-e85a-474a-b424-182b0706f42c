{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/movie/movie-card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play, Star, Calendar, MapPin } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport type { Movie } from '@/types';\nimport { cn } from '@/lib/utils';\n\ninterface MovieCardProps {\n  movie: Movie;\n  sourceKey: string;\n  className?: string;\n  showDetails?: boolean;\n  onPlay?: (movie: Movie) => void;\n}\n\nconst MovieCard: React.FC<MovieCardProps> = ({\n  movie,\n  sourceKey,\n  className,\n  showDetails = true,\n  onPlay,\n}) => {\n  const handlePlay = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onPlay?.(movie);\n  };\n\n  return (\n    <Card className={cn(\"group overflow-hidden transition-all hover:shadow-lg\", className)}>\n      <Link href={`/detail/${sourceKey}/${movie.vodId}`}>\n        <div className=\"relative aspect-[3/4] overflow-hidden\">\n          {/* 海報圖片 */}\n          <Image\n            src={movie.vodPic || '/placeholder-movie.jpg'}\n            alt={movie.vodName}\n            fill\n            className=\"object-cover transition-transform group-hover:scale-105\"\n            sizes=\"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\"\n            onError={(e) => {\n              const target = e.target as HTMLImageElement;\n              target.src = '/placeholder-movie.jpg';\n            }}\n          />\n          \n          {/* 懸浮播放按鈕 */}\n          <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors flex items-center justify-center\">\n            <Button\n              size=\"icon\"\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity\"\n              onClick={handlePlay}\n            >\n              <Play className=\"h-5 w-5\" />\n            </Button>\n          </div>\n\n          {/* 評分標籤 */}\n          {movie.vodRemarks && (\n            <div className=\"absolute top-2 right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded\">\n              {movie.vodRemarks}\n            </div>\n          )}\n\n          {/* 年份標籤 */}\n          {movie.vodYear && (\n            <div className=\"absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded\">\n              {movie.vodYear}\n            </div>\n          )}\n        </div>\n\n        <CardContent className=\"p-3\">\n          {/* 標題 */}\n          <h3 className=\"font-medium text-sm line-clamp-2 mb-2 group-hover:text-primary transition-colors\">\n            {movie.vodName}\n          </h3>\n\n          {showDetails && (\n            <div className=\"space-y-1 text-xs text-muted-foreground\">\n              {/* 類型 */}\n              {movie.typeName && (\n                <div className=\"flex items-center space-x-1\">\n                  <Star className=\"h-3 w-3\" />\n                  <span className=\"line-clamp-1\">{movie.typeName}</span>\n                </div>\n              )}\n\n              {/* 地區 */}\n              {movie.vodArea && (\n                <div className=\"flex items-center space-x-1\">\n                  <MapPin className=\"h-3 w-3\" />\n                  <span className=\"line-clamp-1\">{movie.vodArea}</span>\n                </div>\n              )}\n\n              {/* 年份 */}\n              {movie.vodYear && (\n                <div className=\"flex items-center space-x-1\">\n                  <Calendar className=\"h-3 w-3\" />\n                  <span>{movie.vodYear}</span>\n                </div>\n              )}\n\n              {/* 演員 */}\n              {movie.vodActor && (\n                <div className=\"line-clamp-1\" title={movie.vodActor}>\n                  主演: {movie.vodActor}\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Link>\n    </Card>\n  );\n};\n\nexport default MovieCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AAEA;AAJA;AAAA;AAAA;AAAA;AALA;;;;;;;;AAmBA,MAAM,YAAsC,CAAC,EAC3C,KAAK,EACL,SAAS,EACT,SAAS,EACT,cAAc,IAAI,EAClB,MAAM,EACP;IACC,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,SAAS;IACX;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;kBAC1E,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,MAAM,KAAK,EAAE;;8BAC/C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,MAAM,MAAM,IAAI;4BACrB,KAAK,MAAM,OAAO;4BAClB,IAAI;4BACJ,WAAU;4BACV,OAAM;4BACN,SAAS,CAAC;gCACR,MAAM,SAAS,EAAE,MAAM;gCACvB,OAAO,GAAG,GAAG;4BACf;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;wBAKnB,MAAM,UAAU,kBACf,8OAAC;4BAAI,WAAU;sCACZ,MAAM,UAAU;;;;;;wBAKpB,MAAM,OAAO,kBACZ,8OAAC;4BAAI,WAAU;sCACZ,MAAM,OAAO;;;;;;;;;;;;8BAKpB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAG,WAAU;sCACX,MAAM,OAAO;;;;;;wBAGf,6BACC,8OAAC;4BAAI,WAAU;;gCAEZ,MAAM,QAAQ,kBACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAgB,MAAM,QAAQ;;;;;;;;;;;;gCAKjD,MAAM,OAAO,kBACZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAgB,MAAM,OAAO;;;;;;;;;;;;gCAKhD,MAAM,OAAO,kBACZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAM,MAAM,OAAO;;;;;;;;;;;;gCAKvB,MAAM,QAAQ,kBACb,8OAAC;oCAAI,WAAU;oCAAe,OAAO,MAAM,QAAQ;;wCAAE;wCAC9C,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;uCAEe"}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/movie/movie-grid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Loader2 } from 'lucide-react';\nimport MovieCard from './movie-card';\nimport type { Movie } from '@/types';\nimport { cn } from '@/lib/utils';\n\ninterface MovieGridProps {\n  movies: Movie[];\n  sourceKey: string;\n  loading?: boolean;\n  className?: string;\n  onMoviePlay?: (movie: Movie) => void;\n  onLoadMore?: () => void;\n  hasMore?: boolean;\n}\n\nconst MovieGrid: React.FC<MovieGridProps> = ({\n  movies,\n  sourceKey,\n  loading = false,\n  className,\n  onMoviePlay,\n  onLoadMore,\n  hasMore = false,\n}) => {\n  // 無限滾動檢測\n  React.useEffect(() => {\n    if (!hasMore || loading) return;\n\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n\n      if (scrollTop + clientHeight >= scrollHeight - 1000) {\n        onLoadMore?.();\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [hasMore, loading, onLoadMore]);\n\n  if (movies.length === 0 && !loading) {\n    return (\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\n        <div className=\"text-muted-foreground mb-2\">暫無內容</div>\n        <div className=\"text-sm text-muted-foreground\">\n          請嘗試切換其他分類或檢查網絡連接\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn(\"space-y-6\", className)}>\n      {/* 影片網格 */}\n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4\">\n        {movies.map((movie, index) => (\n          <MovieCard\n            key={`${movie.vodId}-${index}`}\n            movie={movie}\n            sourceKey={sourceKey}\n            onPlay={onMoviePlay}\n          />\n        ))}\n      </div>\n\n      {/* 加載狀態 */}\n      {loading && (\n        <div className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n          <span className=\"text-muted-foreground\">加載中...</span>\n        </div>\n      )}\n\n      {/* 加載更多按鈕 */}\n      {hasMore && !loading && (\n        <div className=\"flex justify-center py-4\">\n          <button\n            onClick={onLoadMore}\n            className=\"px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors\"\n          >\n            加載更多\n          </button>\n        </div>\n      )}\n\n      {/* 沒有更多內容 */}\n      {!hasMore && movies.length > 0 && (\n        <div className=\"text-center py-4 text-muted-foreground text-sm\">\n          已顯示全部內容\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default MovieGrid;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAHA;AAHA;;;;;;AAkBA,MAAM,YAAsC,CAAC,EAC3C,MAAM,EACN,SAAS,EACT,UAAU,KAAK,EACf,SAAS,EACT,WAAW,EACX,UAAU,EACV,UAAU,KAAK,EAChB;IACC,SAAS;IACT,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW,SAAS;QAEzB,MAAM,eAAe;YACnB,MAAM,YAAY,SAAS,eAAe,CAAC,SAAS;YACpD,MAAM,eAAe,SAAS,eAAe,CAAC,YAAY;YAC1D,MAAM,eAAe,SAAS,eAAe,CAAC,YAAY;YAE1D,IAAI,YAAY,gBAAgB,eAAe,MAAM;gBACnD;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAS;QAAS;KAAW;IAEjC,IAAI,OAAO,MAAM,KAAK,KAAK,CAAC,SAAS;QACnC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAA6B;;;;;;8BAC5C,8OAAC;oBAAI,WAAU;8BAAgC;;;;;;;;;;;;IAKrD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,4IAAA,CAAA,UAAS;wBAER,OAAO;wBACP,WAAW;wBACX,QAAQ;uBAHH,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;YASnC,yBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;YAK3C,WAAW,CAAC,yBACX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;YAOJ,CAAC,WAAW,OAAO,MAAM,GAAG,mBAC3B,8OAAC;gBAAI,WAAU;0BAAiD;;;;;;;;;;;;AAMxE;uCAEe"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';\nimport type {\n  SourceBean,\n  Movie,\n  VodInfo,\n  MovieSort,\n  SearchResult,\n  PlayInfo,\n  ParseBean,\n  RequestConfig,\n  ApiResponse\n} from '@/types';\n\nclass ApiService {\n  private axiosInstance: AxiosInstance;\n\n  constructor() {\n    this.axiosInstance = axios.create({\n      timeout: 30000,\n      headers: {\n        'User-Agent': 'TVBox/1.0.0 (Web)',\n        'Accept': 'application/json, text/plain, */*',\n      },\n    });\n\n    // 請求攔截器\n    this.axiosInstance.interceptors.request.use(\n      (config) => {\n        // 添加自定義 headers\n        if (config.userAgent) {\n          config.headers['User-Agent'] = config.userAgent;\n        }\n        if (config.referer) {\n          config.headers['Referer'] = config.referer;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // 響應攔截器\n    this.axiosInstance.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        console.error('API Request Error:', error);\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // 通用請求方法\n  private async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {\n    try {\n      // 使用代理來避免 CORS 問題\n      const proxyUrl = `/api/proxy?url=${encodeURIComponent(config.url)}`;\n\n      const response = await fetch(proxyUrl, {\n        method: config.method || 'GET',\n        headers: {\n          'Accept': 'application/json, text/plain, */*',\n          ...config.headers,\n        },\n        body: config.data ? JSON.stringify(config.data) : undefined,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.text();\n      let parsedData;\n\n      try {\n        parsedData = JSON.parse(data);\n      } catch {\n        // 如果不是 JSON，返回原始文本\n        parsedData = data;\n      }\n\n      return {\n        success: true,\n        data: parsedData,\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: error.message || '請求失敗',\n        message: error.response?.data?.message || error.message,\n      };\n    }\n  }\n\n  // 獲取分類列表\n  async getCategories(source: SourceBean): Promise<ApiResponse<MovieSort>> {\n    const url = this.buildApiUrl(source, 'categories');\n    return this.request<MovieSort>({\n      url,\n      headers: this.getSourceHeaders(source),\n    });\n  }\n\n  // 獲取影片列表\n  async getMovieList(\n    source: SourceBean,\n    categoryId: string,\n    page: number = 1,\n    filters?: Record<string, string>\n  ): Promise<ApiResponse<{ list: Movie[]; page: number; pagecount: number; total: number }>> {\n    const url = this.buildApiUrl(source, 'list', {\n      t: categoryId,\n      pg: page.toString(),\n      ...filters,\n    });\n\n    return this.request({\n      url,\n      headers: this.getSourceHeaders(source),\n    });\n  }\n\n  // 獲取影片詳情\n  async getMovieDetail(source: SourceBean, vodId: string): Promise<ApiResponse<VodInfo>> {\n    const url = this.buildApiUrl(source, 'detail', { ids: vodId });\n\n    const response = await this.request<{ list: VodInfo[] }>({\n      url,\n      headers: this.getSourceHeaders(source),\n    });\n\n    if (response.success && response.data?.list?.[0]) {\n      return {\n        success: true,\n        data: {\n          ...response.data.list[0],\n          sourceKey: source.key,\n        },\n      };\n    }\n\n    return {\n      success: false,\n      error: '獲取詳情失敗',\n    };\n  }\n\n  // 搜索影片\n  async searchMovies(source: SourceBean, keyword: string, page: number = 1): Promise<ApiResponse<SearchResult>> {\n    if (!source.searchable) {\n      return {\n        success: false,\n        error: '該源不支持搜索',\n      };\n    }\n\n    const url = this.buildApiUrl(source, 'search', {\n      wd: keyword,\n      pg: page.toString(),\n    });\n\n    const response = await this.request<{ list: Movie[] }>({\n      url,\n      headers: this.getSourceHeaders(source),\n    });\n\n    if (response.success) {\n      return {\n        success: true,\n        data: {\n          list: response.data?.list || [],\n          sourceKey: source.key,\n          sourceName: source.name,\n        },\n      };\n    }\n\n    return response;\n  }\n\n  // 獲取播放地址\n  async getPlayUrl(\n    source: SourceBean,\n    playFlag: string,\n    playUrl: string,\n    vipFlags?: string[]\n  ): Promise<ApiResponse<PlayInfo>> {\n    const url = this.buildApiUrl(source, 'play', {\n      flag: playFlag,\n      play: playUrl,\n    });\n\n    const response = await this.request<{ url: string; parse?: number; header?: Record<string, string> }>({\n      url,\n      headers: this.getSourceHeaders(source),\n    });\n\n    if (response.success && response.data?.url) {\n      return {\n        success: true,\n        data: {\n          url: response.data.url,\n          headers: response.data.header,\n          parse: response.data.parse === 1,\n        },\n      };\n    }\n\n    return {\n      success: false,\n      error: '獲取播放地址失敗',\n    };\n  }\n\n  // 解析播放地址\n  async parsePlayUrl(parse: ParseBean, url: string): Promise<ApiResponse<PlayInfo>> {\n    try {\n      let parseUrl = parse.url;\n\n      if (parse.type === 0) {\n        // 嗅探類型，直接返回原地址\n        return {\n          success: true,\n          data: {\n            url,\n            parse: false,\n          },\n        };\n      } else {\n        // 解析類型\n        if (parseUrl.includes('?')) {\n          parseUrl += `&url=${encodeURIComponent(url)}`;\n        } else {\n          parseUrl += `?url=${encodeURIComponent(url)}`;\n        }\n\n        const response = await this.request<{ url: string; header?: Record<string, string> }>({\n          url: parseUrl,\n        });\n\n        if (response.success && response.data?.url) {\n          return {\n            success: true,\n            data: {\n              url: response.data.url,\n              headers: response.data.header,\n              parse: true,\n            },\n          };\n        }\n      }\n\n      return {\n        success: false,\n        error: '解析失敗',\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: error.message || '解析失敗',\n      };\n    }\n  }\n\n  // 構建 API URL\n  private buildApiUrl(source: SourceBean, action: string, params?: Record<string, string>): string {\n    let url = source.api;\n\n    // 根據源類型構建不同的 URL\n    if (source.type === 0) {\n      // XML 類型\n      url += `?ac=${action}`;\n    } else if (source.type === 1) {\n      // JSON 類型\n      url += `?ac=${action}`;\n    } else if (source.type === 3) {\n      // Spider 類型，需要特殊處理\n      url += `?ac=${action}`;\n    }\n\n    // 添加參數\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url += `&${key}=${encodeURIComponent(value)}`;\n      });\n    }\n\n    return url;\n  }\n\n  // 獲取源的請求頭\n  private getSourceHeaders(source: SourceBean): Record<string, string> {\n    const headers: Record<string, string> = {};\n\n    if (source.ext) {\n      try {\n        const ext = JSON.parse(source.ext);\n        if (ext.header) {\n          Object.assign(headers, ext.header);\n        }\n      } catch (error) {\n        console.warn('解析源擴展配置失敗:', error);\n      }\n    }\n\n    return headers;\n  }\n}\n\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;AAAA;;AAaA,MAAM;IACI,cAA6B;IAErC,aAAc;QACZ,IAAI,CAAC,aAAa,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAChC,SAAS;YACT,SAAS;gBACP,cAAc;gBACd,UAAU;YACZ;QACF;QAEA,QAAQ;QACR,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACzC,CAAC;YACC,gBAAgB;YAChB,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,OAAO,CAAC,aAAa,GAAG,OAAO,SAAS;YACjD;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO;YAC5C;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,QAAQ;QACR,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC1C,CAAC,WAAa,UACd,CAAC;YACC,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,SAAS;IACT,MAAc,QAAW,MAAqB,EAA2B;QACvE,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,CAAC,eAAe,EAAE,mBAAmB,OAAO,GAAG,GAAG;YAEnE,MAAM,WAAW,MAAM,MAAM,UAAU;gBACrC,QAAQ,OAAO,MAAM,IAAI;gBACzB,SAAS;oBACP,UAAU;oBACV,GAAG,OAAO,OAAO;gBACnB;gBACA,MAAM,OAAO,IAAI,GAAG,KAAK,SAAS,CAAC,OAAO,IAAI,IAAI;YACpD;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI;YAEJ,IAAI;gBACF,aAAa,KAAK,KAAK,CAAC;YAC1B,EAAE,OAAM;gBACN,mBAAmB;gBACnB,aAAa;YACf;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;gBACxB,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;YACzD;QACF;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAAkB,EAAmC;QACvE,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ;QACrC,OAAO,IAAI,CAAC,OAAO,CAAY;YAC7B;YACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACjC;IACF;IAEA,SAAS;IACT,MAAM,aACJ,MAAkB,EAClB,UAAkB,EAClB,OAAe,CAAC,EAChB,OAAgC,EACyD;QACzF,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;YAC3C,GAAG;YACH,IAAI,KAAK,QAAQ;YACjB,GAAG,OAAO;QACZ;QAEA,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB;YACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACjC;IACF;IAEA,SAAS;IACT,MAAM,eAAe,MAAkB,EAAE,KAAa,EAAiC;QACrF,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;YAAE,KAAK;QAAM;QAE5D,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAsB;YACvD;YACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACjC;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE;YAChD,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;oBACxB,WAAW,OAAO,GAAG;gBACvB;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,aAAa,MAAkB,EAAE,OAAe,EAAE,OAAe,CAAC,EAAsC;QAC5G,IAAI,CAAC,OAAO,UAAU,EAAE;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;YAC7C,IAAI;YACJ,IAAI,KAAK,QAAQ;QACnB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAoB;YACrD;YACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACjC;QAEA,IAAI,SAAS,OAAO,EAAE;YACpB,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,MAAM,SAAS,IAAI,EAAE,QAAQ,EAAE;oBAC/B,WAAW,OAAO,GAAG;oBACrB,YAAY,OAAO,IAAI;gBACzB;YACF;QACF;QAEA,OAAO;IACT;IAEA,SAAS;IACT,MAAM,WACJ,MAAkB,EAClB,QAAgB,EAChB,OAAe,EACf,QAAmB,EACa;QAChC,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;YAC3C,MAAM;YACN,MAAM;QACR;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAmE;YACpG;YACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACjC;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,KAAK;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,KAAK,SAAS,IAAI,CAAC,GAAG;oBACtB,SAAS,SAAS,IAAI,CAAC,MAAM;oBAC7B,OAAO,SAAS,IAAI,CAAC,KAAK,KAAK;gBACjC;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,SAAS;IACT,MAAM,aAAa,KAAgB,EAAE,GAAW,EAAkC;QAChF,IAAI;YACF,IAAI,WAAW,MAAM,GAAG;YAExB,IAAI,MAAM,IAAI,KAAK,GAAG;gBACpB,eAAe;gBACf,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ;wBACA,OAAO;oBACT;gBACF;YACF,OAAO;gBACL,OAAO;gBACP,IAAI,SAAS,QAAQ,CAAC,MAAM;oBAC1B,YAAY,CAAC,KAAK,EAAE,mBAAmB,MAAM;gBAC/C,OAAO;oBACL,YAAY,CAAC,KAAK,EAAE,mBAAmB,MAAM;gBAC/C;gBAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAmD;oBACpF,KAAK;gBACP;gBAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,KAAK;oBAC1C,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,KAAK,SAAS,IAAI,CAAC,GAAG;4BACtB,SAAS,SAAS,IAAI,CAAC,MAAM;4BAC7B,OAAO;wBACT;oBACF;gBACF;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF;IACF;IAEA,aAAa;IACL,YAAY,MAAkB,EAAE,MAAc,EAAE,MAA+B,EAAU;QAC/F,IAAI,MAAM,OAAO,GAAG;QAEpB,iBAAiB;QACjB,IAAI,OAAO,IAAI,KAAK,GAAG;YACrB,SAAS;YACT,OAAO,CAAC,IAAI,EAAE,QAAQ;QACxB,OAAO,IAAI,OAAO,IAAI,KAAK,GAAG;YAC5B,UAAU;YACV,OAAO,CAAC,IAAI,EAAE,QAAQ;QACxB,OAAO,IAAI,OAAO,IAAI,KAAK,GAAG;YAC5B,mBAAmB;YACnB,OAAO,CAAC,IAAI,EAAE,QAAQ;QACxB;QAEA,OAAO;QACP,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,mBAAmB,QAAQ;YAC/C;QACF;QAEA,OAAO;IACT;IAEA,UAAU;IACF,iBAAiB,MAAkB,EAA0B;QACnE,MAAM,UAAkC,CAAC;QAEzC,IAAI,OAAO,GAAG,EAAE;YACd,IAAI;gBACF,MAAM,MAAM,KAAK,KAAK,CAAC,OAAO,GAAG;gBACjC,IAAI,IAAI,MAAM,EAAE;oBACd,OAAO,MAAM,CAAC,SAAS,IAAI,MAAM;gBACnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,cAAc;YAC7B;QACF;QAEA,OAAO;IACT;AACF;AAEO,MAAM,aAAa,IAAI"}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Settings, RefreshCw, AlertCircle } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport MovieGrid from '@/components/movie/movie-grid';\nimport { useConfigStore } from '@/stores/config';\nimport { apiService } from '@/lib/api';\nimport type { Movie, MovieSort } from '@/types';\n\nexport default function Home() {\n  const router = useRouter();\n  const {\n    apiConfig,\n    homeSource,\n    settings,\n    isLoading: configLoading,\n    error: configError,\n    loadConfig\n  } = useConfigStore();\n\n  const [categories, setCategories] = useState<MovieSort['sortData']>([]);\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [movies, setMovies] = useState<Movie[]>([]);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [apiUrl, setApiUrl] = useState(settings.apiUrl || '');\n\n  // 初始化配置\n  useEffect(() => {\n    if (!apiConfig && settings.apiUrl) {\n      loadConfig(settings.apiUrl).catch(console.error);\n    }\n  }, [apiConfig, settings.apiUrl, loadConfig]);\n\n  // 加載分類\n  useEffect(() => {\n    if (!homeSource) return;\n\n    const loadCategories = async () => {\n      try {\n        const response = await apiService.getCategories(homeSource);\n        if (response.success && response.data?.sortData) {\n          setCategories(response.data.sortData);\n          // 選擇第一個分類\n          if (response.data.sortData.length > 0) {\n            setSelectedCategory(response.data.sortData[0].id);\n          }\n        }\n      } catch (error) {\n        console.error('加載分類失敗:', error);\n      }\n    };\n\n    loadCategories();\n  }, [homeSource]);\n\n  // 加載影片列表\n  useEffect(() => {\n    if (!homeSource || !selectedCategory) return;\n\n    const loadMovies = async (page: number = 1, append: boolean = false) => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const response = await apiService.getMovieList(homeSource, selectedCategory, page);\n\n        if (response.success && response.data) {\n          const newMovies = response.data.list || [];\n          setMovies(prev => append ? [...prev, ...newMovies] : newMovies);\n          setTotalPages(response.data.pagecount || 1);\n          setCurrentPage(page);\n        } else {\n          setError(response.error || '加載失敗');\n        }\n      } catch (error: any) {\n        setError(error.message || '加載失敗');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadMovies(1);\n  }, [homeSource, selectedCategory]);\n\n  // 加載更多\n  const handleLoadMore = () => {\n    if (currentPage < totalPages && !loading) {\n      const nextPage = currentPage + 1;\n      const loadMovies = async () => {\n        setLoading(true);\n        try {\n          const response = await apiService.getMovieList(homeSource!, selectedCategory, nextPage);\n          if (response.success && response.data) {\n            setMovies(prev => [...prev, ...(response.data!.list || [])]);\n            setCurrentPage(nextPage);\n          }\n        } catch (error) {\n          console.error('加載更多失敗:', error);\n        } finally {\n          setLoading(false);\n        }\n      };\n      loadMovies();\n    }\n  };\n\n  // 播放影片\n  const handleMoviePlay = (movie: Movie) => {\n    router.push(`/detail/${homeSource?.key}/${movie.vodId}?autoplay=true`);\n  };\n\n  // 加載配置\n  const handleLoadConfig = async () => {\n    if (!apiUrl.trim()) return;\n\n    try {\n      await loadConfig(apiUrl.trim());\n    } catch (error) {\n      console.error('加載配置失敗:', error);\n    }\n  };\n\n  // 如果沒有配置，顯示配置頁面\n  if (!apiConfig) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-2xl mx-auto\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Settings className=\"h-6 w-6\" />\n                <span>配置 TVBox</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">\n                  配置地址\n                </label>\n                <div className=\"flex space-x-2\">\n                  <Input\n                    placeholder=\"請輸入配置文件 URL...\"\n                    value={apiUrl}\n                    onChange={(e) => setApiUrl(e.target.value)}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Enter') {\n                        handleLoadConfig();\n                      }\n                    }}\n                  />\n                  <Button\n                    onClick={handleLoadConfig}\n                    disabled={configLoading || !apiUrl.trim()}\n                  >\n                    {configLoading ? (\n                      <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                    ) : (\n                      '加載'\n                    )}\n                  </Button>\n                </div>\n              </div>\n\n              {configError && (\n                <div className=\"flex items-center space-x-2 text-destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">{configError}</span>\n                </div>\n              )}\n\n              <div className=\"text-sm text-muted-foreground\">\n                <p>請輸入有效的 TVBox 配置文件地址，例如：</p>\n                <ul className=\"list-disc list-inside mt-2 space-y-1\">\n                  <li>https://example.com/config.json</li>\n                  <li>支持 JSON 格式的配置文件</li>\n                  <li>支持 GitHub 直鏈（自動轉換 blob 鏈接）</li>\n                </ul>\n\n                <div className=\"mt-4 p-3 bg-muted rounded-lg space-y-2\">\n                  <p className=\"font-medium mb-2\">快速開始：</p>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => {\n                      setApiUrl('/test-config.json');\n                    }}\n                    className=\"w-full\"\n                  >\n                    使用測試配置\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => router.push('/live')}\n                    className=\"w-full\"\n                  >\n                    前往直播頁面\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      {/* 分類選擇 */}\n      {categories.length > 0 && (\n        <div className=\"mb-6\">\n          <div className=\"flex flex-wrap gap-2\">\n            {categories.map((category) => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.name}\n              </Button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 錯誤提示 */}\n      {error && (\n        <div className=\"mb-6\">\n          <Card className=\"border-destructive\">\n            <CardContent className=\"pt-6\">\n              <div className=\"flex items-center space-x-2 text-destructive\">\n                <AlertCircle className=\"h-5 w-5\" />\n                <span>{error}</span>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* 影片網格 */}\n      <MovieGrid\n        movies={movies}\n        sourceKey={homeSource?.key || ''}\n        loading={loading}\n        onMoviePlay={handleMoviePlay}\n        onLoadMore={handleLoadMore}\n        hasMore={currentPage < totalPages}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AANA;AAAA;AAAA;AAJA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,SAAS,EACT,UAAU,EACV,QAAQ,EACR,WAAW,aAAa,EACxB,OAAO,WAAW,EAClB,UAAU,EACX,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,MAAM,IAAI;IAExD,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,SAAS,MAAM,EAAE;YACjC,WAAW,SAAS,MAAM,EAAE,KAAK,CAAC,QAAQ,KAAK;QACjD;IACF,GAAG;QAAC;QAAW,SAAS,MAAM;QAAE;KAAW;IAE3C,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY;QAEjB,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;gBAChD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,UAAU;oBAC/C,cAAc,SAAS,IAAI,CAAC,QAAQ;oBACpC,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;wBACrC,oBAAoB,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBAClD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B;QACF;QAEA;IACF,GAAG;QAAC;KAAW;IAEf,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc,CAAC,kBAAkB;QAEtC,MAAM,aAAa,OAAO,OAAe,CAAC,EAAE,SAAkB,KAAK;YACjE,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,YAAY,CAAC,YAAY,kBAAkB;gBAE7E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,YAAY,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;oBAC1C,UAAU,CAAA,OAAQ,SAAS;+BAAI;+BAAS;yBAAU,GAAG;oBACrD,cAAc,SAAS,IAAI,CAAC,SAAS,IAAI;oBACzC,eAAe;gBACjB,OAAO;oBACL,SAAS,SAAS,KAAK,IAAI;gBAC7B;YACF,EAAE,OAAO,OAAY;gBACnB,SAAS,MAAM,OAAO,IAAI;YAC5B,SAAU;gBACR,WAAW;YACb;QACF;QAEA,WAAW;IACb,GAAG;QAAC;QAAY;KAAiB;IAEjC,OAAO;IACP,MAAM,iBAAiB;QACrB,IAAI,cAAc,cAAc,CAAC,SAAS;YACxC,MAAM,WAAW,cAAc;YAC/B,MAAM,aAAa;gBACjB,WAAW;gBACX,IAAI;oBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,YAAY,CAAC,YAAa,kBAAkB;oBAC9E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,UAAU,CAAA,OAAQ;mCAAI;mCAAU,SAAS,IAAI,CAAE,IAAI,IAAI,EAAE;6BAAE;wBAC3D,eAAe;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;gBAC3B,SAAU;oBACR,WAAW;gBACb;YACF;YACA;QACF;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,IAAI,CAAC,EAAE,MAAM,KAAK,CAAC,cAAc,CAAC;IACvE;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,IAAI,IAAI;QAEpB,IAAI;YACF,MAAM,WAAW,OAAO,IAAI;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,gBAAgB;IAChB,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,WAAW,CAAC;wDACV,IAAI,EAAE,GAAG,KAAK,SAAS;4DACrB;wDACF;oDACF;;;;;;8DAEF,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,iBAAiB,CAAC,OAAO,IAAI;8DAEtC,8BACC,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;+DAErB;;;;;;;;;;;;;;;;;;gCAMP,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAGN,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAmB;;;;;;8DAChC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,UAAU;oDACZ;oDACA,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,WAAW,MAAM,GAAG,mBACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;4BAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;4BACxD,MAAK;4BACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;sCAE7C,SAAS,IAAI;2BALT,SAAS,EAAE;;;;;;;;;;;;;;;YAazB,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;8CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjB,8OAAC,4IAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,WAAW,YAAY,OAAO;gBAC9B,SAAS;gBACT,aAAa;gBACb,YAAY;gBACZ,SAAS,cAAc;;;;;;;;;;;;AAI/B"}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}