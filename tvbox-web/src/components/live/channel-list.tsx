'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Play, Search, Filter, Tv, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLiveStore } from '@/stores/live';
import type { LiveChannelItem, LiveChannelGroup } from '@/types';
import { cn } from '@/lib/utils';

interface ChannelListProps {
  onChannelSelect?: (groupIndex: number, channelIndex: number) => void;
  className?: string;
}

const ChannelList: React.FC<ChannelListProps> = ({
  onChannelSelect,
  className,
}) => {
  const {
    currentChannel,
    currentGroupIndex,
    currentChannelIndex,
    selectedGroupName,
    searchKeyword,
    setSelectedGroup,
    setSearchKeyword,
    setCurrentChannel,
    getFilteredGroups,
  } = useLiveStore();

  const [showGroupFilter, setShowGroupFilter] = useState(false);
  const filteredGroups = getFilteredGroups();
  
  // 獲取所有組名
  const allGroups = useLiveStore(state => state.liveGroups.map(g => g.groupName));

  const handleChannelClick = (groupIndex: number, channelIndex: number) => {
    setCurrentChannel(groupIndex, channelIndex);
    onChannelSelect?.(groupIndex, channelIndex);
  };

  const handleGroupFilter = (groupName: string | null) => {
    setSelectedGroup(groupName);
    setShowGroupFilter(false);
  };

  if (filteredGroups.length === 0) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-12 text-center", className)}>
        <Tv className="h-12 w-12 text-muted-foreground mb-4" />
        <div className="text-lg font-medium mb-2">暫無直播頻道</div>
        <div className="text-sm text-muted-foreground">
          請先加載直播源或檢查網絡連接
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索和過濾 */}
      <div className="space-y-2">
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索頻道..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowGroupFilter(!showGroupFilter)}
          >
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        {/* 組過濾器 */}
        {showGroupFilter && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center">
                <Users className="h-4 w-4 mr-2" />
                選擇分組
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant={selectedGroupName === null ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleGroupFilter(null)}
                  className="justify-start"
                >
                  全部分組
                </Button>
                {allGroups.map((groupName) => (
                  <Button
                    key={groupName}
                    variant={selectedGroupName === groupName ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleGroupFilter(groupName)}
                    className="justify-start"
                  >
                    {groupName}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 頻道列表 */}
      <div className="space-y-4">
        {filteredGroups.map((group, groupIdx) => (
          <Card key={group.groupName}>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center justify-between">
                <span className="flex items-center">
                  <Tv className="h-4 w-4 mr-2" />
                  {group.groupName}
                </span>
                <span className="text-xs text-muted-foreground">
                  {group.liveChannels.length} 個頻道
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 gap-2">
                {group.liveChannels.map((channel, channelIdx) => {
                  const isActive = 
                    currentChannel?.channelName === channel.channelName &&
                    currentGroupIndex === group.groupIndex &&
                    currentChannelIndex === channelIdx;

                  return (
                    <div
                      key={`${channel.channelName}-${channelIdx}`}
                      className={cn(
                        "flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors",
                        isActive 
                          ? "bg-primary text-primary-foreground border-primary" 
                          : "hover:bg-accent hover:text-accent-foreground"
                      )}
                      onClick={() => handleChannelClick(group.groupIndex, channelIdx)}
                    >
                      {/* 頻道 Logo */}
                      <div className="flex-shrink-0">
                        {channel.channelLogo ? (
                          <Image
                            src={channel.channelLogo}
                            alt={channel.channelName}
                            width={40}
                            height={40}
                            className="rounded object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                            }}
                          />
                        ) : (
                          <div className="w-10 h-10 bg-muted rounded flex items-center justify-center">
                            <Tv className="h-5 w-5 text-muted-foreground" />
                          </div>
                        )}
                      </div>

                      {/* 頻道信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {channel.channelName}
                        </div>
                        {channel.channelUrls && channel.channelUrls.length > 1 && (
                          <div className="text-xs text-muted-foreground">
                            {channel.channelUrls.length} 個源
                          </div>
                        )}
                      </div>

                      {/* 播放按鈕 */}
                      <div className="flex-shrink-0">
                        {isActive ? (
                          <div className="w-8 h-8 bg-primary-foreground text-primary rounded-full flex items-center justify-center">
                            <Play className="h-4 w-4 fill-current" />
                          </div>
                        ) : (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="w-8 h-8"
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 統計信息 */}
      <div className="text-center text-sm text-muted-foreground">
        共 {filteredGroups.length} 個分組，
        {filteredGroups.reduce((total, group) => total + group.liveChannels.length, 0)} 個頻道
      </div>
    </div>
  );
};

export default ChannelList;
