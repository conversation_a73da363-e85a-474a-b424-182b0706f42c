{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/stores/player.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport type { VodInfo, PlayInfo, PlayerState, ParseBean } from '@/types';\n\ninterface PlayerStore {\n  // 當前播放的內容\n  currentVod: VodInfo | null;\n  currentPlayInfo: PlayInfo | null;\n  currentEpisodeIndex: number;\n  currentSourceIndex: number;\n  \n  // 播放器狀態\n  playerState: PlayerState;\n  \n  // 解析相關\n  currentParse: ParseBean | null;\n  parseHistory: string[]; // 解析歷史記錄\n  \n  // 播放歷史\n  playHistory: Array<{\n    vodId: string;\n    sourceKey: string;\n    playFlag: string;\n    episodeIndex: number;\n    position: number;\n    timestamp: number;\n  }>;\n  \n  // Actions\n  setCurrentVod: (vod: VodInfo) => void;\n  setCurrentPlayInfo: (playInfo: PlayInfo) => void;\n  setCurrentEpisode: (index: number) => void;\n  setCurrentSource: (index: number) => void;\n  updatePlayerState: (state: Partial<PlayerState>) => void;\n  setCurrentParse: (parse: ParseBean | null) => void;\n  addToParseHistory: (url: string) => void;\n  savePlayProgress: (position: number, duration: number) => void;\n  getPlayProgress: (vodId: string, sourceKey: string, playFlag: string, episodeIndex: number) => number;\n  clearPlayer: () => void;\n}\n\nconst initialPlayerState: PlayerState = {\n  isPlaying: false,\n  currentTime: 0,\n  duration: 0,\n  volume: 1,\n  playbackRate: 1,\n  isFullscreen: false,\n  isLoading: false,\n  error: undefined,\n};\n\nexport const usePlayerStore = create<PlayerStore>((set, get) => ({\n  // 初始狀態\n  currentVod: null,\n  currentPlayInfo: null,\n  currentEpisodeIndex: 0,\n  currentSourceIndex: 0,\n  playerState: initialPlayerState,\n  currentParse: null,\n  parseHistory: [],\n  playHistory: [],\n\n  // Actions\n  setCurrentVod: (vod: VodInfo) => {\n    set({ \n      currentVod: vod,\n      currentEpisodeIndex: vod.playIndex || 0,\n      currentSourceIndex: 0,\n    });\n  },\n\n  setCurrentPlayInfo: (playInfo: PlayInfo) => {\n    set({ currentPlayInfo: playInfo });\n  },\n\n  setCurrentEpisode: (index: number) => {\n    set({ currentEpisodeIndex: index });\n    \n    // 更新 VodInfo 中的播放索引\n    const { currentVod } = get();\n    if (currentVod) {\n      set({\n        currentVod: {\n          ...currentVod,\n          playIndex: index,\n        }\n      });\n    }\n  },\n\n  setCurrentSource: (index: number) => {\n    set({ currentSourceIndex: index });\n  },\n\n  updatePlayerState: (newState: Partial<PlayerState>) => {\n    set(state => ({\n      playerState: { ...state.playerState, ...newState }\n    }));\n  },\n\n  setCurrentParse: (parse: ParseBean | null) => {\n    set({ currentParse: parse });\n  },\n\n  addToParseHistory: (url: string) => {\n    set(state => ({\n      parseHistory: [url, ...state.parseHistory.filter(u => u !== url)].slice(0, 10)\n    }));\n  },\n\n  savePlayProgress: (position: number, duration: number) => {\n    const { currentVod, currentEpisodeIndex, currentSourceIndex } = get();\n    if (!currentVod || !currentVod.vodPlayFrom) return;\n\n    const playFlag = currentVod.vodPlayFrom[currentSourceIndex];\n    const historyItem = {\n      vodId: currentVod.id,\n      sourceKey: currentVod.sourceKey || '',\n      playFlag,\n      episodeIndex: currentEpisodeIndex,\n      position,\n      timestamp: Date.now(),\n    };\n\n    set(state => {\n      const existingIndex = state.playHistory.findIndex(\n        item => \n          item.vodId === historyItem.vodId &&\n          item.sourceKey === historyItem.sourceKey &&\n          item.playFlag === historyItem.playFlag &&\n          item.episodeIndex === historyItem.episodeIndex\n      );\n\n      let newHistory;\n      if (existingIndex >= 0) {\n        // 更新現有記錄\n        newHistory = [...state.playHistory];\n        newHistory[existingIndex] = historyItem;\n      } else {\n        // 添加新記錄\n        newHistory = [historyItem, ...state.playHistory].slice(0, 100); // 保留最近100條\n      }\n\n      return { playHistory: newHistory };\n    });\n  },\n\n  getPlayProgress: (vodId: string, sourceKey: string, playFlag: string, episodeIndex: number) => {\n    const { playHistory } = get();\n    const historyItem = playHistory.find(\n      item =>\n        item.vodId === vodId &&\n        item.sourceKey === sourceKey &&\n        item.playFlag === playFlag &&\n        item.episodeIndex === episodeIndex\n    );\n    return historyItem?.position || 0;\n  },\n\n  clearPlayer: () => {\n    set({\n      currentVod: null,\n      currentPlayInfo: null,\n      currentEpisodeIndex: 0,\n      currentSourceIndex: 0,\n      playerState: initialPlayerState,\n      currentParse: null,\n    });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;;AAwCA,MAAM,qBAAkC;IACtC,WAAW;IACX,aAAa;IACb,UAAU;IACV,QAAQ;IACR,cAAc;IACd,cAAc;IACd,WAAW;IACX,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAC,KAAK,MAAQ,CAAC;QAC/D,OAAO;QACP,YAAY;QACZ,iBAAiB;QACjB,qBAAqB;QACrB,oBAAoB;QACpB,aAAa;QACb,cAAc;QACd,cAAc,EAAE;QAChB,aAAa,EAAE;QAEf,UAAU;QACV,eAAe,CAAC;YACd,IAAI;gBACF,YAAY;gBACZ,qBAAqB,IAAI,SAAS,IAAI;gBACtC,oBAAoB;YACtB;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI;gBAAE,iBAAiB;YAAS;QAClC;QAEA,mBAAmB,CAAC;YAClB,IAAI;gBAAE,qBAAqB;YAAM;YAEjC,oBAAoB;YACpB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,YAAY;gBACd,IAAI;oBACF,YAAY;wBACV,GAAG,UAAU;wBACb,WAAW;oBACb;gBACF;YACF;QACF;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE,oBAAoB;YAAM;QAClC;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,QAAQ;oBAAC;gBACnD,CAAC;QACH;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE,cAAc;YAAM;QAC5B;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,cAAc;wBAAC;2BAAQ,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;qBAAK,CAAC,KAAK,CAAC,GAAG;gBAC7E,CAAC;QACH;QAEA,kBAAkB,CAAC,UAAkB;YACnC,MAAM,EAAE,UAAU,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,GAAG;YAChE,IAAI,CAAC,cAAc,CAAC,WAAW,WAAW,EAAE;YAE5C,MAAM,WAAW,WAAW,WAAW,CAAC,mBAAmB;YAC3D,MAAM,cAAc;gBAClB,OAAO,WAAW,EAAE;gBACpB,WAAW,WAAW,SAAS,IAAI;gBACnC;gBACA,cAAc;gBACd;gBACA,WAAW,KAAK,GAAG;YACrB;YAEA,IAAI,CAAA;gBACF,MAAM,gBAAgB,MAAM,WAAW,CAAC,SAAS,CAC/C,CAAA,OACE,KAAK,KAAK,KAAK,YAAY,KAAK,IAChC,KAAK,SAAS,KAAK,YAAY,SAAS,IACxC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,YAAY,KAAK,YAAY,YAAY;gBAGlD,IAAI;gBACJ,IAAI,iBAAiB,GAAG;oBACtB,SAAS;oBACT,aAAa;2BAAI,MAAM,WAAW;qBAAC;oBACnC,UAAU,CAAC,cAAc,GAAG;gBAC9B,OAAO;oBACL,QAAQ;oBACR,aAAa;wBAAC;2BAAgB,MAAM,WAAW;qBAAC,CAAC,KAAK,CAAC,GAAG,MAAM,WAAW;gBAC7E;gBAEA,OAAO;oBAAE,aAAa;gBAAW;YACnC;QACF;QAEA,iBAAiB,CAAC,OAAe,WAAmB,UAAkB;YACpE,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,MAAM,cAAc,YAAY,IAAI,CAClC,CAAA,OACE,KAAK,KAAK,KAAK,SACf,KAAK,SAAS,KAAK,aACnB,KAAK,QAAQ,KAAK,YAClB,KAAK,YAAY,KAAK;YAE1B,OAAO,aAAa,YAAY;QAClC;QAEA,aAAa;YACX,IAAI;gBACF,YAAY;gBACZ,iBAAiB;gBACjB,qBAAqB;gBACrB,oBAAoB;gBACpB,aAAa;gBACb,cAAc;YAChB;QACF;IACF,CAAC"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/player/video-player.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport videojs from 'video.js';\nimport 'video.js/dist/video-js.css';\nimport { Play, Pause, Volume2, VolumeX, Maximize, Minimize, RotateCcw } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { usePlayerStore } from '@/stores/player';\nimport type { PlayInfo } from '@/types';\nimport { cn } from '@/lib/utils';\n\ninterface VideoPlayerProps {\n  src?: string;\n  poster?: string;\n  className?: string;\n  autoplay?: boolean;\n  onTimeUpdate?: (currentTime: number, duration: number) => void;\n  onEnded?: () => void;\n  onError?: (error: string) => void;\n}\n\nconst VideoPlayer: React.FC<VideoPlayerProps> = ({\n  src,\n  poster,\n  className,\n  autoplay = false,\n  onTimeUpdate,\n  onEnded,\n  onError,\n}) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const playerRef = useRef<any>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const { playerState, updatePlayerState } = usePlayerStore();\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  // 初始化播放器\n  useEffect(() => {\n    if (!videoRef.current || isInitialized) return;\n\n    const player = videojs(videoRef.current, {\n      controls: true,\n      responsive: true,\n      fluid: true,\n      playbackRates: [0.5, 1, 1.25, 1.5, 2],\n      poster: poster,\n      preload: 'metadata',\n      html5: {\n        vhs: {\n          overrideNative: true,\n        },\n        nativeVideoTracks: false,\n        nativeAudioTracks: false,\n        nativeTextTracks: false,\n      },\n    });\n\n    playerRef.current = player;\n    setIsInitialized(true);\n\n    // 事件監聽\n    player.on('loadstart', () => {\n      updatePlayerState({ isLoading: true, error: undefined });\n    });\n\n    player.on('canplay', () => {\n      updatePlayerState({ isLoading: false });\n    });\n\n    player.on('play', () => {\n      updatePlayerState({ isPlaying: true });\n    });\n\n    player.on('pause', () => {\n      updatePlayerState({ isPlaying: false });\n    });\n\n    player.on('timeupdate', () => {\n      const currentTime = player.currentTime();\n      const duration = player.duration();\n      updatePlayerState({ currentTime, duration });\n      onTimeUpdate?.(currentTime, duration);\n    });\n\n    player.on('volumechange', () => {\n      updatePlayerState({ volume: player.volume() });\n    });\n\n    player.on('ratechange', () => {\n      updatePlayerState({ playbackRate: player.playbackRate() });\n    });\n\n    player.on('fullscreenchange', () => {\n      updatePlayerState({ isFullscreen: player.isFullscreen() });\n    });\n\n    player.on('ended', () => {\n      updatePlayerState({ isPlaying: false });\n      onEnded?.();\n    });\n\n    player.on('error', (e: any) => {\n      const error = player.error();\n      let errorMessage = '播放錯誤';\n\n      if (error) {\n        switch (error.code) {\n          case 1:\n            errorMessage = '播放被中止';\n            break;\n          case 2:\n            errorMessage = '網絡錯誤 - 無法加載視頻流';\n            break;\n          case 3:\n            errorMessage = '解碼錯誤 - 視頻格式不支持';\n            break;\n          case 4:\n            errorMessage = '視頻源不可用';\n            break;\n          default:\n            errorMessage = `播放錯誤 (代碼: ${error.code})`;\n        }\n\n        if (error.message) {\n          errorMessage += ` - ${error.message}`;\n        }\n      }\n\n      console.error('Video.js 播放錯誤:', error);\n      updatePlayerState({ error: errorMessage, isLoading: false });\n      onError?.(errorMessage);\n    });\n\n    return () => {\n      if (playerRef.current) {\n        playerRef.current.dispose();\n        playerRef.current = null;\n        setIsInitialized(false);\n      }\n    };\n  }, []);\n\n  // 更新播放源\n  useEffect(() => {\n    if (!playerRef.current || !src) return;\n\n    const player = playerRef.current;\n\n    try {\n      player.src({\n        src: src,\n        type: getVideoType(src),\n      });\n\n      if (autoplay) {\n        player.ready(() => {\n          player.play().catch((error: any) => {\n            console.warn('自動播放失敗:', error);\n          });\n        });\n      }\n    } catch (error) {\n      console.error('設置播放源失敗:', error);\n      onError?.('設置播放源失敗');\n    }\n  }, [src, autoplay]);\n\n  // 獲取視頻類型\n  const getVideoType = (url: string): string => {\n    if (url.includes('.m3u8')) return 'application/x-mpegURL';\n    if (url.includes('.mpd')) return 'application/dash+xml';\n    if (url.includes('.mp4')) return 'video/mp4';\n    if (url.includes('.webm')) return 'video/webm';\n    if (url.includes('.ogg')) return 'video/ogg';\n    return 'video/mp4'; // 默認\n  };\n\n  // 播放/暫停\n  const togglePlay = () => {\n    if (!playerRef.current) return;\n\n    if (playerState.isPlaying) {\n      playerRef.current.pause();\n    } else {\n      playerRef.current.play();\n    }\n  };\n\n  // 靜音/取消靜音\n  const toggleMute = () => {\n    if (!playerRef.current) return;\n    playerRef.current.muted(!playerRef.current.muted());\n  };\n\n  // 全屏/退出全屏\n  const toggleFullscreen = () => {\n    if (!playerRef.current) return;\n\n    if (playerState.isFullscreen) {\n      playerRef.current.exitFullscreen();\n    } else {\n      playerRef.current.requestFullscreen();\n    }\n  };\n\n  // 重新加載\n  const reload = () => {\n    if (!playerRef.current || !src) return;\n\n    const currentTime = playerRef.current.currentTime();\n    playerRef.current.load();\n    playerRef.current.ready(() => {\n      playerRef.current.currentTime(currentTime);\n    });\n  };\n\n  return (\n    <div\n      ref={containerRef}\n      className={cn(\"relative bg-black rounded-lg overflow-hidden\", className)}\n    >\n      {/* Video.js 播放器 */}\n      <div data-vjs-player>\n        <video\n          ref={videoRef}\n          className=\"video-js vjs-default-skin w-full h-full\"\n          playsInline\n          data-setup=\"{}\"\n        />\n      </div>\n\n      {/* 錯誤提示 */}\n      {playerState.error && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black/80\">\n          <div className=\"text-center text-white\">\n            <div className=\"text-lg mb-4\">{playerState.error}</div>\n            <Button onClick={reload} variant=\"outline\">\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              重新加載\n            </Button>\n          </div>\n        </div>\n      )}\n\n      {/* 加載指示器 */}\n      {playerState.isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black/50\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white\"></div>\n        </div>\n      )}\n\n      {/* 自定義控制欄 (可選) */}\n      <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 hover:opacity-100 transition-opacity\">\n        <div className=\"flex items-center justify-between text-white\">\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              size=\"icon\"\n              variant=\"ghost\"\n              onClick={togglePlay}\n              className=\"text-white hover:bg-white/20\"\n            >\n              {playerState.isPlaying ? (\n                <Pause className=\"h-5 w-5\" />\n              ) : (\n                <Play className=\"h-5 w-5\" />\n              )}\n            </Button>\n\n            <Button\n              size=\"icon\"\n              variant=\"ghost\"\n              onClick={toggleMute}\n              className=\"text-white hover:bg-white/20\"\n            >\n              {playerState.volume === 0 ? (\n                <VolumeX className=\"h-5 w-5\" />\n              ) : (\n                <Volume2 className=\"h-5 w-5\" />\n              )}\n            </Button>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              size=\"icon\"\n              variant=\"ghost\"\n              onClick={reload}\n              className=\"text-white hover:bg-white/20\"\n            >\n              <RotateCcw className=\"h-4 w-4\" />\n            </Button>\n\n            <Button\n              size=\"icon\"\n              variant=\"ghost\"\n              onClick={toggleFullscreen}\n              className=\"text-white hover:bg-white/20\"\n            >\n              {playerState.isFullscreen ? (\n                <Minimize className=\"h-5 w-5\" />\n              ) : (\n                <Maximize className=\"h-5 w-5\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoPlayer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AACA;AAEA;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;;;;AAqBA,MAAM,cAA0C,CAAC,EAC/C,GAAG,EACH,MAAM,EACN,SAAS,EACT,WAAW,KAAK,EAChB,YAAY,EACZ,OAAO,EACP,OAAO,EACR;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,eAAe;QAExC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,EAAE;YACvC,UAAU;YACV,YAAY;YACZ,OAAO;YACP,eAAe;gBAAC;gBAAK;gBAAG;gBAAM;gBAAK;aAAE;YACrC,QAAQ;YACR,SAAS;YACT,OAAO;gBACL,KAAK;oBACH,gBAAgB;gBAClB;gBACA,mBAAmB;gBACnB,mBAAmB;gBACnB,kBAAkB;YACpB;QACF;QAEA,UAAU,OAAO,GAAG;QACpB,iBAAiB;QAEjB,OAAO;QACP,OAAO,EAAE,CAAC,aAAa;YACrB,kBAAkB;gBAAE,WAAW;gBAAM,OAAO;YAAU;QACxD;QAEA,OAAO,EAAE,CAAC,WAAW;YACnB,kBAAkB;gBAAE,WAAW;YAAM;QACvC;QAEA,OAAO,EAAE,CAAC,QAAQ;YAChB,kBAAkB;gBAAE,WAAW;YAAK;QACtC;QAEA,OAAO,EAAE,CAAC,SAAS;YACjB,kBAAkB;gBAAE,WAAW;YAAM;QACvC;QAEA,OAAO,EAAE,CAAC,cAAc;YACtB,MAAM,cAAc,OAAO,WAAW;YACtC,MAAM,WAAW,OAAO,QAAQ;YAChC,kBAAkB;gBAAE;gBAAa;YAAS;YAC1C,eAAe,aAAa;QAC9B;QAEA,OAAO,EAAE,CAAC,gBAAgB;YACxB,kBAAkB;gBAAE,QAAQ,OAAO,MAAM;YAAG;QAC9C;QAEA,OAAO,EAAE,CAAC,cAAc;YACtB,kBAAkB;gBAAE,cAAc,OAAO,YAAY;YAAG;QAC1D;QAEA,OAAO,EAAE,CAAC,oBAAoB;YAC5B,kBAAkB;gBAAE,cAAc,OAAO,YAAY;YAAG;QAC1D;QAEA,OAAO,EAAE,CAAC,SAAS;YACjB,kBAAkB;gBAAE,WAAW;YAAM;YACrC;QACF;QAEA,OAAO,EAAE,CAAC,SAAS,CAAC;YAClB,MAAM,QAAQ,OAAO,KAAK;YAC1B,IAAI,eAAe;YAEnB,IAAI,OAAO;gBACT,OAAQ,MAAM,IAAI;oBAChB,KAAK;wBACH,eAAe;wBACf;oBACF,KAAK;wBACH,eAAe;wBACf;oBACF,KAAK;wBACH,eAAe;wBACf;oBACF,KAAK;wBACH,eAAe;wBACf;oBACF;wBACE,eAAe,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;gBAC7C;gBAEA,IAAI,MAAM,OAAO,EAAE;oBACjB,gBAAgB,CAAC,GAAG,EAAE,MAAM,OAAO,EAAE;gBACvC;YACF;YAEA,QAAQ,KAAK,CAAC,kBAAkB;YAChC,kBAAkB;gBAAE,OAAO;gBAAc,WAAW;YAAM;YAC1D,UAAU;QACZ;QAEA,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,OAAO;gBACzB,UAAU,OAAO,GAAG;gBACpB,iBAAiB;YACnB;QACF;IACF,GAAG,EAAE;IAEL,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,KAAK;QAEhC,MAAM,SAAS,UAAU,OAAO;QAEhC,IAAI;YACF,OAAO,GAAG,CAAC;gBACT,KAAK;gBACL,MAAM,aAAa;YACrB;YAEA,IAAI,UAAU;gBACZ,OAAO,KAAK,CAAC;oBACX,OAAO,IAAI,GAAG,KAAK,CAAC,CAAC;wBACnB,QAAQ,IAAI,CAAC,WAAW;oBAC1B;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,UAAU;QACZ;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,IAAI,IAAI,QAAQ,CAAC,UAAU,OAAO;QAClC,IAAI,IAAI,QAAQ,CAAC,SAAS,OAAO;QACjC,IAAI,IAAI,QAAQ,CAAC,SAAS,OAAO;QACjC,IAAI,IAAI,QAAQ,CAAC,UAAU,OAAO;QAClC,IAAI,IAAI,QAAQ,CAAC,SAAS,OAAO;QACjC,OAAO,aAAa,KAAK;IAC3B;IAEA,QAAQ;IACR,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,IAAI,YAAY,SAAS,EAAE;YACzB,UAAU,OAAO,CAAC,KAAK;QACzB,OAAO;YACL,UAAU,OAAO,CAAC,IAAI;QACxB;IACF;IAEA,UAAU;IACV,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,UAAU,OAAO,CAAC,KAAK,CAAC,CAAC,UAAU,OAAO,CAAC,KAAK;IAClD;IAEA,UAAU;IACV,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,IAAI,YAAY,YAAY,EAAE;YAC5B,UAAU,OAAO,CAAC,cAAc;QAClC,OAAO;YACL,UAAU,OAAO,CAAC,iBAAiB;QACrC;IACF;IAEA,OAAO;IACP,MAAM,SAAS;QACb,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,KAAK;QAEhC,MAAM,cAAc,UAAU,OAAO,CAAC,WAAW;QACjD,UAAU,OAAO,CAAC,IAAI;QACtB,UAAU,OAAO,CAAC,KAAK,CAAC;YACtB,UAAU,OAAO,CAAC,WAAW,CAAC;QAChC;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;;0BAG9D,8OAAC;gBAAI,iBAAe;0BAClB,cAAA,8OAAC;oBACC,KAAK;oBACL,WAAU;oBACV,WAAW;oBACX,cAAW;;;;;;;;;;;YAKd,YAAY,KAAK,kBAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB,YAAY,KAAK;;;;;;sCAChD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAQ,SAAQ;;8CAC/B,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAQ7C,YAAY,SAAS,kBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CAET,YAAY,SAAS,iBACpB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAEjB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAIpB,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CAET,YAAY,MAAM,KAAK,kBACtB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CAET,YAAY,YAAY,iBACvB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAEpB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC;uCAEe"}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/lib/m3u-parser.ts"], "sourcesContent": ["import type { LiveChannelGroup, LiveChannelItem } from '@/types';\n\nexport interface M3UChannel {\n  name: string;\n  url: string;\n  logo?: string;\n  group?: string;\n  tvgId?: string;\n  tvgName?: string;\n  catchup?: string;\n  catchupSource?: string;\n}\n\nexport interface M3UPlaylist {\n  channels: M3UChannel[];\n  groups: string[];\n  epgUrl?: string;\n  catchup?: string;\n  catchupSource?: string;\n}\n\n/**\n * 解析 M3U 播放列表\n */\nexport class M3UParser {\n  /**\n   * 解析 M3U 文本內容\n   */\n  static parse(content: string): M3UPlaylist {\n    const lines = content.split('\\n').map(line => line.trim()).filter(line => line);\n    const channels: M3UChannel[] = [];\n    const groups = new Set<string>();\n    let epgUrl: string | undefined;\n    let globalCatchup: string | undefined;\n    let globalCatchupSource: string | undefined;\n\n    let currentChannel: Partial<M3UChannel> = {};\n\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i];\n\n      // 解析 M3U 頭部信息\n      if (line.startsWith('#EXTM3U')) {\n        const headerInfo = this.parseM3UHeader(line);\n        epgUrl = headerInfo.epgUrl;\n        globalCatchup = headerInfo.catchup;\n        globalCatchupSource = headerInfo.catchupSource;\n        continue;\n      }\n\n      // 解析頻道信息\n      if (line.startsWith('#EXTINF:')) {\n        currentChannel = this.parseExtInf(line);\n        continue;\n      }\n\n      // 解析 URL\n      if (line && !line.startsWith('#') && currentChannel.name) {\n        const channel: M3UChannel = {\n          name: currentChannel.name,\n          url: line,\n          logo: currentChannel.logo,\n          group: currentChannel.group,\n          tvgId: currentChannel.tvgId,\n          tvgName: currentChannel.tvgName,\n          catchup: currentChannel.catchup || globalCatchup,\n          catchupSource: currentChannel.catchupSource || globalCatchupSource,\n        };\n\n        channels.push(channel);\n\n        if (channel.group) {\n          groups.add(channel.group);\n        }\n\n        currentChannel = {};\n      }\n    }\n\n    return {\n      channels,\n      groups: Array.from(groups).sort(),\n      epgUrl,\n      catchup: globalCatchup,\n      catchupSource: globalCatchupSource,\n    };\n  }\n\n  /**\n   * 解析 M3U 頭部信息\n   */\n  private static parseM3UHeader(line: string): {\n    epgUrl?: string;\n    catchup?: string;\n    catchupSource?: string;\n  } {\n    const result: any = {};\n\n    // 解析 x-tvg-url\n    const epgMatch = line.match(/x-tvg-url=\"([^\"]+)\"/);\n    if (epgMatch) {\n      result.epgUrl = epgMatch[1];\n    }\n\n    // 解析 catchup\n    const catchupMatch = line.match(/catchup=\"([^\"]+)\"/);\n    if (catchupMatch) {\n      result.catchup = catchupMatch[1];\n    }\n\n    // 解析 catchup-source\n    const catchupSourceMatch = line.match(/catchup-source=\"([^\"]+)\"/);\n    if (catchupSourceMatch) {\n      result.catchupSource = catchupSourceMatch[1];\n    }\n\n    return result;\n  }\n\n  /**\n   * 解析 EXTINF 行\n   */\n  private static parseExtInf(line: string): Partial<M3UChannel> {\n    const channel: Partial<M3UChannel> = {};\n\n    // 提取頻道名稱（最後的部分）\n    const nameMatch = line.match(/,(.+)$/);\n    if (nameMatch) {\n      channel.name = nameMatch[1].trim();\n    }\n\n    // 解析各種屬性\n    const attributes = [\n      { key: 'tvgId', pattern: /tvg-id=\"([^\"]+)\"/ },\n      { key: 'tvgName', pattern: /tvg-name=\"([^\"]+)\"/ },\n      { key: 'logo', pattern: /tvg-logo=\"([^\"]+)\"/ },\n      { key: 'group', pattern: /group-title=\"([^\"]+)\"/ },\n      { key: 'catchup', pattern: /catchup=\"([^\"]+)\"/ },\n      { key: 'catchupSource', pattern: /catchup-source=\"([^\"]+)\"/ },\n    ];\n\n    attributes.forEach(({ key, pattern }) => {\n      const match = line.match(pattern);\n      if (match) {\n        (channel as any)[key] = match[1];\n      }\n    });\n\n    return channel;\n  }\n\n  /**\n   * 將 M3U 播放列表轉換為 TVBox 格式的直播頻道組\n   */\n  static toTVBoxFormat(playlist: M3UPlaylist): LiveChannelGroup[] {\n    const groupMap = new Map<string, LiveChannelItem[]>();\n\n    // 按組分類頻道\n    playlist.channels.forEach((channel, index) => {\n      const groupName = channel.group || '未分類';\n\n      if (!groupMap.has(groupName)) {\n        groupMap.set(groupName, []);\n      }\n\n      const liveChannel: LiveChannelItem = {\n        channelName: channel.name,\n        channelUrls: [channel.url],\n        channelIndex: index,\n        channelLogo: channel.logo,\n        channelEpgUrl: playlist.epgUrl,\n      };\n\n      groupMap.get(groupName)!.push(liveChannel);\n    });\n\n    // 轉換為 LiveChannelGroup 數組\n    const groups: LiveChannelGroup[] = [];\n    let groupIndex = 0;\n\n    groupMap.forEach((channels, groupName) => {\n      groups.push({\n        groupName,\n        groupIndex: groupIndex++,\n        liveChannels: channels.map((channel, channelIndex) => ({\n          ...channel,\n          channelIndex,\n          groupIndex: groupIndex - 1,\n        })),\n      });\n    });\n\n    return groups;\n  }\n\n  /**\n   * 轉換 GitHub URL 為 raw 格式\n   */\n  static convertGitHubUrl(url: string): string {\n    // 如果是 GitHub blob URL，轉換為 raw URL\n    if (url.includes('github.com') && url.includes('/blob/')) {\n      return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/');\n    }\n    return url;\n  }\n\n  /**\n   * 從 URL 加載並解析 M3U 播放列表\n   */\n  static async loadFromUrl(url: string): Promise<M3UPlaylist> {\n    try {\n      // 自動轉換 GitHub URL\n      const processedUrl = this.convertGitHubUrl(url);\n\n      console.log('正在加載 M3U 播放列表:', processedUrl);\n\n      // 使用代理來避免 CORS 問題\n      const proxyUrl = `/api/proxy?url=${encodeURIComponent(processedUrl)}`;\n      const response = await fetch(proxyUrl, {\n        headers: {\n          'Accept': 'text/plain, application/x-mpegurl, */*',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const content = await response.text();\n\n      // 驗證內容是否為有效的 M3U 格式\n      if (!this.validate(content)) {\n        throw new Error('文件格式不正確，請確保是有效的 M3U 播放列表');\n      }\n\n      const playlist = this.parse(content);\n      console.log('M3U 播放列表解析成功:', {\n        頻道數量: playlist.channels.length,\n        分組數量: playlist.groups.length,\n        EPG地址: playlist.epgUrl\n      });\n\n      return playlist;\n    } catch (error) {\n      console.error('加載 M3U 播放列表失敗:', error);\n      throw new Error(`加載 M3U 播放列表失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);\n    }\n  }\n\n  /**\n   * 驗證 M3U 內容格式\n   */\n  static validate(content: string): boolean {\n    const lines = content.split('\\n').map(line => line.trim());\n\n    // 檢查是否以 #EXTM3U 開頭\n    if (!lines[0]?.startsWith('#EXTM3U')) {\n      return false;\n    }\n\n    // 檢查是否包含有效的頻道信息\n    let hasValidChannel = false;\n    for (let i = 0; i < lines.length - 1; i++) {\n      if (lines[i].startsWith('#EXTINF:') && lines[i + 1] && !lines[i + 1].startsWith('#')) {\n        hasValidChannel = true;\n        break;\n      }\n    }\n\n    return hasValidChannel;\n  }\n\n  /**\n   * 過濾頻道\n   */\n  static filterChannels(\n    playlist: M3UPlaylist,\n    filters: {\n      group?: string;\n      keyword?: string;\n      excludeGroups?: string[];\n    }\n  ): M3UPlaylist {\n    let filteredChannels = playlist.channels;\n\n    // 按組過濾\n    if (filters.group) {\n      filteredChannels = filteredChannels.filter(channel => channel.group === filters.group);\n    }\n\n    // 排除特定組\n    if (filters.excludeGroups && filters.excludeGroups.length > 0) {\n      filteredChannels = filteredChannels.filter(\n        channel => !filters.excludeGroups!.includes(channel.group || '')\n      );\n    }\n\n    // 按關鍵詞過濾\n    if (filters.keyword) {\n      const keyword = filters.keyword.toLowerCase();\n      filteredChannels = filteredChannels.filter(channel =>\n        channel.name.toLowerCase().includes(keyword)\n      );\n    }\n\n    // 重新計算組列表\n    const groups = new Set<string>();\n    filteredChannels.forEach(channel => {\n      if (channel.group) {\n        groups.add(channel.group);\n      }\n    });\n\n    return {\n      ...playlist,\n      channels: filteredChannels,\n      groups: Array.from(groups).sort(),\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAwBO,MAAM;IACX;;GAEC,GACD,OAAO,MAAM,OAAe,EAAe;QACzC,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ;QAC1E,MAAM,WAAyB,EAAE;QACjC,MAAM,SAAS,IAAI;QACnB,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,iBAAsC,CAAC;QAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,cAAc;YACd,IAAI,KAAK,UAAU,CAAC,YAAY;gBAC9B,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;gBACvC,SAAS,WAAW,MAAM;gBAC1B,gBAAgB,WAAW,OAAO;gBAClC,sBAAsB,WAAW,aAAa;gBAC9C;YACF;YAEA,SAAS;YACT,IAAI,KAAK,UAAU,CAAC,aAAa;gBAC/B,iBAAiB,IAAI,CAAC,WAAW,CAAC;gBAClC;YACF;YAEA,SAAS;YACT,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,QAAQ,eAAe,IAAI,EAAE;gBACxD,MAAM,UAAsB;oBAC1B,MAAM,eAAe,IAAI;oBACzB,KAAK;oBACL,MAAM,eAAe,IAAI;oBACzB,OAAO,eAAe,KAAK;oBAC3B,OAAO,eAAe,KAAK;oBAC3B,SAAS,eAAe,OAAO;oBAC/B,SAAS,eAAe,OAAO,IAAI;oBACnC,eAAe,eAAe,aAAa,IAAI;gBACjD;gBAEA,SAAS,IAAI,CAAC;gBAEd,IAAI,QAAQ,KAAK,EAAE;oBACjB,OAAO,GAAG,CAAC,QAAQ,KAAK;gBAC1B;gBAEA,iBAAiB,CAAC;YACpB;QACF;QAEA,OAAO;YACL;YACA,QAAQ,MAAM,IAAI,CAAC,QAAQ,IAAI;YAC/B;YACA,SAAS;YACT,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,eAAe,IAAY,EAIxC;QACA,MAAM,SAAc,CAAC;QAErB,eAAe;QACf,MAAM,WAAW,KAAK,KAAK,CAAC;QAC5B,IAAI,UAAU;YACZ,OAAO,MAAM,GAAG,QAAQ,CAAC,EAAE;QAC7B;QAEA,aAAa;QACb,MAAM,eAAe,KAAK,KAAK,CAAC;QAChC,IAAI,cAAc;YAChB,OAAO,OAAO,GAAG,YAAY,CAAC,EAAE;QAClC;QAEA,oBAAoB;QACpB,MAAM,qBAAqB,KAAK,KAAK,CAAC;QACtC,IAAI,oBAAoB;YACtB,OAAO,aAAa,GAAG,kBAAkB,CAAC,EAAE;QAC9C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,YAAY,IAAY,EAAuB;QAC5D,MAAM,UAA+B,CAAC;QAEtC,gBAAgB;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,IAAI,WAAW;YACb,QAAQ,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC,IAAI;QAClC;QAEA,SAAS;QACT,MAAM,aAAa;YACjB;gBAAE,KAAK;gBAAS,SAAS;YAAmB;YAC5C;gBAAE,KAAK;gBAAW,SAAS;YAAqB;YAChD;gBAAE,KAAK;gBAAQ,SAAS;YAAqB;YAC7C;gBAAE,KAAK;gBAAS,SAAS;YAAwB;YACjD;gBAAE,KAAK;gBAAW,SAAS;YAAoB;YAC/C;gBAAE,KAAK;gBAAiB,SAAS;YAA2B;SAC7D;QAED,WAAW,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE;YAClC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACR,OAAe,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;YAClC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,cAAc,QAAqB,EAAsB;QAC9D,MAAM,WAAW,IAAI;QAErB,SAAS;QACT,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS;YAClC,MAAM,YAAY,QAAQ,KAAK,IAAI;YAEnC,IAAI,CAAC,SAAS,GAAG,CAAC,YAAY;gBAC5B,SAAS,GAAG,CAAC,WAAW,EAAE;YAC5B;YAEA,MAAM,cAA+B;gBACnC,aAAa,QAAQ,IAAI;gBACzB,aAAa;oBAAC,QAAQ,GAAG;iBAAC;gBAC1B,cAAc;gBACd,aAAa,QAAQ,IAAI;gBACzB,eAAe,SAAS,MAAM;YAChC;YAEA,SAAS,GAAG,CAAC,WAAY,IAAI,CAAC;QAChC;QAEA,0BAA0B;QAC1B,MAAM,SAA6B,EAAE;QACrC,IAAI,aAAa;QAEjB,SAAS,OAAO,CAAC,CAAC,UAAU;YAC1B,OAAO,IAAI,CAAC;gBACV;gBACA,YAAY;gBACZ,cAAc,SAAS,GAAG,CAAC,CAAC,SAAS,eAAiB,CAAC;wBACrD,GAAG,OAAO;wBACV;wBACA,YAAY,aAAa;oBAC3B,CAAC;YACH;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,iBAAiB,GAAW,EAAU;QAC3C,kCAAkC;QAClC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,WAAW;YACxD,OAAO,IAAI,OAAO,CAAC,cAAc,6BAA6B,OAAO,CAAC,UAAU;QAClF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,YAAY,GAAW,EAAwB;QAC1D,IAAI;YACF,kBAAkB;YAClB,MAAM,eAAe,IAAI,CAAC,gBAAgB,CAAC;YAE3C,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,kBAAkB;YAClB,MAAM,WAAW,CAAC,eAAe,EAAE,mBAAmB,eAAe;YACrE,MAAM,WAAW,MAAM,MAAM,UAAU;gBACrC,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YAEnC,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;gBAC3B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC;YAC5B,QAAQ,GAAG,CAAC,iBAAiB;gBAC3B,MAAM,SAAS,QAAQ,CAAC,MAAM;gBAC9B,MAAM,SAAS,MAAM,CAAC,MAAM;gBAC5B,OAAO,SAAS,MAAM;YACxB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACrF;IACF;IAEA;;GAEC,GACD,OAAO,SAAS,OAAe,EAAW;QACxC,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAEvD,mBAAmB;QACnB,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,WAAW,YAAY;YACpC,OAAO;QACT;QAEA,gBAAgB;QAChB,IAAI,kBAAkB;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,GAAG,IAAK;YACzC,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM;gBACpF,kBAAkB;gBAClB;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,eACL,QAAqB,EACrB,OAIC,EACY;QACb,IAAI,mBAAmB,SAAS,QAAQ;QAExC,OAAO;QACP,IAAI,QAAQ,KAAK,EAAE;YACjB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,QAAQ,KAAK;QACvF;QAEA,QAAQ;QACR,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,GAAG,GAAG;YAC7D,mBAAmB,iBAAiB,MAAM,CACxC,CAAA,UAAW,CAAC,QAAQ,aAAa,CAAE,QAAQ,CAAC,QAAQ,KAAK,IAAI;QAEjE;QAEA,SAAS;QACT,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,UAAU,QAAQ,OAAO,CAAC,WAAW;YAC3C,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAExC;QAEA,UAAU;QACV,MAAM,SAAS,IAAI;QACnB,iBAAiB,OAAO,CAAC,CAAA;YACvB,IAAI,QAAQ,KAAK,EAAE;gBACjB,OAAO,GAAG,CAAC,QAAQ,KAAK;YAC1B;QACF;QAEA,OAAO;YACL,GAAG,QAAQ;YACX,UAAU;YACV,QAAQ,MAAM,IAAI,CAAC,QAAQ,IAAI;QACjC;IACF;AACF"}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/stores/live.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport type { LiveChannelGroup, LiveChannelItem } from '@/types';\nimport { M3UParser, type M3UPlaylist } from '@/lib/m3u-parser';\n\ninterface LiveState {\n  // 直播源數據\n  liveGroups: LiveChannelGroup[];\n  currentPlaylist: M3UPlaylist | null;\n  \n  // 當前播放狀態\n  currentGroup: LiveChannelGroup | null;\n  currentChannel: LiveChannelItem | null;\n  currentChannelIndex: number;\n  currentGroupIndex: number;\n  \n  // 直播源 URL 歷史\n  liveSourceUrls: string[];\n  currentLiveSourceUrl: string;\n  \n  // 過濾和搜索\n  selectedGroupName: string | null;\n  searchKeyword: string;\n  \n  // 加載狀態\n  isLoading: boolean;\n  error: string | null;\n  \n  // EPG 相關\n  epgUrl: string | null;\n  epgData: Record<string, any> | null;\n  \n  // Actions\n  loadLiveSource: (url: string) => Promise<void>;\n  setCurrentChannel: (groupIndex: number, channelIndex: number) => void;\n  nextChannel: () => void;\n  previousChannel: () => void;\n  setSelectedGroup: (groupName: string | null) => void;\n  setSearchKeyword: (keyword: string) => void;\n  addLiveSourceUrl: (url: string) => void;\n  removeLiveSourceUrl: (url: string) => void;\n  clearLiveData: () => void;\n  getFilteredGroups: () => LiveChannelGroup[];\n  getChannelByIndex: (groupIndex: number, channelIndex: number) => LiveChannelItem | null;\n}\n\nexport const useLiveStore = create<LiveState>()(\n  persist(\n    (set, get) => ({\n      // 初始狀態\n      liveGroups: [],\n      currentPlaylist: null,\n      currentGroup: null,\n      currentChannel: null,\n      currentChannelIndex: 0,\n      currentGroupIndex: 0,\n      liveSourceUrls: [],\n      currentLiveSourceUrl: '',\n      selectedGroupName: null,\n      searchKeyword: '',\n      isLoading: false,\n      error: null,\n      epgUrl: null,\n      epgData: null,\n\n      // Actions\n      loadLiveSource: async (url: string) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const playlist = await M3UParser.loadFromUrl(url);\n          const liveGroups = M3UParser.toTVBoxFormat(playlist);\n          \n          set({\n            currentPlaylist: playlist,\n            liveGroups,\n            currentLiveSourceUrl: url,\n            epgUrl: playlist.epgUrl || null,\n            currentGroupIndex: 0,\n            currentChannelIndex: 0,\n            selectedGroupName: null,\n            searchKeyword: '',\n          });\n\n          // 設置第一個頻道為當前頻道\n          if (liveGroups.length > 0 && liveGroups[0].liveChannels.length > 0) {\n            set({\n              currentGroup: liveGroups[0],\n              currentChannel: liveGroups[0].liveChannels[0],\n            });\n          }\n\n          // 添加到歷史記錄\n          get().addLiveSourceUrl(url);\n          \n        } catch (error: any) {\n          set({ \n            error: error.message || '加載直播源失敗',\n            currentPlaylist: null,\n            liveGroups: [],\n          });\n          throw error;\n        } finally {\n          set({ isLoading: false });\n        }\n      },\n\n      setCurrentChannel: (groupIndex: number, channelIndex: number) => {\n        const { liveGroups } = get();\n        \n        if (groupIndex >= 0 && groupIndex < liveGroups.length) {\n          const group = liveGroups[groupIndex];\n          \n          if (channelIndex >= 0 && channelIndex < group.liveChannels.length) {\n            const channel = group.liveChannels[channelIndex];\n            \n            set({\n              currentGroup: group,\n              currentChannel: channel,\n              currentGroupIndex: groupIndex,\n              currentChannelIndex: channelIndex,\n            });\n          }\n        }\n      },\n\n      nextChannel: () => {\n        const { currentGroupIndex, currentChannelIndex, liveGroups } = get();\n        \n        if (liveGroups.length === 0) return;\n        \n        const currentGroup = liveGroups[currentGroupIndex];\n        let nextGroupIndex = currentGroupIndex;\n        let nextChannelIndex = currentChannelIndex + 1;\n        \n        // 如果當前組的頻道已經到最後一個，切換到下一組的第一個頻道\n        if (nextChannelIndex >= currentGroup.liveChannels.length) {\n          nextGroupIndex = (currentGroupIndex + 1) % liveGroups.length;\n          nextChannelIndex = 0;\n        }\n        \n        get().setCurrentChannel(nextGroupIndex, nextChannelIndex);\n      },\n\n      previousChannel: () => {\n        const { currentGroupIndex, currentChannelIndex, liveGroups } = get();\n        \n        if (liveGroups.length === 0) return;\n        \n        let prevGroupIndex = currentGroupIndex;\n        let prevChannelIndex = currentChannelIndex - 1;\n        \n        // 如果當前組的頻道已經到第一個，切換到上一組的最後一個頻道\n        if (prevChannelIndex < 0) {\n          prevGroupIndex = currentGroupIndex === 0 ? liveGroups.length - 1 : currentGroupIndex - 1;\n          prevChannelIndex = liveGroups[prevGroupIndex].liveChannels.length - 1;\n        }\n        \n        get().setCurrentChannel(prevGroupIndex, prevChannelIndex);\n      },\n\n      setSelectedGroup: (groupName: string | null) => {\n        set({ selectedGroupName: groupName });\n      },\n\n      setSearchKeyword: (keyword: string) => {\n        set({ searchKeyword: keyword });\n      },\n\n      addLiveSourceUrl: (url: string) => {\n        set(state => {\n          const newUrls = [url, ...state.liveSourceUrls.filter(u => u !== url)].slice(0, 10);\n          return { liveSourceUrls: newUrls };\n        });\n      },\n\n      removeLiveSourceUrl: (url: string) => {\n        set(state => ({\n          liveSourceUrls: state.liveSourceUrls.filter(u => u !== url)\n        }));\n      },\n\n      clearLiveData: () => {\n        set({\n          liveGroups: [],\n          currentPlaylist: null,\n          currentGroup: null,\n          currentChannel: null,\n          currentChannelIndex: 0,\n          currentGroupIndex: 0,\n          selectedGroupName: null,\n          searchKeyword: '',\n          error: null,\n          epgUrl: null,\n          epgData: null,\n        });\n      },\n\n      getFilteredGroups: () => {\n        const { liveGroups, selectedGroupName, searchKeyword } = get();\n        \n        let filteredGroups = liveGroups;\n        \n        // 按組名過濾\n        if (selectedGroupName) {\n          filteredGroups = filteredGroups.filter(group => group.groupName === selectedGroupName);\n        }\n        \n        // 按關鍵詞過濾頻道\n        if (searchKeyword.trim()) {\n          const keyword = searchKeyword.toLowerCase();\n          filteredGroups = filteredGroups.map(group => ({\n            ...group,\n            liveChannels: group.liveChannels.filter(channel =>\n              channel.channelName.toLowerCase().includes(keyword)\n            ),\n          })).filter(group => group.liveChannels.length > 0);\n        }\n        \n        return filteredGroups;\n      },\n\n      getChannelByIndex: (groupIndex: number, channelIndex: number) => {\n        const { liveGroups } = get();\n        \n        if (groupIndex >= 0 && groupIndex < liveGroups.length) {\n          const group = liveGroups[groupIndex];\n          if (channelIndex >= 0 && channelIndex < group.liveChannels.length) {\n            return group.liveChannels[channelIndex];\n          }\n        }\n        \n        return null;\n      },\n    }),\n    {\n      name: 'tvbox-live',\n      partialize: (state) => ({\n        liveSourceUrls: state.liveSourceUrls,\n        currentLiveSourceUrl: state.currentLiveSourceUrl,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAGA;AAHA;AACA;;;;AA6CO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,YAAY,EAAE;QACd,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,qBAAqB;QACrB,mBAAmB;QACnB,gBAAgB,EAAE;QAClB,sBAAsB;QACtB,mBAAmB;QACnB,eAAe;QACf,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAS;QAET,UAAU;QACV,gBAAgB,OAAO;YACrB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,2HAAA,CAAA,YAAS,CAAC,WAAW,CAAC;gBAC7C,MAAM,aAAa,2HAAA,CAAA,YAAS,CAAC,aAAa,CAAC;gBAE3C,IAAI;oBACF,iBAAiB;oBACjB;oBACA,sBAAsB;oBACtB,QAAQ,SAAS,MAAM,IAAI;oBAC3B,mBAAmB;oBACnB,qBAAqB;oBACrB,mBAAmB;oBACnB,eAAe;gBACjB;gBAEA,eAAe;gBACf,IAAI,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;oBAClE,IAAI;wBACF,cAAc,UAAU,CAAC,EAAE;wBAC3B,gBAAgB,UAAU,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE;oBAC/C;gBACF;gBAEA,UAAU;gBACV,MAAM,gBAAgB,CAAC;YAEzB,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,iBAAiB;oBACjB,YAAY,EAAE;gBAChB;gBACA,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,mBAAmB,CAAC,YAAoB;YACtC,MAAM,EAAE,UAAU,EAAE,GAAG;YAEvB,IAAI,cAAc,KAAK,aAAa,WAAW,MAAM,EAAE;gBACrD,MAAM,QAAQ,UAAU,CAAC,WAAW;gBAEpC,IAAI,gBAAgB,KAAK,eAAe,MAAM,YAAY,CAAC,MAAM,EAAE;oBACjE,MAAM,UAAU,MAAM,YAAY,CAAC,aAAa;oBAEhD,IAAI;wBACF,cAAc;wBACd,gBAAgB;wBAChB,mBAAmB;wBACnB,qBAAqB;oBACvB;gBACF;YACF;QACF;QAEA,aAAa;YACX,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG;YAE/D,IAAI,WAAW,MAAM,KAAK,GAAG;YAE7B,MAAM,eAAe,UAAU,CAAC,kBAAkB;YAClD,IAAI,iBAAiB;YACrB,IAAI,mBAAmB,sBAAsB;YAE7C,+BAA+B;YAC/B,IAAI,oBAAoB,aAAa,YAAY,CAAC,MAAM,EAAE;gBACxD,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,WAAW,MAAM;gBAC5D,mBAAmB;YACrB;YAEA,MAAM,iBAAiB,CAAC,gBAAgB;QAC1C;QAEA,iBAAiB;YACf,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG;YAE/D,IAAI,WAAW,MAAM,KAAK,GAAG;YAE7B,IAAI,iBAAiB;YACrB,IAAI,mBAAmB,sBAAsB;YAE7C,+BAA+B;YAC/B,IAAI,mBAAmB,GAAG;gBACxB,iBAAiB,sBAAsB,IAAI,WAAW,MAAM,GAAG,IAAI,oBAAoB;gBACvF,mBAAmB,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,GAAG;YACtE;YAEA,MAAM,iBAAiB,CAAC,gBAAgB;QAC1C;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE,mBAAmB;YAAU;QACrC;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE,eAAe;YAAQ;QAC/B;QAEA,kBAAkB,CAAC;YACjB,IAAI,CAAA;gBACF,MAAM,UAAU;oBAAC;uBAAQ,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;iBAAK,CAAC,KAAK,CAAC,GAAG;gBAC/E,OAAO;oBAAE,gBAAgB;gBAAQ;YACnC;QACF;QAEA,qBAAqB,CAAC;YACpB,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;gBACzD,CAAC;QACH;QAEA,eAAe;YACb,IAAI;gBACF,YAAY,EAAE;gBACd,iBAAiB;gBACjB,cAAc;gBACd,gBAAgB;gBAChB,qBAAqB;gBACrB,mBAAmB;gBACnB,mBAAmB;gBACnB,eAAe;gBACf,OAAO;gBACP,QAAQ;gBACR,SAAS;YACX;QACF;QAEA,mBAAmB;YACjB,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;YAEzD,IAAI,iBAAiB;YAErB,QAAQ;YACR,IAAI,mBAAmB;gBACrB,iBAAiB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;YACtE;YAEA,WAAW;YACX,IAAI,cAAc,IAAI,IAAI;gBACxB,MAAM,UAAU,cAAc,WAAW;gBACzC,iBAAiB,eAAe,GAAG,CAAC,CAAA,QAAS,CAAC;wBAC5C,GAAG,KAAK;wBACR,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,UACtC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAE/C,CAAC,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,YAAY,CAAC,MAAM,GAAG;YAClD;YAEA,OAAO;QACT;QAEA,mBAAmB,CAAC,YAAoB;YACtC,MAAM,EAAE,UAAU,EAAE,GAAG;YAEvB,IAAI,cAAc,KAAK,aAAa,WAAW,MAAM,EAAE;gBACrD,MAAM,QAAQ,UAAU,CAAC,WAAW;gBACpC,IAAI,gBAAgB,KAAK,eAAe,MAAM,YAAY,CAAC,MAAM,EAAE;oBACjE,OAAO,MAAM,YAAY,CAAC,aAAa;gBACzC;YACF;YAEA,OAAO;QACT;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,gBAAgB,MAAM,cAAc;YACpC,sBAAsB,MAAM,oBAAoB;QAClD,CAAC;AACH"}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/components/live/channel-list.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport Image from 'next/image';\nimport { Play, Search, Filter, Tv, Users } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useLiveStore } from '@/stores/live';\nimport type { LiveChannelItem, LiveChannelGroup } from '@/types';\nimport { cn } from '@/lib/utils';\n\ninterface ChannelListProps {\n  onChannelSelect?: (groupIndex: number, channelIndex: number) => void;\n  className?: string;\n}\n\nconst ChannelList: React.FC<ChannelListProps> = ({\n  onChannelSelect,\n  className,\n}) => {\n  const {\n    currentChannel,\n    currentGroupIndex,\n    currentChannelIndex,\n    selectedGroupName,\n    searchKeyword,\n    setSelectedGroup,\n    setSearchKeyword,\n    setCurrentChannel,\n    getFilteredGroups,\n  } = useLiveStore();\n\n  const [showGroupFilter, setShowGroupFilter] = useState(false);\n  const filteredGroups = getFilteredGroups();\n\n  // 獲取所有組名 - 使用 useMemo 緩存結果\n  const liveGroups = useLiveStore(state => state.liveGroups);\n  const allGroups = useMemo(() =>\n    liveGroups.map(g => g.groupName),\n    [liveGroups]\n  );\n\n  const handleChannelClick = (groupIndex: number, channelIndex: number) => {\n    setCurrentChannel(groupIndex, channelIndex);\n    onChannelSelect?.(groupIndex, channelIndex);\n  };\n\n  const handleGroupFilter = (groupName: string | null) => {\n    setSelectedGroup(groupName);\n    setShowGroupFilter(false);\n  };\n\n  if (filteredGroups.length === 0) {\n    return (\n      <div className={cn(\"flex flex-col items-center justify-center py-12 text-center\", className)}>\n        <Tv className=\"h-12 w-12 text-muted-foreground mb-4\" />\n        <div className=\"text-lg font-medium mb-2\">暫無直播頻道</div>\n        <div className=\"text-sm text-muted-foreground\">\n          請先加載直播源或檢查網絡連接\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn(\"space-y-4\", className)}>\n      {/* 搜索和過濾 */}\n      <div className=\"space-y-2\">\n        <div className=\"flex space-x-2\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"搜索頻道...\"\n              value={searchKeyword}\n              onChange={(e) => setSearchKeyword(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            onClick={() => setShowGroupFilter(!showGroupFilter)}\n          >\n            <Filter className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* 組過濾器 */}\n        {showGroupFilter && (\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm flex items-center\">\n                <Users className=\"h-4 w-4 mr-2\" />\n                選擇分組\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"pt-0\">\n              <div className=\"grid grid-cols-2 gap-2\">\n                <Button\n                  variant={selectedGroupName === null ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => handleGroupFilter(null)}\n                  className=\"justify-start\"\n                >\n                  全部分組\n                </Button>\n                {allGroups.map((groupName) => (\n                  <Button\n                    key={groupName}\n                    variant={selectedGroupName === groupName ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={() => handleGroupFilter(groupName)}\n                    className=\"justify-start\"\n                  >\n                    {groupName}\n                  </Button>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n\n      {/* 頻道列表 */}\n      <div className=\"space-y-4\">\n        {filteredGroups.map((group, groupIdx) => (\n          <Card key={group.groupName}>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm flex items-center justify-between\">\n                <span className=\"flex items-center\">\n                  <Tv className=\"h-4 w-4 mr-2\" />\n                  {group.groupName}\n                </span>\n                <span className=\"text-xs text-muted-foreground\">\n                  {group.liveChannels.length} 個頻道\n                </span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"pt-0\">\n              <div className=\"grid grid-cols-1 gap-2\">\n                {group.liveChannels.map((channel, channelIdx) => {\n                  const isActive =\n                    currentChannel?.channelName === channel.channelName &&\n                    currentGroupIndex === group.groupIndex &&\n                    currentChannelIndex === channelIdx;\n\n                  return (\n                    <div\n                      key={`${channel.channelName}-${channelIdx}`}\n                      className={cn(\n                        \"flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors\",\n                        isActive\n                          ? \"bg-primary text-primary-foreground border-primary\"\n                          : \"hover:bg-accent hover:text-accent-foreground\"\n                      )}\n                      onClick={() => handleChannelClick(group.groupIndex, channelIdx)}\n                    >\n                      {/* 頻道 Logo */}\n                      <div className=\"flex-shrink-0\">\n                        {channel.channelLogo ? (\n                          <Image\n                            src={channel.channelLogo}\n                            alt={channel.channelName}\n                            width={40}\n                            height={40}\n                            className=\"rounded object-cover\"\n                            onError={(e) => {\n                              const target = e.target as HTMLImageElement;\n                              target.style.display = 'none';\n                            }}\n                          />\n                        ) : (\n                          <div className=\"w-10 h-10 bg-muted rounded flex items-center justify-center\">\n                            <Tv className=\"h-5 w-5 text-muted-foreground\" />\n                          </div>\n                        )}\n                      </div>\n\n                      {/* 頻道信息 */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"font-medium truncate\">\n                          {channel.channelName}\n                        </div>\n                        {channel.channelUrls && channel.channelUrls.length > 1 && (\n                          <div className=\"text-xs text-muted-foreground\">\n                            {channel.channelUrls.length} 個源\n                          </div>\n                        )}\n                      </div>\n\n                      {/* 播放按鈕 */}\n                      <div className=\"flex-shrink-0\">\n                        {isActive ? (\n                          <div className=\"w-8 h-8 bg-primary-foreground text-primary rounded-full flex items-center justify-center\">\n                            <Play className=\"h-4 w-4 fill-current\" />\n                          </div>\n                        ) : (\n                          <Button\n                            size=\"icon\"\n                            variant=\"ghost\"\n                            className=\"w-8 h-8\"\n                          >\n                            <Play className=\"h-4 w-4\" />\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* 統計信息 */}\n      <div className=\"text-center text-sm text-muted-foreground\">\n        共 {filteredGroups.length} 個分組，\n        {filteredGroups.reduce((total, group) => total + group.liveChannels.length, 0)} 個頻道\n      </div>\n    </div>\n  );\n};\n\nexport default ChannelList;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AANA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;;;;AAiBA,MAAM,cAA0C,CAAC,EAC/C,eAAe,EACf,SAAS,EACV;IACC,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EAClB,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,iBAAiB;IAEvB,2BAA2B;IAC3B,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,CAAA,QAAS,MAAM,UAAU;IACzD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IACxB,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS,GAC/B;QAAC;KAAW;IAGd,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,kBAAkB,YAAY;QAC9B,kBAAkB,YAAY;IAChC;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D;;8BAChF,8OAAC,8LAAA,CAAA,KAAE;oBAAC,WAAU;;;;;;8BACd,8OAAC;oBAAI,WAAU;8BAA2B;;;;;;8BAC1C,8OAAC;oBAAI,WAAU;8BAAgC;;;;;;;;;;;;IAKrD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,WAAU;;;;;;;;;;;;0CAGd,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,mBAAmB,CAAC;0CAEnC,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKrB,iCACC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAItC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,sBAAsB,OAAO,YAAY;4CAClD,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;sDACX;;;;;;wCAGA,UAAU,GAAG,CAAC,CAAC,0BACd,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,sBAAsB,YAAY,YAAY;gDACvD,MAAK;gDACL,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DAET;+CANI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBnB,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,yBAC1B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,8LAAA,CAAA,KAAE;oDAAC,WAAU;;;;;;gDACb,MAAM,SAAS;;;;;;;sDAElB,8OAAC;4CAAK,WAAU;;gDACb,MAAM,YAAY,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAIjC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS;wCAChC,MAAM,WACJ,gBAAgB,gBAAgB,QAAQ,WAAW,IACnD,sBAAsB,MAAM,UAAU,IACtC,wBAAwB;wCAE1B,qBACE,8OAAC;4CAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,sDACA;4CAEN,SAAS,IAAM,mBAAmB,MAAM,UAAU,EAAE;;8DAGpD,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,WAAW,iBAClB,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,QAAQ,WAAW;wDACxB,KAAK,QAAQ,WAAW;wDACxB,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,SAAS,CAAC;4DACR,MAAM,SAAS,EAAE,MAAM;4DACvB,OAAO,KAAK,CAAC,OAAO,GAAG;wDACzB;;;;;6EAGF,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,8LAAA,CAAA,KAAE;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAMpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,WAAW;;;;;;wDAErB,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,WAAW,CAAC,MAAM;gEAAC;;;;;;;;;;;;;8DAMlC,8OAAC;oDAAI,WAAU;8DACZ,yBACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;6EAGlB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;2CAtDjB,GAAG,QAAQ,WAAW,CAAC,CAAC,EAAE,YAAY;;;;;oCA4DjD;;;;;;;;;;;;uBAlFK,MAAM,SAAS;;;;;;;;;;0BA0F9B,8OAAC;gBAAI,WAAU;;oBAA4C;oBACtD,eAAe,MAAM;oBAAC;oBACxB,eAAe,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,YAAY,CAAC,MAAM,EAAE;oBAAG;;;;;;;;;;;;;AAIvF;uCAEe"}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1513, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TVBoxOSC-2025DIY/tvbox-web/src/app/live/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useSearchParams } from 'next/navigation';\nimport { Plus, RefreshCw, AlertCircle, Settings, List, Monitor } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport VideoPlayer from '@/components/player/video-player';\nimport ChannelList from '@/components/live/channel-list';\nimport { useLiveStore } from '@/stores/live';\nimport { useConfigStore } from '@/stores/config';\nimport { cn } from '@/lib/utils';\n\nexport default function LivePage() {\n  const searchParams = useSearchParams();\n  const { settings } = useConfigStore();\n  const {\n    liveGroups,\n    currentChannel,\n    currentLiveSourceUrl,\n    liveSourceUrls,\n    isLoading,\n    error,\n    loadLiveSource,\n    nextChannel,\n    previousChannel,\n    clearLiveData,\n  } = useLiveStore();\n\n  const [newSourceUrl, setNewSourceUrl] = useState('');\n  const [showChannelList, setShowChannelList] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n\n  // 預設的 M3U 源地址\n  const defaultM3UUrl = 'https://raw.githubusercontent.com/YanG-1989/m3u/main/Gather.m3u';\n\n  // GitHub URL 轉換函數\n  const convertGitHubUrl = (url: string): string => {\n    // 如果是 GitHub blob URL，轉換為 raw URL\n    if (url.includes('github.com') && url.includes('/blob/')) {\n      return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/');\n    }\n    return url;\n  };\n\n  // 初始化時加載源\n  useEffect(() => {\n    if (liveGroups.length === 0 && !currentLiveSourceUrl) {\n      // 優先級：URL 參數 > 配置中的 M3U URL > 默認 URL\n      const urlParam = searchParams.get('url');\n      const configM3uUrl = settings?.defaultM3uUrl;\n\n      const sourceUrl = urlParam || configM3uUrl || defaultM3UUrl;\n\n      if (sourceUrl) {\n        console.log('自動加載 M3U 源:', sourceUrl);\n        handleLoadSource(sourceUrl);\n      }\n    }\n  }, [searchParams, settings?.defaultM3uUrl]);\n\n  // 鍵盤快捷鍵\n  useEffect(() => {\n    const handleKeyPress = (e: KeyboardEvent) => {\n      if (e.target instanceof HTMLInputElement) return;\n\n      switch (e.key) {\n        case 'ArrowUp':\n          e.preventDefault();\n          previousChannel();\n          break;\n        case 'ArrowDown':\n          e.preventDefault();\n          nextChannel();\n          break;\n        case 'l':\n        case 'L':\n          setShowChannelList(!showChannelList);\n          break;\n        case 'f':\n        case 'F':\n          setIsFullscreen(!isFullscreen);\n          break;\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [showChannelList, isFullscreen, nextChannel, previousChannel]);\n\n  const handleLoadSource = async (url: string) => {\n    try {\n      // 轉換 GitHub URL\n      const convertedUrl = convertGitHubUrl(url);\n      await loadLiveSource(convertedUrl);\n      setNewSourceUrl('');\n    } catch (error) {\n      console.error('加載直播源失敗:', error);\n    }\n  };\n\n  const handleAddSource = () => {\n    if (newSourceUrl.trim()) {\n      handleLoadSource(newSourceUrl.trim());\n    }\n  };\n\n  // 如果沒有加載任何源，顯示添加源界面\n  if (liveGroups.length === 0 && !isLoading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-2xl mx-auto\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Monitor className=\"h-6 w-6\" />\n                <span>添加直播源</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">\n                  M3U 播放列表地址\n                </label>\n                <div className=\"flex space-x-2\">\n                  <Input\n                    placeholder=\"請輸入 M3U 播放列表 URL...\"\n                    value={newSourceUrl}\n                    onChange={(e) => setNewSourceUrl(e.target.value)}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Enter') {\n                        handleAddSource();\n                      }\n                    }}\n                  />\n                  <Button\n                    onClick={handleAddSource}\n                    disabled={isLoading || !newSourceUrl.trim()}\n                  >\n                    {isLoading ? (\n                      <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                    ) : (\n                      <Plus className=\"h-4 w-4\" />\n                    )}\n                  </Button>\n                </div>\n              </div>\n\n              {/* 快速添加默認源 */}\n              <div>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => handleLoadSource(defaultM3UUrl)}\n                  disabled={isLoading}\n                  className=\"w-full\"\n                >\n                  加載默認直播源\n                </Button>\n              </div>\n\n              {/* 歷史源 */}\n              {liveSourceUrls.length > 0 && (\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    歷史源\n                  </label>\n                  <div className=\"space-y-2\">\n                    {liveSourceUrls.map((url, index) => (\n                      <div key={index} className=\"flex items-center space-x-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleLoadSource(url)}\n                          disabled={isLoading}\n                          className=\"flex-1 justify-start truncate\"\n                        >\n                          {url}\n                        </Button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {error && (\n                <div className=\"flex items-center space-x-2 text-destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">{error}</span>\n                </div>\n              )}\n\n              <div className=\"text-sm text-muted-foreground\">\n                <p>支持標準的 M3U/M3U8 播放列表格式</p>\n                <p>示例: https://example.com/playlist.m3u</p>\n                <p>支持 GitHub 鏈接（會自動轉換為 raw 格式）</p>\n\n                <div className=\"mt-3 p-3 bg-muted rounded-lg\">\n                  <p className=\"font-medium mb-2\">快速測試：</p>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setNewSourceUrl('https://github.com/YanG-1989/m3u/blob/main/Gather.m3u')}\n                    className=\"w-full text-xs\"\n                  >\n                    使用示例 GitHub M3U 源\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen flex flex-col\">\n      {/* 頂部工具欄 */}\n      <div className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container mx-auto px-4 py-2\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-lg font-semibold\">直播電視</h1>\n              {currentChannel && (\n                <div className=\"text-sm text-muted-foreground\">\n                  正在播放: {currentChannel.channelName}\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowChannelList(!showChannelList)}\n              >\n                <List className=\"h-4 w-4 mr-2\" />\n                {showChannelList ? '隱藏' : '顯示'}頻道列表\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  setNewSourceUrl('');\n                  // 這裡可以打開添加源的對話框\n                }}\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                添加源\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={clearLiveData}\n              >\n                <Settings className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 主內容區域 */}\n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* 播放器區域 */}\n        <div className={cn(\n          \"flex-1 bg-black relative\",\n          !showChannelList && \"w-full\"\n        )}>\n          {currentChannel ? (\n            <VideoPlayer\n              src={currentChannel.channelUrls[0]}\n              poster={currentChannel.channelLogo}\n              autoplay={true}\n              className=\"w-full h-full\"\n              onError={(error) => {\n                console.error('播放錯誤:', error);\n                // 可以嘗試切換到下一個源或下一個頻道\n              }}\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-full text-white\">\n              <div className=\"text-center\">\n                <Monitor className=\"h-16 w-16 mx-auto mb-4 opacity-50\" />\n                <div className=\"text-lg\">請選擇頻道</div>\n              </div>\n            </div>\n          )}\n\n          {/* 頻道切換提示 */}\n          <div className=\"absolute top-4 left-4 right-4 pointer-events-none\">\n            <div className=\"bg-black/50 text-white px-4 py-2 rounded-lg opacity-0 transition-opacity\">\n              <div className=\"text-sm\">\n                使用 ↑↓ 鍵切換頻道，L 鍵切換頻道列表，F 鍵全屏\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 頻道列表側邊欄 */}\n        {showChannelList && (\n          <div className=\"w-80 border-l bg-background overflow-hidden flex flex-col\">\n            <div className=\"flex-1 overflow-y-auto p-4\">\n              <ChannelList\n                onChannelSelect={(groupIndex, channelIndex) => {\n                  // 頻道選擇已經在 ChannelList 內部處理\n                }}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 加載狀態 */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-background rounded-lg p-6 flex items-center space-x-3\">\n            <RefreshCw className=\"h-5 w-5 animate-spin\" />\n            <span>加載直播源中...</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;IAClC,MAAM,EACJ,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,cAAc,EACd,SAAS,EACT,KAAK,EACL,cAAc,EACd,WAAW,EACX,eAAe,EACf,aAAa,EACd,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,cAAc;IACd,MAAM,gBAAgB;IAEtB,kBAAkB;IAClB,MAAM,mBAAmB,CAAC;QACxB,kCAAkC;QAClC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,WAAW;YACxD,OAAO,IAAI,OAAO,CAAC,cAAc,6BAA6B,OAAO,CAAC,UAAU;QAClF;QACA,OAAO;IACT;IAEA,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,MAAM,KAAK,KAAK,CAAC,sBAAsB;YACpD,qCAAqC;YACrC,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,eAAe,UAAU;YAE/B,MAAM,YAAY,YAAY,gBAAgB;YAE9C,wCAAe;gBACb,QAAQ,GAAG,CAAC,eAAe;gBAC3B,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAc,UAAU;KAAc;IAE1C,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,CAAC;YACtB,IAAI,EAAE,MAAM,YAAY,kBAAkB;YAE1C,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,EAAE,cAAc;oBAChB;oBACA;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB;oBACA;gBACF,KAAK;gBACL,KAAK;oBACH,mBAAmB,CAAC;oBACpB;gBACF,KAAK;gBACL,KAAK;oBACH,gBAAgB,CAAC;oBACjB;YACJ;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAiB;QAAc;QAAa;KAAgB;IAEhE,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,gBAAgB;YAChB,MAAM,eAAe,iBAAiB;YACtC,MAAM,eAAe;YACrB,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC5B;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,aAAa,IAAI,IAAI;YACvB,iBAAiB,aAAa,IAAI;QACpC;IACF;IAEA,oBAAoB;IACpB,IAAI,WAAW,MAAM,KAAK,KAAK,CAAC,WAAW;QACzC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAW,CAAC;wDACV,IAAI,EAAE,GAAG,KAAK,SAAS;4DACrB;wDACF;oDACF;;;;;;8DAEF,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,aAAa,CAAC,aAAa,IAAI;8DAExC,0BACC,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;6EAErB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAOxB,8OAAC;8CACC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,iBAAiB;wCAChC,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;gCAMF,eAAe,MAAM,GAAG,mBACvB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,KAAK,sBACxB,8OAAC;oDAAgB,WAAU;8DACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,iBAAiB;wDAChC,UAAU;wDACV,WAAU;kEAET;;;;;;mDARK;;;;;;;;;;;;;;;;gCAgBjB,uBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDAEH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAmB;;;;;;8DAChC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;oCACrC,gCACC,8OAAC;wCAAI,WAAU;;4CAAgC;4CACtC,eAAe,WAAW;;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,kBAAkB,OAAO;4CAAK;;;;;;;kDAGjC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,gBAAgB;wCAChB,gBAAgB;wCAClB;;0DAEA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAInC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDAET,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,4BACA,CAAC,mBAAmB;;4BAEnB,+BACC,8OAAC,+IAAA,CAAA,UAAW;gCACV,KAAK,eAAe,WAAW,CAAC,EAAE;gCAClC,QAAQ,eAAe,WAAW;gCAClC,UAAU;gCACV,WAAU;gCACV,SAAS,CAAC;oCACR,QAAQ,KAAK,CAAC,SAAS;gCACvB,oBAAoB;gCACtB;;;;;qDAGF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAI,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAM/B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;oBAQ9B,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6IAAA,CAAA,UAAW;gCACV,iBAAiB,CAAC,YAAY;gCAC5B,2BAA2B;gCAC7B;;;;;;;;;;;;;;;;;;;;;;YAQT,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAMlB"}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}